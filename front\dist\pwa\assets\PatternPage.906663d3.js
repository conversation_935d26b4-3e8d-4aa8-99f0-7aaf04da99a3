import{k as Fe,r as v,b as Ee,c as he,w as ie,I as n,J as P,L as p,a2 as G,M as i,a3 as de,P as l,O as g,az as r,aC as F,aA as x,A as w,aB as W,bf as z,be as h,N as M,a8 as re,aD as Ae,a6 as Se}from"./index.5bec8c58.js";import{Q as B}from"./QSelect.88ca58b5.js";import{Q as qe,a as Re}from"./QItem.ca9e1076.js";import{Q as De}from"./QSpinnerDots.4c09bdd2.js";import{Q as Ve}from"./QLinearProgress.ab26c7b0.js";import{Q as Be}from"./QPage.c6f07f5b.js";import{u as Me,_ as Le}from"./IndexPage.1029fcec.js";import{Q as $e,a as Te,b as Qe,c as ze,u as Pe}from"./QTabPanels.966b0370.js";import{L as Ce}from"./lotto.5fc4e377.js";import{p as A}from"./padding.dd505b59.js";import{u as ke}from"./useLotteryAnalysis.8aefd45f.js";import{Q as Ue,a as xe,b as V,c as je}from"./QTable.860e4bc4.js";import{_ as He}from"./plugin-vue_export-helper.21dcd24c.js";import"./position-engine.c9e2754b.js";import"./selection.d62715cb.js";import"./QPopupProxy.325da3b8.js";import"./QResizeObserver.e69cd9d2.js";import"./touch.9135741d.js";import"./use-render-cache.3aae9b27.js";import"./QList.b95bf97b.js";const Oe={class:"row q-gutter-y-md"},Ne={class:"col-12 col-sm-4 draw-title text-center"},Ie={class:"text-period"},Ge={class:"text-draw-date"},We={class:"col-12 col-sm-6 self-center"},Je={class:"row justify-center"},Ze={key:0,class:"col-auto"},Ke={class:"row q-my-md q-gutter-sm items-center"},Xe={class:"col-sm-auto"},Ye={class:"col-sm-auto"},et={class:"row q-my-md q-gutter-sm items-center"},tt={class:"col-sm-auto"},lt={class:"col-sm-auto"},st={class:"row q-col-gutter-xs"},at={class:"ball tail-number"},ot={class:"row q-col-gutter-xs"},ut={class:"ball tail-number"},nt={class:"row q-col-gutter-xs"},rt={class:"ball tail-number"},it={class:"row q-gutter-sm items-center q-mb-md"},dt={class:"ball tail-number q-mx-xs"},ct={class:"row q-gutter-sm items-center q-mb-md"},mt={class:"ball tail-number q-mx-xs"},pt={class:"row q-gutter-sm items-center"},vt={class:"ball tail-number q-mx-xs"},bt={class:"text-subtitle1"},ft={class:"row justify-center"},gt={class:"row justify-center"},_t={class:"row justify-center"},wt={class:"row justify-center"},yt={class:"row justify-center"},Ct={class:"row justify-center"},xt={class:"row justify-center"},Ft={class:"row justify-center"},kt={class:"row justify-center"},Et={class:"row justify-center"},ht={class:"row no-wrap"},At={key:0,class:"text-subtitle1 ball special-number"},St={class:"row no-wrap"},qt=Fe({__name:"PatternResult",props:{isSuperLotto:{type:Boolean},drawResults:{},predictResult:{},rdResults:{},tailRdResults:{}},setup(ge){const y=ge,S=v("1"),q=ke();let k=v(new Map);const ee=[{name:"draw_date",label:"\u65E5\u671F",field:"draw_date",align:"center"},{name:"tail1",label:"\u5C3E1",field:"tail1",align:"center"},{name:"tail2",label:"\u5C3E2",field:"tail2",align:"center"},{name:"tail3",label:"\u5C3E3",field:"tail3",align:"center"},{name:"tail4",label:"\u5C3E4",field:"tail4",align:"center"},{name:"tail5",label:"\u5C3E5",field:"tail5",align:"center"},{name:"tail6",label:"\u5C3E6",field:"tail6",align:"center"},{name:"tail7",label:"\u5C3E7",field:"tail7",align:"center"},{name:"tail8",label:"\u5C3E8",field:"tail8",align:"center"},{name:"tail9",label:"\u5C3E9",field:"tail9",align:"center"},{name:"tail0",label:"\u5C3E0",field:"tail0",align:"center"},{name:"draw_results",label:"\u734E\u865F",field:"draw_results",align:"center"},{name:"tail_set",label:"\u5C3E\u6578",field:"tail_set",align:"center"}];Ee(()=>{J(),Z()});const U=v(new Map),L=v(new Map),J=()=>{if(y.tailRdResults.length===0){le.value=1;return}const a=Math.max(...y.tailRdResults.map(t=>t.consecutiveHits));le.value=Math.max(1,a)},Z=()=>{var a;k.value.clear(),N.value=[],C.value=[],o.value=[],U.value.clear();for(const t of y.drawResults){t.tails=new Map;for(let s=0;s<10;s++)t.tails.set(s,[]);let e=[...t.draw_number_size];!y.isSuperLotto&&t.special_number&&(e.push(t.special_number),e=e.sort((s,u)=>s-u));for(const s of e){const u=s%10;(a=t.tails.get(u))==null||a.push(s)}t.tailSet=q.getTailSet(t,y.isSuperLotto);for(const s of t.tailSet)U.value.set(s,(U.value.get(s)||0)+1)}U.value=X(U.value);for(const t of Array(10).keys())k.value.set(t,0);pe(),ve(),se(),fe(),k.value=X(k.value),D()},$=v(1),te=v([{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 1 \u6B21",value:1},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 2 \u6B21",value:2},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 3 \u6B21",value:3},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 4 \u6B21",value:4},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 5 \u6B21",value:5}]),T=v(1),Q=v("above"),ce=v([{label:"(\u542B)\u4EE5\u4E0A",value:"above"},{label:"(\u542B)\u4EE5\u4E0B",value:"below"},{label:"\u525B\u597D",value:"exact"}]),le=v(1),me=he(()=>Array.from({length:le.value},(a,t)=>({label:`\u5DF2\u9023\u7E8C\u62D6\u51FA ${t+1} \u6B21`,value:t+1}))),j=v("above"),ae=v([{label:"(\u542B)\u4EE5\u4E0A",value:"above"},{label:"(\u542B)\u4EE5\u4E0B",value:"below"},{label:"\u525B\u597D",value:"exact"}]);ie([$,Q,T,j],()=>{Z()}),ie(()=>y.tailRdResults,()=>{J()});const pe=()=>{const a=v([]);L.value.clear(),a.value=y.rdResults.filter(u=>{switch(Q.value){case"above":return u.consecutiveHits>=$.value;case"below":return u.consecutiveHits<=$.value;case"exact":return u.consecutiveHits===$.value;default:return u.consecutiveHits>=$.value}});const t=new Map,e=H(a.value);let s=0;for(const u of a.value){let b=new Set;for(const d of u.targetNumbers){const E=d%10,Y=u.consecutiveHits,ne=oe(u,e.get(E)||0);t.set(E,(t.get(E)||0)+Y+ne),s+=u.consecutiveHits,b.add(E)}for(const d of b)L.value.set(d,(L.value.get(d)||0)+1)}L.value=X(L.value);for(const u of t.keys()){const b=t.get(u)||0;K(u,b/s)}},R=v(new Map),ve=()=>{const a=v([]);R.value.clear(),a.value=y.tailRdResults.filter(s=>{switch(j.value){case"above":return s.consecutiveHits>=T.value;case"below":return s.consecutiveHits<=T.value;case"exact":return s.consecutiveHits===T.value;default:return s.consecutiveHits>=T.value}});const t=H(a.value);let e=0;for(const s of a.value)for(const u of s.targetNumbers){const b=s.consecutiveHits,d=oe(s,t.get(u)||0);R.value.set(u,(R.value.get(u)||0)+b+d),e+=s.consecutiveHits}R.value=X(R.value);for(const s of R.value.keys()){const u=R.value.get(s)||0;K(s,u/e)}},H=a=>{const t=new Map,e=new Map;for(const s of a)for(const u of s.targetNumbers){const b=u%10;t.set(b,(t.get(b)||0)+u),e.set(b,(e.get(b)||0)+1)}for(const s of t.keys()){const u=e.get(s)||1;t.set(s,Number((t.get(s)||0)/u))}return t},oe=(a,t)=>(a.consecutiveHits+5*t)/(a.consecutiveHits+5),se=()=>{const a=y.drawResults,t=a.slice(-10),e=O(a),s=I(t),u=new Map;e.forEach((d,E)=>{const Y=s.get(E)||0;u.set(E,(d+Y)/2)});const b=ue(u);for(const d of u.keys()){const E=b.get(d)||0;K(d,E)}},O=a=>{const t=new Map;a.forEach(u=>{if(u.draw_number_size.forEach(b=>{const d=b%10;t.set(d,(t.get(d)||0)+1)}),!y.isSuperLotto&&u.special_number){const b=u.special_number%10;t.set(b,(t.get(b)||0)+1)}});const e=Math.max(...t.values()),s=new Map;return t.forEach((u,b)=>s.set(b,u/e)),s},I=a=>{const t=new Map,e=new Map;for(let u=1;u<a.length;u++){const b=a[u-1].tailSet,d=a[u].tailSet;!b||!d||b.length===0||d.length===0||b.forEach(E=>{t.set(E,(t.get(E)||0)+1),d.includes(E)&&e.set(E,(e.get(E)||0)+1)})}const s=new Map;return t.forEach((u,b)=>{const d=e.get(b)||0;s.set(b,d/u)}),s},ue=a=>{const t=Array.from(a.values()),e=Math.min(...t),s=Math.max(...t),u=new Map;return a.forEach((b,d)=>u.set(d,s===e?0:(b-e)/(s-e))),u},K=(a,t)=>{k.value.set(a,(k.value.get(a)||0)+t)},X=a=>{const t=Array.from(a.entries());return t.sort((e,s)=>s[1]-e[1]),new Map(t)},be=v(3),fe=()=>{for(const a of k.value.keys()){const t=k.value.get(a)||0;k.value.set(a,Number((t/be.value*100).toFixed(1)))}},N=v([]),C=v([]),o=v([]),D=()=>{N.value=[],C.value=[],o.value=[];const a=Array.from(L.value.entries()).map(s=>s[0]).slice(0,5),t=Array.from(R.value.entries()).map(s=>s[0]).slice(0,5),e=Array.from(k.value.entries()).map(s=>s[0]).slice(0,5);for(const s of a)t.includes(s)&&N.value.push(s);for(const s of a)e.includes(s)&&C.value.push(s);for(const s of t)e.includes(s)&&o.value.push(s);N.value.sort((s,u)=>s===0?1:u===0?-1:s-u),C.value.sort((s,u)=>s===0?1:u===0?-1:s-u),o.value.sort((s,u)=>s===0?1:u===0?-1:s-u)},m=a=>a&&Array.from(a).length===0?"#f8d7da":"",_=a=>{var t,e;return(e=(t=y.predictResult)==null?void 0:t.tailSet)!=null&&e.length?y.predictResult.tailSet.includes(a):!1};return(a,t)=>(n(),P(G,{class:"q-mt-md"},{default:p(()=>[i(de,null,{default:p(()=>[i($e,{modelValue:S.value,"onUpdate:modelValue":t[0]||(t[0]=e=>S.value=e),dense:"",align:"justify",class:"text-h6","active-color":"primary","indicator-color":"primary"},{default:p(()=>[i(Te,{name:"1",label:"\u7248\u8DEF\u5206\u6790"})]),_:1},8,["modelValue"]),i(Qe,{modelValue:S.value,"onUpdate:modelValue":t[5]||(t[5]=e=>S.value=e)},{default:p(()=>[i(ze,{name:"1"},{default:p(()=>[a.predictResult.period?(n(),P(G,{key:0,bordered:"",class:"ball-card full-width q-my-lg"},{default:p(()=>[i(de,null,{default:p(()=>[l("div",Oe,[l("div",Ne,[t[6]||(t[6]=l("div",{class:"text-h6"},"\u9810\u6E2C\u958B\u734E\u7D50\u679C",-1)),l("div",Ie," \u7B2C "+g(a.predictResult.period)+" \u671F ",1),l("div",Ge," \u958B\u734E\u65E5\u671F\uFF1A"+g(a.predictResult.draw_date),1)]),l("div",We,[l("div",Je,[(n(!0),r(x,null,F(a.predictResult.draw_number_size,e=>(n(),r("div",{key:e,class:"col-auto"},[(n(),r("div",{class:"ball",key:e},g(w(A)(e)),1))]))),128)),a.predictResult.special_number?(n(),r("div",Ze,[(n(),r("div",{class:"ball special-number",key:a.predictResult.special_number},g(w(A)(a.predictResult.special_number)),1))])):W("",!0)])])])]),_:1})]),_:1})):W("",!0),l("div",Ke,[t[7]||(t[7]=l("div",{class:"col-12 text-h6"},"\u7248\u8DEF\u5206\u6790\u7BE9\u9078",-1)),l("div",Xe,[i(B,{outlined:"",dense:"",modelValue:$.value,"onUpdate:modelValue":t[1]||(t[1]=e=>$.value=e),options:te.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])]),l("div",Ye,[i(B,{outlined:"",dense:"",modelValue:Q.value,"onUpdate:modelValue":t[2]||(t[2]=e=>Q.value=e),options:ce.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])])]),l("div",et,[t[8]||(t[8]=l("div",{class:"col-12 text-h6"},"\u5C3E\u6578\u5206\u6790\u7BE9\u9078",-1)),l("div",tt,[i(B,{outlined:"",dense:"",modelValue:T.value,"onUpdate:modelValue":t[3]||(t[3]=e=>T.value=e),options:me.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])]),l("div",lt,[i(B,{outlined:"",dense:"",modelValue:j.value,"onUpdate:modelValue":t[4]||(t[4]=e=>j.value=e),options:ae.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])])]),t[22]||(t[22]=l("div",{class:"row q-mb-sm"},[l("label",{class:"col text-h6 text-bold"}," \u62D6\u724C\uFF1A\u5C3E\u6578\u9810\u6E2C\u958B\u51FA\u6A5F\u7387 (\u7531\u9AD8\u81F3\u4F4E\u6392\u5E8F) ")],-1)),i(G,{flat:"",bordered:"",class:"q-mb-lg q-pa-sm appearance"},{default:p(()=>[l("div",st,[(n(!0),r(x,null,F(L.value.entries(),([e])=>(n(),r("div",{class:h(["col-4 col-md-2 text-h6",{predict:_(e)}]),key:e},[l("span",at,g(e),1)],2))),128))])]),_:1}),t[23]||(t[23]=l("div",{class:"row q-mb-sm"},[l("label",{class:"col text-h6 text-bold"}," \u5C3E\u6578\uFF1A\u9810\u6E2C\u958B\u51FA\u6A5F\u7387 (\u7531\u9AD8\u81F3\u4F4E\u6392\u5E8F) ")],-1)),i(G,{flat:"",bordered:"",class:"q-mb-lg q-pa-sm appearance"},{default:p(()=>[l("div",ot,[(n(!0),r(x,null,F(R.value.entries(),([e])=>(n(),r("div",{class:h(["col-4 col-md-2 text-h6",{predict:_(e)}]),key:e},[l("span",ut,g(e),1)],2))),128))])]),_:1}),t[24]||(t[24]=l("div",{class:"row q-mb-sm"},[l("label",{class:"col text-h6 text-bold"}," \u7248\u8DEF\uFF1A\u5C3E\u6578\u9810\u6E2C\u958B\u51FA\u6A5F\u7387\uFF08\u7531\u9AD8\u81F3\u4F4E\u6392\u5E8F\uFF09 ")],-1)),i(G,{flat:"",bordered:"",class:"q-mb-lg q-pa-sm appearance"},{default:p(()=>[l("div",nt,[(n(!0),r(x,null,F(w(k).entries(),([e])=>(n(),r("div",{class:h(["col-4 col-md-2 text-h6",{predict:_(e)}]),key:e},[l("span",rt,g(e),1)],2))),128))])]),_:1}),t[25]||(t[25]=l("div",{class:"row q-mb-sm"},[l("label",{class:"col text-h6 text-bold"}," \u7D9C\u5408\u6BD4\u5C0D\u9810\u6E2C\u5C3E\u6578 ")],-1)),i(G,{flat:"",bordered:"",class:"q-mb-lg q-pa-sm appearance"},{default:p(()=>[l("div",it,[t[9]||(t[9]=l("span",{class:"text-h6"},"\u62D6\u724C+\u5C3E\u6578\uFF1A",-1)),(n(!0),r(x,null,F(N.value.entries(),([,e])=>(n(),r("span",{key:e,class:h({predict:_(e)})},[l("span",dt,g(e),1)],2))),128))]),l("div",ct,[t[10]||(t[10]=l("span",{class:"text-h6"},"\u62D6\u724C+\u7248\u8DEF\uFF1A",-1)),(n(!0),r(x,null,F(C.value.entries(),([,e])=>(n(),r("span",{key:e,class:h({predict:_(e)})},[l("span",mt,g(e),1)],2))),128))]),l("div",pt,[t[11]||(t[11]=l("span",{class:"text-h6"},"\u5C3E\u6578+\u7248\u8DEF\uFF1A",-1)),(n(!0),r(x,null,F(o.value.entries(),([,e])=>(n(),r("span",{key:e,class:h({predict:_(e)})},[l("span",vt,g(e),1)],2))),128))])]),_:1}),i(Ue,{rows:a.drawResults,columns:ee,"rows-per-page-options":[0],"hide-bottom":"","hide-pagination":"","virtual-scroll":"",separator:"cell",class:"sticky-virtscroll-table q-mt-lg"},{header:p(e=>[i(xe,{props:e,class:"bg-primary text-white"},{default:p(()=>[(n(!0),r(x,null,F(e.cols,s=>(n(),P(je,{key:s.name,props:e},{default:p(()=>[M(g(s.label),1)]),_:2},1032,["props"]))),128))]),_:2},1032,["props"])]),body:p(e=>[i(xe,{props:e},{default:p(()=>{var s,u,b,d,E,Y,ne,_e,we,ye;return[i(V,{key:"draw_date",props:e},{default:p(()=>[l("div",bt,g(e.row.draw_date),1)]),_:2},1032,["props"]),i(V,{key:"tail1",props:e,class:"fixed-col",style:z({backgroundColor:m((s=e.row.tails)==null?void 0:s.get(1))})},{default:p(()=>{var f;return[l("div",ft,[(n(!0),r(x,null,F((f=e.row.tails)==null?void 0:f.get(1),c=>(n(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!a.isSuperLotto}])},[M(g(w(A)(c))+" ",1),t[12]||(t[12]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(V,{key:"tail2",props:e,class:"fixed-col",style:z({backgroundColor:m((u=e.row.tails)==null?void 0:u.get(2))})},{default:p(()=>{var f;return[l("div",gt,[(n(!0),r(x,null,F((f=e.row.tails)==null?void 0:f.get(2),c=>(n(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!a.isSuperLotto}])},[M(g(w(A)(c)),1),t[13]||(t[13]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(V,{key:"tail3",props:e,class:"fixed-col",style:z({backgroundColor:m((b=e.row.tails)==null?void 0:b.get(3))})},{default:p(()=>{var f;return[l("div",_t,[(n(!0),r(x,null,F((f=e.row.tails)==null?void 0:f.get(3),c=>(n(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!a.isSuperLotto}])},[M(g(w(A)(c)),1),t[14]||(t[14]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(V,{key:"tail4",props:e,class:"fixed-col",style:z({backgroundColor:m((d=e.row.tails)==null?void 0:d.get(4))})},{default:p(()=>{var f;return[l("div",wt,[(n(!0),r(x,null,F((f=e.row.tails)==null?void 0:f.get(4),c=>(n(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!a.isSuperLotto}])},[M(g(w(A)(c)),1),t[15]||(t[15]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(V,{key:"tail5",props:e,class:"fixed-col",style:z({backgroundColor:m((E=e.row.tails)==null?void 0:E.get(5))})},{default:p(()=>{var f;return[l("div",yt,[(n(!0),r(x,null,F((f=e.row.tails)==null?void 0:f.get(5),c=>(n(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!a.isSuperLotto}])},[M(g(w(A)(c)),1),t[16]||(t[16]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(V,{key:"tail6",props:e,class:"fixed-col",style:z({backgroundColor:m((Y=e.row.tails)==null?void 0:Y.get(6))})},{default:p(()=>{var f;return[l("div",Ct,[(n(!0),r(x,null,F((f=e.row.tails)==null?void 0:f.get(6),c=>(n(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!a.isSuperLotto}])},[M(g(w(A)(c)),1),t[17]||(t[17]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(V,{key:"tail7",props:e,class:"fixed-col",style:z({backgroundColor:m((ne=e.row.tails)==null?void 0:ne.get(7))})},{default:p(()=>{var f;return[l("div",xt,[(n(!0),r(x,null,F((f=e.row.tails)==null?void 0:f.get(7),c=>(n(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!a.isSuperLotto}])},[M(g(w(A)(c)),1),t[18]||(t[18]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(V,{key:"tail8",props:e,class:"fixed-col",style:z({backgroundColor:m((_e=e.row.tails)==null?void 0:_e.get(8))})},{default:p(()=>{var f;return[l("div",Ft,[(n(!0),r(x,null,F((f=e.row.tails)==null?void 0:f.get(8),c=>(n(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!a.isSuperLotto}])},[M(g(w(A)(c)),1),t[19]||(t[19]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(V,{key:"tail9",props:e,class:"fixed-col",style:z({backgroundColor:m((we=e.row.tails)==null?void 0:we.get(9))})},{default:p(()=>{var f;return[l("div",kt,[(n(!0),r(x,null,F((f=e.row.tails)==null?void 0:f.get(9),c=>(n(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!a.isSuperLotto}])},[M(g(w(A)(c)),1),t[20]||(t[20]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(V,{key:"tail0",props:e,class:"fixed-col",style:z({backgroundColor:m((ye=e.row.tails)==null?void 0:ye.get(0))})},{default:p(()=>{var f;return[l("div",Et,[(n(!0),r(x,null,F((f=e.row.tails)==null?void 0:f.get(0),c=>(n(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!a.isSuperLotto}])},[M(g(w(A)(c)),1),t[21]||(t[21]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(V,{key:"draw_results",props:e},{default:p(()=>[l("div",ht,[(n(!0),r(x,null,F(e.row.draw_number_size,f=>(n(),r("span",{key:f,class:"text-subtitle1 ball"},g(w(A)(f)),1))),128)),e.row.special_number?(n(),r("span",At,g(w(A)(e.row.special_number)),1)):W("",!0)])]),_:2},1032,["props"]),i(V,{key:"tail_set",props:e},{default:p(()=>[l("div",St,[(n(!0),r(x,null,F(e.row.tailSet,f=>(n(),r("span",{key:f,class:"text-subtitle1 ball tail-number"},g(f),1))),128))])]),_:2},1032,["props"])]}),_:2},1032,["props"])]),_:1},8,["rows"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}))}});var Rt=He(qt,[["__scopeId","data-v-11e42d34"]]);const Dt={class:"row lto-ref q-mb-sm"},Vt={class:"col-12 col-sm-4 self-center text-h6"},Bt={class:"col-12 col-sm-6 self-center text-subtitle1"},Mt={class:"row balls"},Lt={class:"ball"},$t={key:0,class:"col-auto"},Tt={class:"row q-mb-md"},Qt={class:"col"},zt={key:1,class:"row q-mb-md"},Pt={class:"row q-mb-md"},Ut={class:"col-12 col-sm-4 q-pa-sm"},jt={class:"col-12 col-sm-4 q-pa-sm"},Ht={class:"col-12 col-sm-4 q-pa-sm"},Ot={class:"row q-mb-md"},Nt={class:"col-12 col-sm-4 q-pa-sm"},It={class:"col-12 col-sm-4 q-pa-sm"},Gt={class:"col-12 col-sm-4 q-pa-sm"},Wt={class:"row q-mb-md"},Jt={class:"col-12 col-sm-4"},Zt={class:"q-pa-sm"},Kt={class:"col-12 col-sm-4"},Xt={class:"q-pa-sm"},Yt={class:"col-12 col-sm-4"},el={class:"q-pa-sm"},tl={class:"text-center q-mb-sm"},xl=Fe({__name:"PatternPage",setup(ge){const y=Me(),S=v(y.getLotto),q=Pe(),k=ke(),ee=v(1),U=v(1),L=v(1),J=v(1),Z=v(1),$=v(1),te=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3}],T=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3},{label:"\u56DB\u661F\u7D44\u5408",value:4},{label:"\u4E94\u661F\u7D44\u5408",value:5}],Q=v(!1),ce=()=>{Q.value=!0},le=()=>{Q.value=!1},me=()=>{Q.value=!1};ie(()=>y.getLotto,C=>{C&&(S.value=C)}),ie(()=>{var C;return(C=S.value)==null?void 0:C.period},()=>{O.value=[]});const j=v(50);let ae=v(Array.from({length:991},(C,o)=>({label:`${o+10}\u671F`,value:o+10})));const pe=(C,o,D)=>{const m=parseInt(C,10);(m<10||m>1e3)&&D(),o(()=>{ae.value=Array.from({length:991},(_,a)=>a+10).filter(_=>_.toString().startsWith(C)).map(_=>({label:`${_.toString()}\u671F`,value:_}))})},R=v(20),ve=Array.from({length:21},(C,o)=>({label:`${o+10}\u671F`,value:o+10})),H=v(1),oe=Array.from({length:15},(C,o)=>({label:`\u4E0B${o+1}\u671F`,value:o+1})),se=v(!1),O=v([]),I=v({period:"",draw_date:"",draw_number_appear:[],draw_number_size:[],tails:new Map}),ue=v([]),K=v([]),X=async()=>{var C,o,D;try{q.startCalculating(),se.value=y.isSuperLotto;const m=await Ce.getLottoList({draw_type:y.getDrawType,date_end:(o=(C=S.value)==null?void 0:C.draw_date)!=null?o:"",limit:j.value});O.value=m.data.reverse();const _=await Ce.getLottoPredict({draw_type:y.getDrawType,draw_date:(D=y.getLotto)==null?void 0:D.draw_date,ahead_count:H.value});I.value=_.data,I.value.period&&(I.value.tailSet=k.getTailSet(I.value,se.value));const a=await be();ue.value=a.data;const t=await fe();K.value=t.data}catch(m){console.error("\u8A08\u7B97\u932F\u8AA4:",m)}finally{N()}},be=()=>{const C=O.value.map(m=>{const _=[...m.draw_number_size];return m.special_number&&!y.isSuperLotto&&_.push(m.special_number),{numbers:[..._],period:String(m.period)}});k.setResults(C),k.setConfig({firstGroupSize:ee.value,secondGroupSize:U.value,targetGroupSize:L.value,maxRange:R.value,lookAheadCount:H.value});let o=Date.now();const D=8;return k.analyzeWithProgress(async m=>{const _=Date.now();_-o>=D&&(await q.updateProgress(m),o=_)},m=>{q.addWarning(m)})},fe=()=>{const C=O.value.map(m=>{const _=new Set;for(let t of m.draw_number_size)_.add(t%10);!y.isSuperLotto&&m.special_number&&_.add(m.special_number%10);const a=Array.from(_).sort((t,e)=>t===0?1:e===0?-1:t-e);return{period:String(m.period),numbers:[...a]}});k.init({firstGroupSize:J.value,secondGroupSize:Z.value,targetGroupSize:$.value,maxRange:R.value,lookAheadCount:H.value},C);let o=Date.now();const D=8;return k.analyzeWithProgress(async m=>{const _=Date.now();_-o>=D&&(await q.updateProgress(m),o=_)},m=>{q.addWarning(m)})},N=()=>{q.stopCalculating(),k.stopAnalyzer()};return(C,o)=>(n(),P(Be,{class:"justify-center"},{default:p(()=>[i(G,null,{default:p(()=>[i(de,null,{default:p(()=>{var D,m,_,a,t,e,s,u,b;return[(D=w(y).getLotto)!=null&&D.draw_date?(n(),r(x,{key:0},[l("div",Dt,[l("div",Vt,[l("div",null,g(w(y).getDrawLabel),1),o[9]||(o[9]=l("span",null,"\u53C3\u8003\u671F\u865F\uFF1A",-1)),l("span",null,g((m=S.value)==null?void 0:m.period),1),l("span",null,"\uFF08"+g((_=S.value)==null?void 0:_.draw_date)+"\uFF09",1)]),l("div",Bt,[l("div",Mt,[(n(!0),r(x,null,F((a=S.value)==null?void 0:a.draw_number_size,d=>(n(),r("div",{class:"col-auto",key:d},[l("div",Lt,g(w(A)(d)),1)]))),128)),(t=S.value)!=null&&t.special_number?(n(),r("div",$t,[(n(),r("div",{class:"ball special-number",key:(e=S.value)==null?void 0:e.special_number},g(w(A)((s=S.value)==null?void 0:s.special_number)),1))])):W("",!0)])])]),l("div",Tt,[l("div",Qt,[Q.value?(n(),P(re,{key:1,type:"button",label:"\u53D6\u6D88\u9078\u64C7",color:"negative",class:"text-h6 q-ml-md",onClick:me})):(n(),P(re,{key:0,type:"button",label:"\u91CD\u65B0\u9078\u64C7",color:"primary",class:"text-h6 q-ml-md",onClick:ce}))])])],64)):(n(),r("div",zt,o[10]||(o[10]=[l("div",{class:"text-h6"},"\u203B\u8ACB\u9078\u64C7\u53C3\u8003\u671F\u865F",-1)]))),i(Ae,{class:"q-mb-md"}),!Q.value&&((u=w(y).getLotto)==null?void 0:u.draw_date)?(n(),r(x,{key:2},[o[17]||(o[17]=l("div",{class:"row q-mb-md"},[l("div",{class:"col-12 text-h5 text-weight-bolder text-center"}," \u7D9C\u5408\u5206\u6790\u8A2D\u5B9A ")],-1)),l("div",Pt,[o[11]||(o[11]=l("div",{class:"col-12 text-h6 text-weight-bold"},"\u734E\u865F\u62D6\u724C\u7D44\u5408",-1)),l("div",Ut,[i(B,{outlined:"",dense:"",modelValue:ee.value,"onUpdate:modelValue":o[0]||(o[0]=d=>ee.value=d),options:te,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",jt,[i(B,{outlined:"",dense:"",modelValue:U.value,"onUpdate:modelValue":o[1]||(o[1]=d=>U.value=d),options:te,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",Ht,[i(B,{outlined:"",dense:"",modelValue:L.value,"onUpdate:modelValue":o[2]||(o[2]=d=>L.value=d),options:te,"emit-value":"","map-options":""},null,8,["modelValue"])])]),l("div",Ot,[o[12]||(o[12]=l("div",{class:"col-12 text-h6 text-weight-bold"},"\u5C3E\u6578\u62D6\u724C\u7D44\u5408",-1)),l("div",Nt,[i(B,{outlined:"",dense:"",modelValue:J.value,"onUpdate:modelValue":o[3]||(o[3]=d=>J.value=d),options:T,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",It,[i(B,{outlined:"",dense:"",modelValue:Z.value,"onUpdate:modelValue":o[4]||(o[4]=d=>Z.value=d),options:T,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",Gt,[i(B,{outlined:"",dense:"",modelValue:$.value,"onUpdate:modelValue":o[5]||(o[5]=d=>$.value=d),options:T,"emit-value":"","map-options":""},null,8,["modelValue"])])]),l("div",Wt,[l("div",Jt,[o[14]||(o[14]=l("div",{class:"text-h6 text-weight-bold"},"\u63A8\u7B97\u671F\u6578",-1)),l("div",Zt,[i(B,{outlined:"",dense:"",modelValue:j.value,"onUpdate:modelValue":o[6]||(o[6]=d=>j.value=d),options:w(ae),"input-debounce":"0","use-input":"","hide-selected":"","fill-input":"",onFilter:pe,"emit-value":"","map-options":""},{"no-option":p(()=>[i(qe,null,{default:p(()=>[i(Re,{class:"text-grey"},{default:p(()=>o[13]||(o[13]=[M(" \u7121\u53EF\u7528\u9078\u9805 ")])),_:1})]),_:1})]),_:1},8,["modelValue","options"])])]),l("div",Kt,[o[15]||(o[15]=l("div",{class:"text-h6 text-weight-bold"},"\u6700\u5927\u5340\u9593",-1)),l("div",Xt,[i(B,{outlined:"",dense:"",modelValue:R.value,"onUpdate:modelValue":o[7]||(o[7]=d=>R.value=d),options:w(ve),"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),l("div",Yt,[o[16]||(o[16]=l("div",{class:"text-h6 text-weight-bold"},"\u9810\u6E2C\u671F\u6578",-1)),l("div",el,[i(B,{outlined:"",dense:"",modelValue:H.value,"onUpdate:modelValue":o[8]||(o[8]=d=>H.value=d),options:w(oe),"emit-value":"","map-options":""},null,8,["modelValue","options"])])])]),i(Se,{align:"right",class:"q-my-lg q-py-none q-px-md"},{default:p(()=>[w(q).isCalculating?(n(),P(re,{key:0,type:"button",label:"\u4E2D\u65B7\u8A08\u7B97",color:"negative",class:"text-h6 q-mr-md",onClick:N})):W("",!0),i(re,{type:"button",label:"\u958B\u59CB\u8A08\u7B97",color:"positive",class:"text-h6 q-px-lg q-py-sm",onClick:X,loading:w(q).isCalculating},{loading:p(()=>[i(De)]),_:1},8,["loading"])]),_:1})],64)):(n(),P(Le,{key:3,"draw-type-query":w(y).drawType,"date-query":((b=S.value)==null?void 0:b.draw_date)||"","is-select-ref":!0,onSelectRef:le},null,8,["draw-type-query","date-query"]))]}),_:1}),w(q).isCalculating?(n(),P(de,{key:0},{default:p(()=>[l("div",tl,g(w(q).progressMessage),1),i(Ve,{rounded:"",size:"md",value:w(q).progress,"animation-speed":50,color:"primary",class:"q-mb-xs"},null,8,["value"])]),_:1})):W("",!0)]),_:1}),!w(q).isCalculating&&O.value.length>0?(n(),P(Rt,{key:0,"is-super-lotto":se.value,"draw-results":O.value,"predict-result":I.value,"rd-results":ue.value,"tail-rd-results":K.value},null,8,["is-super-lotto","draw-results","predict-result","rd-results","tail-rd-results"])):W("",!0)]),_:1}))}});export{xl as default};
