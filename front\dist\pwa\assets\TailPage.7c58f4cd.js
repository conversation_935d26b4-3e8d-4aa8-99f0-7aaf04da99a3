import{k as be,r as _,c as ke,b as Ce,w as oe,I as l,J as x,L as n,a2 as X,M as i,a3 as ee,P as e,O as o,az as a,aC as g,aA as v,A as m,aB as $,bm as De,N as y,be as D,a8 as ie,b6 as Fe,bf as Ee,a5 as qe,aD as xe,a6 as Be}from"./index.5bec8c58.js";import{Q as Y,b as ne}from"./QSelect.88ca58b5.js";import{Q as Ae,a as Pe}from"./QItem.ca9e1076.js";import{Q as Ve}from"./QSpinnerDots.4c09bdd2.js";import{Q as $e}from"./QLinearProgress.ab26c7b0.js";import{Q as Se}from"./QPage.c6f07f5b.js";import{Q as Ne,a as ve,b as Re,c as fe,u as Le}from"./QTabPanels.966b0370.js";import{u as he,_ as Qe}from"./IndexPage.1029fcec.js";import{L as _e}from"./lotto.5fc4e377.js";import{u as ze}from"./useLotteryAnalysis.8aefd45f.js";import{p as k}from"./padding.dd505b59.js";import{Q as ye}from"./QPagination.0db9a4e5.js";import{Q as Te,a as we,b as I,c as Me}from"./QTable.860e4bc4.js";import{u as Ue}from"./use-quasar.c22dc7d5.js";import{Q as Ie}from"./QSpace.5cb73d14.js";import{_ as He}from"./plugin-vue_export-helper.21dcd24c.js";import"./position-engine.c9e2754b.js";import"./selection.d62715cb.js";import"./QResizeObserver.e69cd9d2.js";import"./touch.9135741d.js";import"./use-render-cache.3aae9b27.js";import"./QPopupProxy.325da3b8.js";import"./QList.b95bf97b.js";const Oe={class:"row q-gutter-y-md"},je={class:"col-12 col-sm-4 draw-title text-center"},Ge={class:"text-period"},We={class:"text-draw-date"},Je={class:"col-12 col-sm-6 self-center"},Ze={class:"row justify-center"},Ke={key:0,class:"col-auto"},Xe={class:"row q-my-md q-gutter-sm items-center"},Ye={class:"col-sm-auto"},et={class:"col-sm-auto"},tt={class:"col-sm-auto"},lt={class:"row q-my-md"},st={class:"col-auto text-h6"},ut={class:"row q-col-gutter-sm"},at={class:"row items-center"},ot={class:"ball tail-number"},rt={key:0,class:"row q-my-md justify-center"},it={class:"row q-col-gutter-md"},nt={key:0,class:"col-12 q-my-sm"},dt={class:"row q-col-gutter-md items-center text-h6"},ct={class:"col-12 col-sm-4"},mt={class:"col-12 col-sm-6"},pt={class:"row q-col-gutter-md"},vt={class:"col-auto"},ft={class:"col-12 col-sm-2 text-center"},bt={key:1,class:"row justify-center"},gt={class:"row q-my-md"},_t={class:"row q-my-sm"},yt={class:"col text-h6 text-bold"},wt={class:"row q-gutter-xs"},kt={class:"row items-center"},ht={class:"text-h6"},Ct={class:"row justify-center"},Dt={key:0,class:"col-auto"},Ft={class:"row items-center"},Et={class:"col-12 q-mb-sm text-h6 text-center"},qt={class:"col-12 col"},xt={class:"row justify-center"},Bt={key:0,class:"col-auto"},At={class:"col-12 q-mt-md"},Pt={class:"row q-gutter-xs justify-center"},Vt={class:"text-subtitle1",style:{"border-bottom":"1px solid black"}},$t=be({__name:"TailFollowResult",props:{isSuperLotto:{type:Boolean},drawResults:{},predictResult:{},drawTailResults:{},rdResults:{},occurrenceResults:{},pageSize:{}},emits:["view-detail"],setup(ce){const h=ce,A=!Ue().platform.is.desktop,Q=_("1"),S=_(1),E=_({pageItems:[],targetNumAppearances:new Map,totalCount:0,totalPages:0}),C=_(1),de=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3},{label:"\u56DB\u661F\u7D44\u5408",value:4},{label:"\u4E94\u661F\u7D44\u5408",value:5}],te=_(new Map),d=_(1),b=_(1),P=ke(()=>Array.from({length:b.value},(r,u)=>({label:`\u5DF2\u9023\u7E8C\u62D6\u51FA ${u+1} \u6B21`,value:u+1}))),z=_("count"),N=_("above"),J=_([{label:"(\u542B)\u4EE5\u4E0A",value:"above"},{label:"(\u542B)\u4EE5\u4E0B",value:"below"},{label:"\u525B\u597D",value:"exact"}]),T=_([{label:"\u6E96\u78BA\u6B21\u6578\u7D71\u8A08",value:"count"},{label:"\u9810\u6E2C\u7D44\u6578\u7D71\u8A08",value:"group"}]),le=[{name:"period",label:"\u671F\u6578",field:"period",align:"center"},{name:"draw_number_size",label:"\u958B\u734E\u865F\u78BC",field:"draw_number_size",align:"center",format:r=>r.join(" ")},{name:"tail1",label:"\u5C3E1",field:"tail1",align:"center"},{name:"tail2",label:"\u5C3E2",field:"tail2",align:"center"},{name:"tail3",label:"\u5C3E3",field:"tail3",align:"center"},{name:"tail4",label:"\u5C3E4",field:"tail4",align:"center"},{name:"tail5",label:"\u5C3E5",field:"tail5",align:"center"},{name:"tail6",label:"\u5C3E6",field:"tail6",align:"center"},{name:"tail7",label:"\u5C3E7",field:"tail7",align:"center"},{name:"tail8",label:"\u5C3E8",field:"tail8",align:"center"},{name:"tail9",label:"\u5C3E9",field:"tail9",align:"center"},{name:"tail0",label:"\u5C3E0",field:"tail0",align:"center"}],R=_(!1);Ce(()=>{R.value=!1,O(),j(),G(),R.value=!0}),oe(()=>d.value,()=>{!R.value||G()}),oe(()=>N.value,()=>{!R.value||G()}),oe(()=>z.value,()=>{!R.value||G()}),oe(()=>S.value,()=>{!R.value||V()}),oe(()=>C.value,()=>{p()});const p=()=>{const r=new Map;for(const u of h.drawTailResults){const t=Z(u.numbers,C.value),s=Array.from(t);for(const U of s){let w="";for(let L=0;L<U.length;L++)w+=`${U[L]}`,L<U.length-1&&(w+=", ");const c=r.get(w);c?r.set(w,c+1):r.set(w,1)}const f=Array.from(r.entries()).sort((U,w)=>w[1]-U[1]);te.value=new Map(f)}},F=r=>{const u=Z(h.predictResult.tailSet||[],C.value),t=Array.from(u),s=[];for(const f of t){let U="";for(let w=0;w<f.length;w++)U+=`${f[w]}`,w<f.length-1&&(U+=", ");s.push(U)}return s.includes(r)};function Z(r,u){return H(r,u)}function*H(r,u,t=0,s=[]){if(s.length===u){yield[...s];return}for(let f=t;f<r.length;f++)s.push(r[f]),yield*H(r,u,f+1,s),s.pop()}const O=()=>{var r;S.value=1,C.value=1,p();for(let u of h.drawResults){u.tails=new Map;for(let s=0;s<10;s++)u.tails.set(s,[]);let t=[...u.draw_number_size];!h.isSuperLotto&&u.special_number&&(t.push(u.special_number),t=t.sort((s,f)=>s-f));for(const s of t){const f=s%10;(r=u.tails.get(f))==null||r.push(s)}}},M=_([]),j=()=>{b.value=1;for(const r of h.rdResults)r.consecutiveHits>b.value&&(b.value=r.consecutiveHits)},G=()=>{M.value=[],S.value=1;const r=new Map;for(const t of h.rdResults){let s=!1;switch(N.value){case"above":s=t.consecutiveHits>=d.value;break;case"below":s=t.consecutiveHits<=d.value;break;case"exact":s=t.consecutiveHits===d.value;break;default:s=t.consecutiveHits>=d.value}if(s){if(M.value.push(t),z.value==="count")for(const f of t.targetNumbers){const U=t.consecutiveHits;r.set(f,(r.get(f)||0)+U)}else if(z.value==="group")for(const f of t.targetNumbers)r.set(f,(r.get(f)||0)+1)}}const u=Array.from(r.entries()).sort((t,s)=>s[1]-t[1]);E.value.targetNumAppearances=new Map(u),V()},V=()=>{const r=(S.value-1)*h.pageSize,u=r+h.pageSize;E.value.pageItems=M.value.slice(r,u),E.value.totalCount=M.value.length,E.value.totalPages=Math.ceil(M.value.length/h.pageSize)};return(r,u)=>(l(),x(X,{class:"q-mt-md"},{default:n(()=>[i(ee,null,{default:n(()=>[i(Ne,{modelValue:Q.value,"onUpdate:modelValue":u[0]||(u[0]=t=>Q.value=t),dense:"",align:"justify",class:"text-h6","active-color":"primary","indicator-color":"primary"},{default:n(()=>[i(ve,{name:"1",label:"\u5206\u6790\u7D50\u679C"}),i(ve,{name:"2",label:"\u7D44\u5408\u7D71\u8A08\u7D50\u679C"}),i(ve,{name:"3",label:"\u958B\u734E\u7D50\u679C"})]),_:1},8,["modelValue"]),r.predictResult.period?(l(),x(X,{key:0,bordered:"",class:"ball-card full-width q-my-lg"},{default:n(()=>[i(ee,null,{default:n(()=>[e("div",Oe,[e("div",je,[u[8]||(u[8]=e("div",{class:"text-h6"},"\u9810\u6E2C\u958B\u734E\u7D50\u679C",-1)),e("div",Ge,"\u7B2C "+o(r.predictResult.period)+" \u671F",1),e("div",We," \u958B\u734E\u65E5\u671F\uFF1A"+o(r.predictResult.draw_date),1)]),e("div",Je,[e("div",Ze,[(l(!0),a(v,null,g(r.predictResult.draw_number_size,t=>(l(),a("div",{key:t,class:"col-auto"},[(l(),a("div",{class:"ball",key:t},o(m(k)(t)),1))]))),128)),r.predictResult.special_number?(l(),a("div",Ke,[(l(),a("div",{class:"ball special-number",key:r.predictResult.special_number},o(m(k)(r.predictResult.special_number)),1))])):$("",!0)])])])]),_:1})]),_:1})):$("",!0),i(Re,{modelValue:Q.value,"onUpdate:modelValue":u[7]||(u[7]=t=>Q.value=t)},{default:n(()=>[i(fe,{name:"1"},{default:n(()=>[e("div",Xe,[u[9]||(u[9]=e("div",{class:"col-sm-auto text-h6"},"\u7BE9\u9078",-1)),e("div",Ye,[i(Y,{outlined:"",dense:"",modelValue:d.value,"onUpdate:modelValue":u[1]||(u[1]=t=>d.value=t),options:P.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])]),e("div",et,[i(Y,{outlined:"",dense:"",modelValue:N.value,"onUpdate:modelValue":u[2]||(u[2]=t=>N.value=t),options:J.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])]),e("div",tt,[i(Y,{outlined:"",dense:"",modelValue:z.value,"onUpdate:modelValue":u[3]||(u[3]=t=>z.value=t),options:T.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])])]),e("div",lt,[e("div",st," \u5171 "+o(E.value.totalCount)+" \u7B46\u8CC7\u6599 ",1)]),u[12]||(u[12]=e("div",{class:"row q-my-sm"},[e("label",{class:"col text-h6 text-bold"},"\u9810\u6E2C\u7D50\u679C")],-1)),i(X,{flat:"",bordered:"",class:"q-pa-sm q-mb-md text-subtitle1 appearance"},{default:n(()=>[e("div",ut,[(l(!0),a(v,null,g(E.value.targetNumAppearances.keys(),t=>{var s;return l(),a("div",{class:D(["col-4 col-md-2",{predict:(s=r.predictResult.tailSet)==null?void 0:s.includes(t)}]),key:t},[e("div",at,[e("span",ot,o(t),1),y(" ("+o(E.value.targetNumAppearances.get(t))+"\u6B21) ",1)])],2)}),128))])]),_:1}),E.value.totalPages>1?(l(),a("div",rt,[i(ye,{modelValue:S.value,"onUpdate:modelValue":u[4]||(u[4]=t=>S.value=t),max:E.value.totalPages,input:!0},null,8,["modelValue","max"])])):$("",!0),e("div",it,[E.value.pageItems.length===0?(l(),a("div",nt,[i(X,{flat:"",bordered:""},{default:n(()=>[i(ee,null,{default:n(()=>u[10]||(u[10]=[e("div",{class:"text-h6 text-center"},"\u6C92\u6709\u7B26\u5408\u689D\u4EF6\u7684\u7D50\u679C",-1)])),_:1})]),_:1})])):$("",!0),(l(!0),a(v,null,g(E.value.pageItems,(t,s)=>(l(),a("div",{key:s,class:"col-12 q-my-sm"},[i(X,{flat:"",bordered:""},{default:n(()=>[i(ee,null,{default:n(()=>[e("div",dt,[e("div",ct,[e("div",null,[u[11]||(u[11]=y(" \u958B\u51FA ")),(l(!0),a(v,null,g(t.firstNumbers,f=>(l(),x(ne,{key:f,color:"primary","text-color":"white",size:"lg",dense:""},{default:n(()=>[y(o(f)+"\u5C3E ",1)]),_:2},1024))),128)),y(" \u4E0B"+o(t.gap)+"\u671F\u958B ",1),(l(!0),a(v,null,g(t.secondNumbers,f=>(l(),x(ne,{key:f,color:"secondary","text-color":"white",size:"lg",dense:""},{default:n(()=>[y(o(f)+"\u5C3E ",1)]),_:2},1024))),128)),y(" \u518D\u4E0B"+o(t.targetGap)+"\u671F\u9810\u6E2C\u62D6\u51FA ",1),(l(!0),a(v,null,g(t.targetNumbers,f=>(l(),x(ne,{key:f,color:"accent","text-color":"white",size:"lg",dense:""},{default:n(()=>[y(o(f)+"\u5C3E ",1)]),_:2},1024))),128))])]),e("div",mt,[e("div",pt,[e("div",vt," \u5DF2\u9023\u7E8C\u62D6\u51FA"+o(t.consecutiveHits)+"\u6B21 ",1)])]),e("div",ft,[i(ie,{dense:"",color:"primary",icon:"visibility",label:"\u6AA2\u8996\u8A73\u60C5",class:"text-subtitle1 text-bold",onClick:f=>r.$emit("view-detail",t)},null,8,["onClick"])])])]),_:2},1024)]),_:2},1024)]))),128))]),E.value.totalPages>1?(l(),a("div",bt,[i(ye,{modelValue:S.value,"onUpdate:modelValue":u[5]||(u[5]=t=>S.value=t),max:E.value.totalPages,input:!0},null,8,["modelValue","max"])])):$("",!0)]),_:1}),i(fe,{name:"2"},{default:n(()=>[e("div",gt,[u[13]||(u[13]=e("span",{class:"text-h6 q-mr-sm"}," \u5C3E\u6578\u7D44\u5408\uFF1A ",-1)),i(Y,{modelValue:C.value,"onUpdate:modelValue":u[6]||(u[6]=t=>C.value=t),options:de,"map-options":"","emit-value":"",outlined:"",dense:""},null,8,["modelValue"])]),e("div",_t,[e("label",yt," \u5C3E\u6578\u7D44\u5408\u51FA\u73FE\u6B21\u6578 ("+o(r.drawResults.length)+"\u671F) ",1)]),i(X,{flat:"",bordered:"",class:"q-pa-sm q-mb-md text-subtitle1 appearance"},{default:n(()=>[e("div",wt,[(l(!0),a(v,null,g(te.value.keys(),t=>(l(),a("div",{class:D(["col-4 col-md-2",{predict:F(t)}]),key:t},[e("div",kt,[(l(!0),a(v,null,g(t.split(","),s=>(l(),a("span",{class:"ball tail-number",key:s},o(s),1))),128)),y(" ("+o(te.value.get(t))+"\u6B21) ",1)])],2))),128))])]),_:1})]),_:1}),i(fe,{name:"3"},{default:n(()=>[i(Te,{rows:r.drawResults,columns:le,"rows-per-page-options":[0],"hide-bottom":"",separator:"cell",class:"q-mt-lg"},De({header:n(t=>[A?$("",!0):(l(),x(we,{key:0,props:t,class:"bg-primary text-white"},{default:n(()=>[(l(!0),a(v,null,g(t.cols,s=>(l(),x(Me,{key:s.name,props:t},{default:n(()=>[y(o(s.label),1)]),_:2},1032,["props"]))),128))]),_:2},1032,["props"]))]),_:2},[A?{name:"body",fn:n(t=>[(l(),x(X,{square:"",bordered:"",key:t.row.period},{default:n(()=>[i(ee,{class:"q-px-none q-py-sm"},{default:n(()=>[e("div",Ft,[e("div",Et,[e("span",null,o(t.row.period)+"\u671F ",1),u[25]||(u[25]=e("span",null," | ",-1)),e("span",null,o(t.row.drawDate),1)]),e("div",qt,[e("div",xt,[(l(!0),a(v,null,g(t.row.draw_number_size,s=>(l(),a("div",{key:s,class:"col-auto"},[(l(),a("div",{class:"ball",key:s},o(m(k)(s)),1))]))),128)),t.row.special_number?(l(),a("div",Bt,[(l(),a("div",{class:"ball special-number",key:t.row.special_number},o(m(k)(t.row.special_number)),1))])):$("",!0)])]),e("div",At,[e("div",Pt,[(l(),a(v,null,g([1,2,3,4,5,6,7,8,9,0],s=>e("div",{class:"col-1 text-center",style:{border:"1px solid black"},key:s},[e("div",Vt," \u5C3E"+o(s),1),e("div",null,[(l(!0),a(v,null,g(t.row.tails.get(s),f=>(l(),a("span",{key:f,class:D(["text-h6",{"text-negative":f===t.row.special_number&&!r.isSuperLotto}])},[y(o(m(k)(f)),1),u[26]||(u[26]=e("br",null,null,-1))],2))),128))])])),64))])])])]),_:2},1024)]),_:2},1024))]),key:"1"}:{name:"body",fn:n(t=>[i(we,{props:t},{default:n(()=>[i(I,{key:"period",props:t},{default:n(()=>[e("div",ht,[y(o(t.row.period)+" ",1),u[14]||(u[14]=e("br",null,null,-1)),y(" "+o(t.row.draw_date),1)])]),_:2},1032,["props"]),i(I,{key:"draw_number_size",props:t},{default:n(()=>[e("div",Ct,[(l(!0),a(v,null,g(t.row.draw_number_size,s=>(l(),a("div",{key:s,class:"col-auto"},[(l(),a("div",{class:"ball",key:s},o(m(k)(s)),1))]))),128)),t.row.special_number?(l(),a("div",Dt,[(l(),a("div",{class:"ball special-number",key:t.row.special_number},o(m(k)(t.row.special_number)),1))])):$("",!0)])]),_:2},1032,["props"]),i(I,{key:"tail1",props:t},{default:n(()=>[(l(!0),a(v,null,g(t.row.tails.get(1),s=>(l(),a("span",{key:s,class:D(["text-h6",{"text-negative":s===t.row.special_number&&!r.isSuperLotto}])},[y(o(m(k)(s))+" ",1),u[15]||(u[15]=e("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),i(I,{key:"tail2",props:t},{default:n(()=>[(l(!0),a(v,null,g(t.row.tails.get(2),s=>(l(),a("span",{key:s,class:D(["text-h6",{"text-negative":s===t.row.special_number&&!r.isSuperLotto}])},[y(o(m(k)(s)),1),u[16]||(u[16]=e("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),i(I,{key:"tail3",props:t},{default:n(()=>[(l(!0),a(v,null,g(t.row.tails.get(3),s=>(l(),a("span",{key:s,class:D(["text-h6",{"text-negative":s===t.row.special_number&&!r.isSuperLotto}])},[y(o(m(k)(s)),1),u[17]||(u[17]=e("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),i(I,{key:"tail4",props:t},{default:n(()=>[(l(!0),a(v,null,g(t.row.tails.get(4),s=>(l(),a("span",{key:s,class:D(["text-h6",{"text-negative":s===t.row.special_number&&!r.isSuperLotto}])},[y(o(m(k)(s)),1),u[18]||(u[18]=e("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),i(I,{key:"tail5",props:t},{default:n(()=>[(l(!0),a(v,null,g(t.row.tails.get(5),s=>(l(),a("span",{key:s,class:D(["text-h6",{"text-negative":s===t.row.special_number&&!r.isSuperLotto}])},[y(o(m(k)(s)),1),u[19]||(u[19]=e("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),i(I,{key:"tail6",props:t},{default:n(()=>[(l(!0),a(v,null,g(t.row.tails.get(6),s=>(l(),a("span",{key:s,class:D(["text-h6",{"text-negative":s===t.row.special_number&&!r.isSuperLotto}])},[y(o(m(k)(s)),1),u[20]||(u[20]=e("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),i(I,{key:"tail7",props:t},{default:n(()=>[(l(!0),a(v,null,g(t.row.tails.get(7),s=>(l(),a("span",{key:s,class:D(["text-h6",{"text-negative":s===t.row.special_number&&!r.isSuperLotto}])},[y(o(m(k)(s)),1),u[21]||(u[21]=e("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),i(I,{key:"tail8",props:t},{default:n(()=>[(l(!0),a(v,null,g(t.row.tails.get(8),s=>(l(),a("span",{key:s,class:D(["text-h6",{"text-negative":s===t.row.special_number&&!r.isSuperLotto}])},[y(o(m(k)(s)),1),u[22]||(u[22]=e("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),i(I,{key:"tail9",props:t},{default:n(()=>[(l(!0),a(v,null,g(t.row.tails.get(9),s=>(l(),a("span",{key:s,class:D(["text-h6",{"text-negative":s===t.row.special_number&&!r.isSuperLotto}])},[y(o(m(k)(s)),1),u[23]||(u[23]=e("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),i(I,{key:"tail0",props:t},{default:n(()=>[(l(!0),a(v,null,g(t.row.tails.get(0),s=>(l(),a("span",{key:s,class:D(["text-h6",{"text-negative":s===t.row.special_number&&!r.isSuperLotto}])},[y(o(m(k)(s)),1),u[24]||(u[24]=e("br",null,null,-1))],2))),128))]),_:2},1032,["props"])]),_:2},1032,["props"])]),key:"0"}]),1032,["rows"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}))}});const St={class:"text-h6 q-mb-md"},Nt={class:"text-h6 q-mb-md"},Rt={class:"row text-h6 q-mb-md"},Lt={class:"col-auto"},Qt={class:"row balls"},zt={key:0,class:"col-auto"},Tt={key:0,class:"row text-h6 q-mb-md"},Mt={class:"col-auto"},Ut={class:"row balls"},It={key:0,class:"col-auto"},Ht={key:1,class:"row text-h6"},Ot={class:"col-auto"},jt={class:"row balls"},Gt={key:0,class:"col-auto"},Wt={key:2,class:"predict-section"},Jt={class:"row text-h6 q-mt-sm"},Zt={class:"col-auto"},Kt={key:0,class:"predict-period"},Xt={class:"predict-date"},Yt={class:"row balls"},el={key:0,class:"col-auto"},tl={key:1,class:"col-auto pending-draw"},ll=be({__name:"TailFollowDetail",props:{modelValue:{type:Boolean},results:{},selectedDetail:{},occurrences:{},predictResult:{}},emits:["update:modelValue"],setup(ce,{emit:h}){const B=ce,A=he(),Q=ke({get:()=>B.modelValue,set:d=>S("update:modelValue",d)}),S=h,E=d=>{if(!d)return"";const b=B.results.find(P=>P.period==d);return b==null?void 0:b.draw_date},C=d=>{if(!d)return{numbers:[],specialNumber:0};const b=B.results.find(P=>P.period==d);return{numbers:b==null?void 0:b.draw_number_size,specialNumber:b!=null&&b.special_number?Number(b.special_number):0}},de=d=>{var le,R,p;if(!d)return[];const b=d.firstNumbers.join(",")+"-"+d.secondNumbers.join(",")+"-"+d.gap+"-"+d.targetGap,P=(R=(le=B.occurrences.get(b))==null?void 0:le.periods)!=null?R:[],z=P.filter(F=>F.targetPeriod===void 0||F.targetPeriod===null||F.targetPeriod.trim()===""),N=P.filter(F=>F.targetPeriod!==void 0&&F.targetPeriod!==null&&F.targetPeriod.trim()!==""),J=(p=d.consecutiveHits)!=null?p:0;let T=[];return J>0&&N.length>0&&(T=N.sort((Z,H)=>{const O=parseInt(Z.targetPeriod),M=parseInt(H.targetPeriod);return isNaN(O)||isNaN(M)?Z.targetPeriod.localeCompare(H.targetPeriod):M-O}).slice(0,J)),z.length>0&&(T=[...z,...T]),T},te=d=>d.targetPeriod===void 0;return(d,b)=>(l(),x(Fe,{modelValue:Q.value,"onUpdate:modelValue":b[1]||(b[1]=P=>Q.value=P)},{default:n(()=>[i(X,{style:{"max-width":"100%",width:"800px"}},{default:n(()=>[i(ee,{class:"row items-center"},{default:n(()=>[b[2]||(b[2]=e("div",{class:"text-h6"},"\u8A73\u7D30\u8CC7\u6599",-1)),i(Ie),i(ie,{icon:"close",flat:"",round:"",dense:"",onClick:b[0]||(b[0]=P=>Q.value=!1)})]),_:1}),i(ee,{class:"q-pa-md"},{default:n(()=>{var P,z,N,J,T,le,R;return[e("div",St,[b[3]||(b[3]=y(" \u958B\u51FA ")),(l(!0),a(v,null,g((P=d.selectedDetail)==null?void 0:P.firstNumbers,p=>(l(),x(ne,{key:p,color:"primary","text-color":"white",class:"text-h6"},{default:n(()=>[y(o(p)+"\u5C3E ",1)]),_:2},1024))),128)),y(" \u4E0B"+o((z=d.selectedDetail)==null?void 0:z.gap)+"\u671F\u958B ",1),(l(!0),a(v,null,g((N=d.selectedDetail)==null?void 0:N.secondNumbers,p=>(l(),x(ne,{key:p,color:"secondary","text-color":"white",class:"text-h6"},{default:n(()=>[y(o(p)+"\u5C3E ",1)]),_:2},1024))),128)),y(" \u518D\u4E0B "+o((J=d.selectedDetail)==null?void 0:J.targetGap)+" \u671F\u9810\u6E2C\u62D6\u51FA ",1),(l(!0),a(v,null,g((T=d.selectedDetail)==null?void 0:T.targetNumbers,p=>(l(),x(ne,{key:p,color:"accent","text-color":"white",class:"text-h6"},{default:n(()=>[y(o(p)+"\u5C3E ",1)]),_:2},1024))),128))]),e("div",Nt," \u5DF2\u9023\u7E8C\u62D6\u51FA"+o((R=(le=d.selectedDetail)==null?void 0:le.consecutiveHits)!=null?R:0)+"\u6B21 ",1),e("div",null,[(l(!0),a(v,null,g(de(d.selectedDetail),(p,F)=>(l(),x(X,{key:F,class:"q-mb-md",style:Ee({"background-color":te(p)?"#e8f5e8":"#fefefe"})},{default:n(()=>[i(ee,null,{default:n(()=>{var Z,H,O,M,j,G;return[e("div",Rt,[e("div",Lt,[e("div",null,"\u7B2C "+o(p.firstPeriod)+" \u671F",1),e("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+o(E(p.firstPeriod)),1)]),e("div",Qt,[(l(!0),a(v,null,g(C(p.firstPeriod).numbers,(V,r)=>{var u;return l(),a("div",{class:"col-auto",key:r},[e("div",{class:D(["ball",{"first-num":(u=d.selectedDetail)==null?void 0:u.firstNumbers.includes(V%10)}])},o(m(k)(V)),3)])}),128)),C(p.firstPeriod).specialNumber?(l(),a("div",zt,[e("div",{class:D(["ball special-number",{"first-num":((Z=d.selectedDetail)==null?void 0:Z.firstNumbers.includes(C(p.firstPeriod).specialNumber%10))&&!m(A).isSuperLotto}])},o(m(k)(C(p.firstPeriod).specialNumber)),3)])):$("",!0)])]),p.secondPeriod?(l(),a("div",Tt,[e("div",Mt,[e("div",null,"\u7B2C "+o(p.secondPeriod)+" \u671F",1),e("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+o((H=E(p.secondPeriod))!=null?H:"\u672A\u958B\u734E"),1)]),e("div",Ut,[(l(!0),a(v,null,g(C(p.secondPeriod).numbers,(V,r)=>{var u;return l(),a("div",{class:"col-auto",key:r},[e("div",{class:D(["ball",{"second-num":(u=d.selectedDetail)==null?void 0:u.secondNumbers.includes(V%10)}])},o(m(k)(V)),3)])}),128)),C(p.secondPeriod).specialNumber?(l(),a("div",It,[e("div",{class:D(["ball special-number",{"second-num":((O=d.selectedDetail)==null?void 0:O.secondNumbers.includes(C(p.secondPeriod).specialNumber%10))&&!m(A).isSuperLotto}])},o(m(k)(C(p.secondPeriod).specialNumber)),3)])):$("",!0)])])):$("",!0),p.targetPeriod?(l(),a("div",Ht,[e("div",Ot,[e("div",null,"\u7B2C "+o(p.targetPeriod)+" \u671F",1),e("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+o((M=E(p.targetPeriod))!=null?M:"\u672A\u958B\u734E"),1)]),e("div",jt,[(l(!0),a(v,null,g(C(p.targetPeriod).numbers,(V,r)=>{var u;return l(),a("div",{class:"col-auto",key:r},[e("div",{class:D(["ball",{"target-num":(u=d.selectedDetail)==null?void 0:u.targetNumbers.includes(V%10)}])},o(m(k)(V)),3)])}),128)),C(p.targetPeriod).specialNumber?(l(),a("div",Gt,[e("div",{class:D(["ball special-number",{"target-num":((j=d.selectedDetail)==null?void 0:j.targetNumbers.includes(C(p.targetPeriod).specialNumber%10))&&!m(A).isSuperLotto}])},o(m(k)(C(p.targetPeriod).specialNumber)),3)])):$("",!0)])])):(l(),a("div",Wt,[e("div",Jt,[e("div",Zt,[d.predictResult.period?(l(),a("div",Kt," \u7B2C "+o(d.predictResult.period)+" \u671F ",1)):$("",!0),e("div",Xt,[d.predictResult.period?(l(),a(v,{key:0},[y(" \u5BE6\u969B\u958B\u734E\u65E5\u671F\uFF1A"+o(d.predictResult.draw_date),1)],64)):(l(),a(v,{key:1},[y(" \u9810\u6E2C\u671F\u865F\uFF1A\u5C1A\u672A\u958B\u734E ")],64))])]),e("div",Yt,[d.predictResult.period?(l(),a(v,{key:0},[(l(!0),a(v,null,g(d.predictResult.draw_number_size,(V,r)=>{var u;return l(),a("div",{class:"col-auto",key:r},[e("div",{class:D(["ball",{"target-num":(u=d.selectedDetail)==null?void 0:u.targetNumbers.includes(V%10)}])},o(m(k)(V)),3)])}),128)),d.predictResult.special_number?(l(),a("div",el,[e("div",{class:D(["ball special-number",{"target-num":((G=d.selectedDetail)==null?void 0:G.targetNumbers.includes(d.predictResult.special_number%10))&&!m(A).isSuperLotto}])},o(m(k)(d.predictResult.special_number)),3)])):$("",!0)],64)):(l(),a("div",tl,[i(qe,{name:"schedule",size:"lg"}),b[4]||(b[4]=e("span",null,"\u5C1A\u672A\u958B\u734E",-1))]))])])]))]}),_:2},1024)]),_:2},1032,["style"]))),128))])]}),_:1})]),_:1})]),_:1},8,["modelValue"]))}});var sl=He(ll,[["__scopeId","data-v-164dfad4"]]);const ul={class:"row lto-ref q-mb-sm"},al={class:"col-12 col-sm-4 self-center text-h6"},ol={class:"col-12 col-sm-6 self-center text-subtitle1"},rl={class:"row balls"},il={class:"ball"},nl={key:0,class:"col-auto"},dl={class:"row q-mb-md"},cl={class:"col"},ml={key:1,class:"row q-mb-md"},pl={class:"row q-mb-md"},vl={class:"col-12 col-sm-4 q-pa-sm"},fl={class:"col-12 col-sm-4 q-pa-sm"},bl={class:"col-12 col-sm-4 q-pa-sm"},gl={class:"row q-mb-md"},_l={class:"col-12 col-sm-4"},yl={class:"q-pa-sm"},wl={class:"col-12 col-sm-4"},kl={class:"q-pa-sm"},hl={class:"col-12 col-sm-4"},Cl={class:"q-pa-sm"},Dl={class:"text-center q-mb-sm"},Wl=be({__name:"TailPage",setup(ce){const h=Le(),B=he(),A=_(B.getLotto),Q=ze(),S=_(!1),E=()=>{S.value=!0},C=()=>{S.value=!1},de=()=>{S.value=!1};oe(()=>B.getLotto,w=>{w&&(A.value=w)}),oe(()=>{var w;return(w=A.value)==null?void 0:w.period},()=>{F.value=[]});const te=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3},{label:"\u56DB\u661F\u7D44\u5408",value:4},{label:"\u4E94\u661F\u7D44\u5408",value:5}],d=_(1),b=_(1),P=_(1),z=Array.from({length:21},(w,c)=>({label:`${c+10}\u671F`,value:c+10})),N=_(20);let J=_(Array.from({length:991},(w,c)=>({label:`${c+10}\u671F`,value:c+10})));const T=_(50),le=(w,c,L)=>{const se=parseInt(w,10);(se<10||se>1e3)&&L(),c(()=>{J.value=Array.from({length:991},(K,ae)=>ae+10).filter(K=>K.toString().startsWith(w)).map(K=>({label:`${K.toString()}\u671F`,value:K}))})},R=Array.from({length:15},(w,c)=>({label:`\u4E0B${c+1}\u671F`,value:c+1})),p=_(1),F=_([]),Z=_("super_lotto638"),H=_([]),O=_(new Map),M=_(new Map),j=_({period:"",draw_date:"",draw_number_appear:[],draw_number_size:[],tails:new Map}),G=_([]),V=_(!1),r=async()=>{var w,c,L;try{Z.value=B.drawType,V.value=B.isSuperLotto,h.startCalculating(),u();const se=await _e.getLottoList({draw_type:B.getDrawType,date_end:(c=(w=A.value)==null?void 0:w.draw_date)!=null?c:"",limit:T.value});F.value=se.data;const K=await _e.getLottoPredict({draw_type:B.getDrawType,draw_date:(L=B.getLotto)==null?void 0:L.draw_date,ahead_count:p.value});j.value=K.data,j.value.period&&(j.value.tailSet=Q.getTailSet(j.value,V.value)),G.value=F.value.map(W=>{const ue=new Set;for(let q of W.draw_number_size)ue.add(q%10);W.special_number&&!B.isSuperLotto&&ue.add(W.special_number%10);const pe=Array.from(ue).sort((q,ge)=>q===0?1:ge===0?-1:q-ge);return{period:String(W.period),numbers:[...pe]}}).reverse(),Q.init({firstGroupSize:d.value,secondGroupSize:b.value,targetGroupSize:P.value,maxRange:N.value,lookAheadCount:p.value},G.value);let ae=Date.now();const me=8,re=await Q.analyzeWithProgress(async W=>{const ue=Date.now();ue-ae>=me&&(await h.updateProgress(W),ae=ue)},W=>{h.addWarning(W)});H.value=re.data,O.value=re.occurrences,M.value=re.matchData}catch(se){console.error(se)}finally{t()}},u=()=>{F.value=[]},t=()=>{Q.stopAnalyzer(),h.stopCalculating()},s=_(!1),f=_(null),U=w=>{f.value=w,s.value=!0};return(w,c)=>(l(),x(Se,{class:"justify-center"},{default:n(()=>[i(X,{class:"q-mx-auto q-py-sm"},{default:n(()=>[i(ee,null,{default:n(()=>{var L,se,K,ae,me,re,W,ue,pe;return[(L=m(B).getLotto)!=null&&L.draw_date?(l(),a(v,{key:0},[e("div",ul,[e("div",al,[e("div",null,o(m(B).getDrawLabel),1),c[7]||(c[7]=e("span",null,"\u53C3\u8003\u671F\u865F\uFF1A",-1)),e("span",null,o((se=A.value)==null?void 0:se.period),1),e("span",null,"\uFF08"+o((K=A.value)==null?void 0:K.draw_date)+"\uFF09",1)]),e("div",ol,[e("div",rl,[(l(!0),a(v,null,g((ae=A.value)==null?void 0:ae.draw_number_size,q=>(l(),a("div",{class:"col-auto",key:q},[e("div",il,o(m(k)(q)),1)]))),128)),(me=A.value)!=null&&me.special_number?(l(),a("div",nl,[(l(),a("div",{class:"ball special-number",key:(re=A.value)==null?void 0:re.special_number},o(m(k)((W=A.value)==null?void 0:W.special_number)),1))])):$("",!0)])])]),e("div",dl,[e("div",cl,[S.value?(l(),x(ie,{key:1,type:"button",label:"\u53D6\u6D88\u9078\u64C7",color:"negative",class:"text-h6 q-ml-md",onClick:de})):(l(),x(ie,{key:0,type:"button",label:"\u91CD\u65B0\u9078\u64C7",color:"primary",class:"text-h6 q-ml-md",onClick:E}))])])],64)):(l(),a("div",ml,c[8]||(c[8]=[e("div",{class:"text-h6"},"\u203B\u8ACB\u9078\u64C7\u53C3\u8003\u671F\u865F",-1)]))),i(xe,{class:"q-mb-md"}),!S.value&&((ue=m(B).getLotto)==null?void 0:ue.draw_date)?(l(),a(v,{key:2},[c[14]||(c[14]=e("div",{class:"row q-mb-md"},[e("div",{class:"col-12 text-h5 text-weight-bolder text-center"}," \u5C3E\u6578\u5206\u6790\u8A2D\u5B9A ")],-1)),e("div",pl,[c[9]||(c[9]=e("div",{class:"col-12 text-h6 text-weight-bold"},"\u5C3E\u6578\u62D6\u724C\u7D44\u5408",-1)),e("div",vl,[i(Y,{outlined:"",dense:"",modelValue:d.value,"onUpdate:modelValue":c[0]||(c[0]=q=>d.value=q),options:te,"emit-value":"","map-options":""},null,8,["modelValue"])]),e("div",fl,[i(Y,{outlined:"",dense:"",modelValue:b.value,"onUpdate:modelValue":c[1]||(c[1]=q=>b.value=q),options:te,"emit-value":"","map-options":""},null,8,["modelValue"])]),e("div",bl,[i(Y,{outlined:"",dense:"",modelValue:P.value,"onUpdate:modelValue":c[2]||(c[2]=q=>P.value=q),options:te,"emit-value":"","map-options":""},null,8,["modelValue"])])]),e("div",gl,[e("div",_l,[c[11]||(c[11]=e("div",{class:"text-h6 text-weight-bold"},"\u63A8\u7B97\u671F\u6578",-1)),e("div",yl,[i(Y,{outlined:"",dense:"",modelValue:T.value,"onUpdate:modelValue":c[3]||(c[3]=q=>T.value=q),options:m(J),"input-debounce":"0","use-input":"","hide-selected":"","fill-input":"",onFilter:le,"emit-value":"","map-options":""},{"no-option":n(()=>[i(Ae,null,{default:n(()=>[i(Pe,{class:"text-grey"},{default:n(()=>c[10]||(c[10]=[y(" \u7121\u53EF\u7528\u9078\u9805 ")])),_:1})]),_:1})]),_:1},8,["modelValue","options"])])]),e("div",wl,[c[12]||(c[12]=e("div",{class:"text-h6 text-weight-bold"},"\u6700\u5927\u5340\u9593",-1)),e("div",kl,[i(Y,{outlined:"",dense:"",modelValue:N.value,"onUpdate:modelValue":c[4]||(c[4]=q=>N.value=q),options:m(z),"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),e("div",hl,[c[13]||(c[13]=e("div",{class:"text-h6 text-weight-bold"},"\u9810\u6E2C\u671F\u6578",-1)),e("div",Cl,[i(Y,{outlined:"",dense:"",modelValue:p.value,"onUpdate:modelValue":c[5]||(c[5]=q=>p.value=q),options:m(R),"emit-value":"","map-options":""},null,8,["modelValue","options"])])])]),i(Be,{align:"right",class:"q-my-lg q-py-none q-px-md"},{default:n(()=>[m(h).isCalculating?(l(),x(ie,{key:0,type:"button",label:"\u4E2D\u65B7\u8A08\u7B97",color:"negative",class:"text-h6 q-mr-md",onClick:t})):$("",!0),i(ie,{type:"button",label:"\u958B\u59CB\u8A08\u7B97",color:"positive",class:"text-h6 q-px-lg q-py-sm",onClick:r,loading:m(h).isCalculating},{loading:n(()=>[i(Ve)]),_:1},8,["loading"])]),_:1})],64)):(l(),x(Qe,{key:3,"draw-type-query":m(B).drawType,"date-query":((pe=A.value)==null?void 0:pe.draw_date)||"",isSelectRef:!0,onSelectRef:C},null,8,["draw-type-query","date-query"]))]}),_:1}),m(h).isCalculating?(l(),x(ee,{key:0},{default:n(()=>[e("div",Dl,o(m(h).progressMessage),1),i($e,{rounded:"",size:"md",value:m(h).progress,"animation-speed":50,color:"primary",class:"q-mb-xs"},null,8,["value"])]),_:1})):$("",!0)]),_:1}),!m(h).isCalculating&&F.value.length>0?(l(),a(v,{key:0},[i($t,{"is-super-lotto":V.value,"draw-results":F.value,"predict-result":j.value,"draw-tail-results":G.value,"rd-results":H.value,"occurrence-results":O.value,"page-size":50,onViewDetail:U},null,8,["is-super-lotto","draw-results","predict-result","draw-tail-results","rd-results","occurrence-results"]),i(sl,{modelValue:s.value,"onUpdate:modelValue":c[6]||(c[6]=L=>s.value=L),results:F.value,"predict-result":j.value,"selected-detail":f.value,occurrences:O.value},null,8,["modelValue","results","predict-result","selected-detail","occurrences"])],64)):$("",!0)]),_:1}))}});export{Wl as default};
