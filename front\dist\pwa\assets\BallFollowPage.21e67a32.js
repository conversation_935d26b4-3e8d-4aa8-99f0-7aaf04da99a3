import{C as Ce,c as we,h as De,Q as Ae,k as _e,r as b,b as ke,w as oe,I as t,J as x,L as p,a2 as H,M as o,a3 as J,P as e,O as a,az as s,aC as k,aA as w,A as m,aB as R,a8 as se,be as z,N,b6 as qe,bf as Ne,a5 as xe,aD as Pe,a6 as Ee}from"./index.5bec8c58.js";import{Q as te,b as re}from"./QSelect.88ca58b5.js";import{Q as $e,a as Ve}from"./QItem.ca9e1076.js";import{Q as Re}from"./QSpinnerDots.4c09bdd2.js";import{Q as Se}from"./QLinearProgress.ab26c7b0.js";import{Q as Qe}from"./QPage.c6f07f5b.js";import{Q as ze,a as me,b as Le,c as ve,u as Me}from"./QTabPanels.966b0370.js";import{u as he,_ as Ie}from"./IndexPage.1029fcec.js";import{u as Ue}from"./useLotteryAnalysis.8aefd45f.js";import{L as ge}from"./lotto.5fc4e377.js";import{p as h}from"./padding.dd505b59.js";import{Q as ye}from"./QPagination.0db9a4e5.js";import{Q as Te}from"./QSpace.5cb73d14.js";import{_ as Fe}from"./plugin-vue_export-helper.21dcd24c.js";import"./position-engine.c9e2754b.js";import"./selection.d62715cb.js";import"./QResizeObserver.e69cd9d2.js";import"./touch.9135741d.js";import"./use-render-cache.3aae9b27.js";import"./QPopupProxy.325da3b8.js";var He=Ce({name:"QBtnGroup",props:{unelevated:Boolean,outline:Boolean,flat:Boolean,rounded:Boolean,square:Boolean,push:Boolean,stretch:Boolean,glossy:Boolean,spread:Boolean},setup(le,{slots:f}){const y=we(()=>{const D=["unelevated","outline","flat","rounded","square","push","stretch","glossy"].filter(C=>le[C]===!0).map(C=>`q-btn-group--${C}`).join(" ");return`q-btn-group row no-wrap${D.length!==0?" "+D:""}`+(le.spread===!0?" q-btn-group--spread":" inline")});return()=>De("div",{class:y.value},Ae(f.default))}});const Oe={class:"row q-gutter-y-md"},je={class:"col-12 col-sm-5 draw-title text-center"},Ge={class:"text-period"},We={class:"text-draw-date"},Je={class:"col-12 col-sm-7 self-center"},Ze={class:"row justify-center"},Ke={key:0,class:"col-auto"},Xe={class:"row q-my-md q-gutter-sm items-center"},Ye={class:"col-sm-auto"},et={class:"col-sm-auto"},tt={class:"col-sm-auto"},st={class:"row"},lt={class:"col-auto text-h6"},ut={key:0,class:"row q-my-md justify-center"},at={class:"row q-col-gutter-md"},ot={key:0,class:"col-12 q-my-sm"},rt={class:"row q-col-gutter-md items-center text-h6"},it={class:"col-12 col-sm-4"},nt={class:"col-12 col-sm-6"},dt={class:"row q-col-gutter-md"},ct={class:"col-auto"},pt={class:"col-12 col-sm-2 text-center"},mt={key:1,class:"row justify-center"},vt={class:"row q-mb-sm"},_t={class:"col text-h6 text-bold"},bt={class:"row q-col-gutter-sm"},ft={class:"row items-center"},gt={class:"ball"},yt={class:"row q-mb-sm"},wt={class:"col text-h6 text-bold"},ht={class:"row q-col-gutter-sm"},Ft={class:"row items-center"},Bt={class:"ball"},Ct={class:"row q-mb-sm"},Dt={class:"col text-h6 text-bold"},At={class:"row q-col-gutter-sm"},kt={class:"ball"},qt={class:"row q-col-gutter-xs"},Nt={class:"row items-center"},xt={class:"ball tail-number"},Pt={class:"row q-mt-md"},Et={class:"row q-mt-xl"},$t={class:"row q-gutter-y-md"},Vt={class:"col-12 col-sm-4 draw-title text-center"},Rt={class:"text-period"},St={class:"text-draw-date"},Qt={class:"col-12 col-sm-6 self-center"},zt={class:"row justify-center"},Lt={key:0,class:"col-auto"},Mt=_e({__name:"BallFollowResult",props:{maxNumber:{},isSuperLotto:{type:Boolean},drawResults:{},predictResult:{},results:{},occurrenceResults:{},pageSize:{}},emits:["view-details"],setup(le){const f=le,y=b("1"),D=b([{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 1 \u6B21",value:1},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 2 \u6B21",value:2},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 3 \u6B21",value:3},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 4 \u6B21",value:4},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 5 \u6B21",value:5}]),C=b(1),Z=b("above"),ue=b([{label:"(\u542B)\u4EE5\u4E0A",value:"above"},{label:"(\u542B)\u4EE5\u4E0B",value:"below"},{label:"\u525B\u597D",value:"exact"}]),A=b("count"),ie=b([{label:"\u6E96\u78BA\u6B21\u6578\u7D71\u8A08",value:"count"},{label:"\u9810\u6E2C\u7D44\u6578\u7D71\u8A08",value:"group"}]),I=b(1),F=b({pageItems:[],targetNumAppearances:new Map,tailNumAppearances:new Map,totalCount:0,totalPages:0}),r=b(!1);ke(async()=>{r.value=!1,g(),E(),r.value=!0}),oe(()=>C.value,()=>{!r.value||E()}),oe(()=>Z.value,()=>{!r.value||E()}),oe(()=>A.value,()=>{!r.value||E()}),oe(()=>I.value,()=>{!r.value||U()});const g=()=>{C.value=1,I.value=1},B=b([]),O=v=>{const l=[],i=Array.from(v);for(let u=1;u<=f.maxNumber;u++)i.includes(u)||l.push(u);return l},S=b([]),q=b(new Map),E=()=>{S.value=[],I.value=1,q.value.clear();const v=new Map,l=new Map;for(const n of f.results){let _=!1;switch(Z.value){case"above":_=n.consecutiveHits>=C.value;break;case"below":_=n.consecutiveHits<=C.value;break;case"exact":_=n.consecutiveHits===C.value;break;default:_=n.consecutiveHits>=C.value}if(_&&S.value.push(n),A.value==="count")for(const T of n.targetNumbers){let K=n.consecutiveHits;if(_){v.set(T,(v.get(T)||0)+K);const X=T%10;l.set(X,(l.get(X)||0)+K)}else for(const X of n.targetNumbers)q.value.set(X,(q.value.get(X)||0)+K)}else if(A.value==="group")for(const T of n.targetNumbers)if(_){v.set(T,(v.get(T)||0)+1);const K=T%10;l.set(K,(l.get(K)||0)+1)}else q.value.set(T,(q.value.get(T)||0)+1)}const i=Array.from(v.entries()).sort((n,_)=>_[1]-n[1]);F.value.targetNumAppearances=new Map(i),B.value=O(F.value.targetNumAppearances.keys()),q.value=new Map(Array.from(q.value.entries()).filter(n=>B.value.includes(n[0])).sort((n,_)=>_[1]-n[1]));for(const n of B.value)q.value.has(n)||q.value.set(n,0);const u=Array.from(l.entries()).sort((n,_)=>_[1]-n[1]);F.value.tailNumAppearances=new Map(u),U()},U=()=>{const v=(I.value-1)*f.pageSize,l=v+f.pageSize;F.value.pageItems=S.value.slice(v,l),F.value.totalCount=S.value.length,F.value.totalPages=Math.ceil(S.value.length/f.pageSize)},L=b([...f.drawResults].reverse()),c=b("asc"),$=v=>{v==="desc"?(c.value="desc",L.value=[...f.drawResults]):(c.value="asc",L.value=[...f.drawResults].reverse())},M=v=>{var i,u;if(!((u=(i=f.predictResult)==null?void 0:i.draw_number_size)!=null&&u.length))return!1;const l=[...f.predictResult.draw_number_size];return!f.isSuperLotto&&f.predictResult.special_number&&l.push(f.predictResult.special_number),l.includes(v)},j=v=>{var l;return!((l=f.predictResult)!=null&&l.special_number)||f.isSuperLotto?!1:f.predictResult.special_number===v};return(v,l)=>(t(),x(H,{class:"q-mt-md"},{default:p(()=>[o(J,null,{default:p(()=>[o(ze,{modelValue:y.value,"onUpdate:modelValue":l[0]||(l[0]=i=>y.value=i),dense:"",align:"justify",class:"text-h6","active-color":"primary","indicator-color":"primary"},{default:p(()=>[o(me,{name:"1",label:"\u5206\u6790\u7D50\u679C"}),o(me,{name:"2",label:"\u9810\u6E2C\u7D50\u679C"}),o(me,{name:"3",label:"\u958B\u734E\u7D50\u679C"})]),_:1},8,["modelValue"]),v.predictResult.period?(t(),x(H,{key:0,bordered:"",class:"ball-card full-width q-my-lg"},{default:p(()=>[o(J,null,{default:p(()=>[e("div",Oe,[e("div",je,[l[9]||(l[9]=e("div",{class:"text-h6"},"\u9810\u6E2C\u958B\u734E\u7D50\u679C",-1)),e("div",Ge,"\u7B2C "+a(v.predictResult.period)+" \u671F",1),e("div",We," \u958B\u734E\u65E5\u671F\uFF1A"+a(v.predictResult.draw_date),1)]),e("div",Je,[e("div",Ze,[(t(!0),s(w,null,k(v.predictResult.draw_number_size,i=>(t(),s("div",{key:i,class:"col-auto"},[(t(),s("div",{class:"ball",key:i},a(m(h)(i)),1))]))),128)),v.predictResult.special_number?(t(),s("div",Ke,[(t(),s("div",{class:"ball special-number",key:v.predictResult.special_number},a(m(h)(v.predictResult.special_number)),1))])):R("",!0)])])])]),_:1})]),_:1})):R("",!0),y.value==="1"||y.value==="2"?(t(),s(w,{key:1},[e("div",Xe,[l[10]||(l[10]=e("div",{class:"col-sm-auto text-h6"},"\u7BE9\u9078",-1)),e("div",Ye,[o(te,{outlined:"",dense:"",modelValue:C.value,"onUpdate:modelValue":l[1]||(l[1]=i=>C.value=i),options:D.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])]),e("div",et,[o(te,{outlined:"",dense:"",modelValue:Z.value,"onUpdate:modelValue":l[2]||(l[2]=i=>Z.value=i),options:ue.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])]),e("div",tt,[o(te,{outlined:"",dense:"",modelValue:A.value,"onUpdate:modelValue":l[3]||(l[3]=i=>A.value=i),options:ie.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])])]),e("div",st,[e("div",lt," \u5171 "+a(F.value.totalCount)+" \u7B46\u8CC7\u6599 ",1)])],64)):R("",!0),o(Le,{modelValue:y.value,"onUpdate:modelValue":l[8]||(l[8]=i=>y.value=i)},{default:p(()=>[o(ve,{name:"1"},{default:p(()=>[F.value.totalPages>1?(t(),s("div",ut,[o(ye,{modelValue:I.value,"onUpdate:modelValue":l[4]||(l[4]=i=>I.value=i),max:F.value.totalPages,input:!0},null,8,["modelValue","max"])])):R("",!0),e("div",at,[F.value.pageItems.length===0?(t(),s("div",ot,[o(H,{flat:"",bordered:""},{default:p(()=>[o(J,null,{default:p(()=>l[11]||(l[11]=[e("div",{class:"text-h6 text-center"},"\u6C92\u6709\u7B26\u5408\u689D\u4EF6\u7684\u7D50\u679C",-1)])),_:1})]),_:1})])):R("",!0),(t(!0),s(w,null,k(F.value.pageItems,(i,u)=>(t(),s("div",{key:u,class:"col-12 q-my-sm"},[o(H,{flat:"",bordered:""},{default:p(()=>[o(J,null,{default:p(()=>[e("div",rt,[e("div",it,[e("div",null,[l[12]||(l[12]=N(" \u958B\u51FA ")),(t(!0),s(w,null,k(i.firstNumbers,n=>(t(),x(re,{key:n,color:"primary","text-color":"white",size:"lg",dense:""},{default:p(()=>[N(a(m(h)(n)),1)]),_:2},1024))),128)),N(" \u4E0B"+a(i.gap)+"\u671F\u958B ",1),(t(!0),s(w,null,k(i.secondNumbers,n=>(t(),x(re,{key:n,color:"secondary","text-color":"white",size:"lg",dense:""},{default:p(()=>[N(a(m(h)(n)),1)]),_:2},1024))),128)),N(" \u518D\u4E0B"+a(i.targetGap)+"\u671F\u9810\u6E2C\u62D6\u51FA ",1),(t(!0),s(w,null,k(i.targetNumbers,n=>(t(),x(re,{key:n,color:"accent","text-color":"white",size:"lg",dense:""},{default:p(()=>[N(a(m(h)(n)),1)]),_:2},1024))),128))])]),e("div",nt,[e("div",dt,[e("div",ct," \u5DF2\u9023\u7E8C\u62D6\u51FA"+a(i.consecutiveHits)+"\u6B21 ",1)])]),e("div",pt,[o(se,{dense:"",color:"primary",icon:"visibility",label:"\u6AA2\u8996\u8A73\u60C5",class:"text-subtitle1 text-bold",onClick:n=>v.$emit("view-details",i)},null,8,["onClick"])])])]),_:2},1024)]),_:2},1024)]))),128))]),F.value.totalPages>1?(t(),s("div",mt,[o(ye,{modelValue:I.value,"onUpdate:modelValue":l[5]||(l[5]=i=>I.value=i),max:F.value.totalPages,input:!0},null,8,["modelValue","max"])])):R("",!0)]),_:1}),o(ve,{name:"2"},{default:p(()=>{var i;return[e("div",vt,[e("label",_t," \u9810\u6E2C\u865F\u78BC\uFF08"+a(((i=Array.from(F.value.targetNumAppearances.keys()))==null?void 0:i.length)||0)+"\u7B46\uFF09 ",1)]),o(H,{flat:"",bordered:"",class:"q-pa-sm q-mb-md text-subtitle1 appearance"},{default:p(()=>[e("div",bt,[(t(!0),s(w,null,k(F.value.targetNumAppearances.keys(),u=>(t(),s("div",{class:z(["col-4 col-md-2",{predict:M(u),"predict-special-number":j(u)}]),key:u},[e("div",ft,[e("span",gt,a(m(h)(u)),1),N(" ("+a(F.value.targetNumAppearances.get(u))+"\u6B21) ",1)])],2))),128))])]),_:1}),e("div",yt,[e("label",wt," \u672A\u51FA\u73FE\u865F\u78BC - \u4F9D\u9810\u6E2C\u6B21\u6578\u6392\u5E8F\uFF08"+a(B.value.length)+"\u7B46) ",1)]),o(H,{flat:"",bordered:"",class:"q-pa-sm q-mb-md text-subtitle1 appearance"},{default:p(()=>[e("div",ht,[(t(!0),s(w,null,k(q.value.entries(),([u,n])=>(t(),s("div",{class:z(["col-4 col-md-2",{predict:M(u),"predict-special-number":j(u)}]),key:u},[e("div",Ft,[e("span",Bt,a(m(h)(u)),1),N(" ("+a(n)+"\u6B21) ",1)])],2))),128))])]),_:1}),e("div",Ct,[e("label",Dt," \u672A\u51FA\u73FE\u865F\u78BC - \u4F9D\u5927\u5C0F\u6392\u5E8F\uFF08"+a(B.value.length)+"\u7B46) ",1)]),o(H,{flat:"",bordered:"",class:"q-pa-sm q-mb-md text-subtitle1 appearance"},{default:p(()=>[e("div",At,[(t(!0),s(w,null,k(B.value,u=>(t(),s("div",{class:z(["col-3 col-md-1",{predict:M(u),"predict-special-number":j(u)}]),key:u},[e("span",kt,a(m(h)(u)),1)],2))),128))])]),_:1}),l[13]||(l[13]=e("div",{class:"row q-mb-sm"},[e("label",{class:"col text-h6 text-bold"},"\u9810\u6E2C\u5C3E\u6578\u78BC")],-1)),o(H,{flat:"",bordered:"",class:"q-pa-sm text-subtitle1 appearance"},{default:p(()=>[e("div",qt,[(t(!0),s(w,null,k(F.value.tailNumAppearances.keys(),u=>{var n;return t(),s("div",{class:z(["col-4 col-md-2",{predict:(n=v.predictResult.tailSet)==null?void 0:n.includes(u)}]),key:u},[e("div",Nt,[e("span",xt,a(u),1),N(" ("+a(F.value.tailNumAppearances.get(u))+"\u6B21) ",1)])],2)}),128))])]),_:1})]}),_:1}),o(ve,{name:"3"},{default:p(()=>[e("div",Pt,[o(He,{spread:""},{default:p(()=>[o(se,{type:"button",icon:"arrow_downward",label:"\u7531\u820A\u5230\u65B0",color:"primary","text-color":"white",class:z({"bg-secondary":c.value==="asc"}),onClick:l[6]||(l[6]=i=>$("asc"))},null,8,["class"]),o(se,{type:"button",icon:"arrow_upward",label:"\u7531\u65B0\u5230\u820A",color:"primary","text-color":"white",class:z({"bg-secondary":c.value==="desc"}),onClick:l[7]||(l[7]=i=>$("desc"))},null,8,["class"])]),_:1})]),e("div",Et,[(t(!0),s(w,null,k(L.value,i=>(t(),x(H,{bordered:"",key:i.period,class:"ball-card full-width q-mb-md"},{default:p(()=>[o(J,{class:"q-py-md"},{default:p(()=>[e("div",$t,[e("div",Vt,[e("div",Rt,"\u7B2C "+a(i.period)+" \u671F",1),e("div",St," \u958B\u734E\u65E5\u671F\uFF1A"+a(i.draw_date),1)]),e("div",Qt,[e("div",zt,[(t(!0),s(w,null,k(i.draw_number_size,u=>(t(),s("div",{key:u,class:"col-auto"},[(t(),s("div",{class:"ball",key:u},a(m(h)(u)),1))]))),128)),i.special_number?(t(),s("div",Lt,[(t(),s("div",{class:"ball special-number",key:i.special_number},a(m(h)(i.special_number)),1))])):R("",!0)])])])]),_:2},1024)]),_:2},1024))),128))])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}))}});const It={class:"text-h6 q-mb-md"},Ut={class:"text-h6 q-mb-md"},Tt={class:"row text-h6 q-mb-md"},Ht={class:"col-auto"},Ot={class:"row balls"},jt={key:0,class:"col-auto"},Gt={class:"row text-h6 q-mb-md"},Wt={class:"col-auto"},Jt={class:"row balls"},Zt={key:0,class:"col-auto"},Kt={key:0,class:"row text-h6"},Xt={class:"col-auto"},Yt={class:"row balls"},es={key:0,class:"col-auto"},ts={key:1,class:"predict-section"},ss={class:"row text-h6 q-mt-sm"},ls={class:"col-auto"},us={key:0,class:"predict-period"},as={class:"predict-date"},os={class:"row balls"},rs={key:0,class:"col-auto"},is={key:1,class:"col-auto pending-draw"},ns=_e({__name:"BallFollowDetail",props:{modelValue:{type:Boolean},results:{},selectedDetail:{},occurrences:{},predictResult:{}},emits:["update:modelValue"],setup(le,{emit:f}){const y=le,D=he(),C=we({get:()=>y.modelValue,set:r=>Z("update:modelValue",r)}),Z=f,ue=r=>{if(!r)return"";const g=y.results.find(B=>B.period==r);return g==null?void 0:g.draw_date},A=r=>{if(!r)return{numbers:[],specialNumber:0};const g=y.results.find(B=>B.period==r);return{numbers:g==null?void 0:g.draw_number_size,specialNumber:g!=null&&g.special_number?Number(g.special_number):0}},ie=r=>{var U,L,c;if(!r)return[];const g=r.firstNumbers.join(",")+"-"+r.secondNumbers.join(",")+"-"+r.gap+"-"+r.targetGap,B=(L=(U=y.occurrences.get(g))==null?void 0:U.periods)!=null?L:[],O=B.filter($=>$.targetPeriod===void 0||$.targetPeriod===null||$.targetPeriod.trim()===""),S=B.filter($=>$.targetPeriod!==void 0&&$.targetPeriod!==null&&$.targetPeriod.trim()!==""),q=(c=r.consecutiveHits)!=null?c:0;let E=[];return q>0&&S.length>0&&(E=S.sort((M,j)=>{const v=parseInt(M.targetPeriod),l=parseInt(j.targetPeriod);return isNaN(v)||isNaN(l)?M.targetPeriod.localeCompare(j.targetPeriod):l-v}).slice(0,q)),O.length>0&&(E=[...O,...E]),E},I=r=>r.targetPeriod===void 0,F=()=>{Z("update:modelValue",!1)};return(r,g)=>(t(),x(qe,{modelValue:C.value,"onUpdate:modelValue":g[0]||(g[0]=B=>C.value=B)},{default:p(()=>[o(H,{style:{"max-width":"100%",width:"800px"}},{default:p(()=>[o(J,{class:"row items-center"},{default:p(()=>[g[1]||(g[1]=e("div",{class:"text-h6"},"\u8A73\u7D30\u8CC7\u6599",-1)),o(Te),o(se,{icon:"close",flat:"",round:"",dense:"",onClick:F})]),_:1}),o(J,{class:"q-pa-md"},{default:p(()=>{var B,O,S,q,E,U,L;return[e("div",It,[g[2]||(g[2]=N(" \u958B\u51FA ")),(t(!0),s(w,null,k((B=r.selectedDetail)==null?void 0:B.firstNumbers,c=>(t(),x(re,{key:c,color:"primary","text-color":"white",class:"text-h6"},{default:p(()=>[N(a(m(h)(c)),1)]),_:2},1024))),128)),N(" \u4E0B"+a((O=r.selectedDetail)==null?void 0:O.gap)+"\u671F\u958B ",1),(t(!0),s(w,null,k((S=r.selectedDetail)==null?void 0:S.secondNumbers,c=>(t(),x(re,{key:c,color:"secondary","text-color":"white",class:"text-h6"},{default:p(()=>[N(a(m(h)(c)),1)]),_:2},1024))),128)),N(" \u518D\u4E0B "+a((q=r.selectedDetail)==null?void 0:q.targetGap)+" \u671F\u9810\u6E2C\u62D6\u51FA ",1),(t(!0),s(w,null,k((E=r.selectedDetail)==null?void 0:E.targetNumbers,c=>(t(),x(re,{key:c,color:"accent","text-color":"white",class:"text-h6"},{default:p(()=>[N(a(m(h)(c)),1)]),_:2},1024))),128))]),e("div",Ut," \u5DF2\u9023\u7E8C\u62D6\u51FA"+a((L=(U=r.selectedDetail)==null?void 0:U.consecutiveHits)!=null?L:0)+"\u6B21 ",1),e("div",null,[(t(!0),s(w,null,k(ie(r.selectedDetail),(c,$)=>(t(),x(H,{key:$,class:"q-mb-md",style:Ne({"background-color":I(c)?"#e8f5e8":"#fefefe"})},{default:p(()=>[o(J,null,{default:p(()=>{var M,j,v,l,i;return[e("div",Tt,[e("div",Ht,[e("div",null,"\u7B2C "+a(c.firstPeriod)+" \u671F",1),e("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+a(ue(c.firstPeriod)),1)]),e("div",Ot,[(t(!0),s(w,null,k(A(c.firstPeriod).numbers,(u,n)=>{var _;return t(),s("div",{class:"col-auto",key:n},[e("div",{class:z(["ball",{"first-num":(_=r.selectedDetail)==null?void 0:_.firstNumbers.includes(u)}])},a(m(h)(u)),3)])}),128)),A(c.firstPeriod).specialNumber?(t(),s("div",jt,[e("div",{class:z(["ball special-number",{"first-num":((M=r.selectedDetail)==null?void 0:M.firstNumbers.includes(A(c.firstPeriod).specialNumber))&&!m(D).isSuperLotto}])},a(m(h)(A(c.firstPeriod).specialNumber)),3)])):R("",!0)])]),e("div",Gt,[e("div",Wt,[e("div",null,"\u7B2C "+a(c.secondPeriod)+" \u671F",1),e("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+a(ue(c.secondPeriod)),1)]),e("div",Jt,[(t(!0),s(w,null,k(A(c.secondPeriod).numbers,(u,n)=>{var _;return t(),s("div",{class:"col-auto",key:n},[e("div",{class:z(["ball",{"second-num":(_=r.selectedDetail)==null?void 0:_.secondNumbers.includes(u)}])},a(m(h)(u)),3)])}),128)),A(c.secondPeriod).specialNumber?(t(),s("div",Zt,[e("div",{class:z(["ball special-number",{"second-num":((j=r.selectedDetail)==null?void 0:j.secondNumbers.includes(A(c.secondPeriod).specialNumber))&&!m(D).isSuperLotto}])},a(m(h)(A(c.secondPeriod).specialNumber)),3)])):R("",!0)])]),c.targetPeriod?(t(),s("div",Kt,[e("div",Xt,[e("div",null,"\u7B2C "+a(c.targetPeriod)+" \u671F",1),e("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+a((v=ue(c.targetPeriod))!=null?v:"\u672A\u958B\u734E"),1)]),e("div",Yt,[(t(!0),s(w,null,k(A(c.targetPeriod).numbers,(u,n)=>{var _;return t(),s("div",{class:"col-auto",key:n},[e("div",{class:z(["ball",{"target-num":(_=r.selectedDetail)==null?void 0:_.targetNumbers.includes(u)}])},a(m(h)(u)),3)])}),128)),A(c.targetPeriod).specialNumber?(t(),s("div",es,[e("div",{class:z(["ball special-number",{"target-num":((l=r.selectedDetail)==null?void 0:l.targetNumbers.includes(A(c.targetPeriod).specialNumber))&&!m(D).isSuperLotto}])},a(m(h)(A(c.targetPeriod).specialNumber)),3)])):R("",!0)])])):(t(),s("div",ts,[e("div",ss,[e("div",ls,[r.predictResult.period?(t(),s("div",us," \u7B2C "+a(r.predictResult.period)+" \u671F ",1)):R("",!0),e("div",as,[r.predictResult.period?(t(),s(w,{key:0},[N(" \u5BE6\u969B\u958B\u734E\u65E5\u671F\uFF1A"+a(r.predictResult.draw_date),1)],64)):(t(),s(w,{key:1},[N(" \u9810\u6E2C\u671F\u865F\uFF1A\u5C1A\u672A\u958B\u734E ")],64))])]),e("div",os,[r.predictResult.period?(t(),s(w,{key:0},[(t(!0),s(w,null,k(r.predictResult.draw_number_size,(u,n)=>{var _;return t(),s("div",{class:"col-auto",key:n},[e("div",{class:z(["ball",{"target-num":(_=r.selectedDetail)==null?void 0:_.targetNumbers.includes(u)}])},a(m(h)(u)),3)])}),128)),r.predictResult.special_number?(t(),s("div",rs,[e("div",{class:z(["ball special-number",{"target-num":((i=r.selectedDetail)==null?void 0:i.targetNumbers.includes(r.predictResult.special_number))&&!m(D).isSuperLotto}])},a(m(h)(r.predictResult.special_number)),3)])):R("",!0)],64)):(t(),s("div",is,[o(xe,{name:"schedule",size:"lg"}),g[3]||(g[3]=e("span",null,"\u5C1A\u672A\u958B\u734E",-1))]))])])]))]}),_:2},1024)]),_:2},1032,["style"]))),128))])]}),_:1})]),_:1})]),_:1},8,["modelValue"]))}});var ds=Fe(ns,[["__scopeId","data-v-21f33910"]]);const cs={class:"row lto-ref q-mb-sm"},ps={class:"col-12 col-sm-4 self-center text-h6"},ms={class:"col-12 col-sm-6 self-center text-subtitle1"},vs={class:"row balls"},_s={class:"ball"},bs={key:0,class:"col-auto"},fs={class:"row q-mb-md"},gs={class:"col"},ys={key:1,class:"row q-mb-md"},ws={class:"row q-mb-md"},hs={class:"col-12 col-sm-4 q-pa-sm"},Fs={class:"col-12 col-sm-4 q-pa-sm"},Bs={class:"col-12 col-sm-4 q-pa-sm"},Cs={class:"row q-mb-md"},Ds={class:"col-12 col-sm-4"},As={class:"q-pa-sm"},ks={class:"col-12 col-sm-4"},qs={class:"q-pa-sm"},Ns={class:"col-12 col-sm-4"},xs={class:"q-pa-sm"},Ps={class:"text-center q-mb-sm"},Es=_e({__name:"BallFollowPage",setup(le){const f=Me(),y=he(),D=b(y.getLotto),C=b(!1),Z=()=>{C.value=!0},ue=()=>{C.value=!1},A=()=>{C.value=!1};oe(()=>y.getLotto,P=>{P&&(D.value=P)}),oe(()=>{var P;return(P=D.value)==null?void 0:P.period},()=>{n.value=[]});const{analyzeWithProgress:ie,stopAnalyzer:I,setConfig:F,setResults:r}=Ue(),g=b([]),B=b(new Map),O=b(!1),S=b(null),q=P=>{S.value=P,O.value=!0},E=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3}],U=b(1),L=b(1),c=b(1);let $=b(Array.from({length:991},(P,d)=>({label:`${d+10}\u671F`,value:d+10})));const M=b(50),j=(P,d,Y)=>{const ee=parseInt(P,10);(ee<10||ee>1e3)&&Y(),d(()=>{$.value=Array.from({length:991},(W,ae)=>ae+10).filter(W=>W.toString().startsWith(P)).map(W=>({label:`${W.toString()}\u671F`,value:W}))})},v=Array.from({length:21},(P,d)=>({label:`${d+10}\u671F`,value:d+10})),l=b(20),i=Array.from({length:15},(P,d)=>({label:`\u4E0B${d+1}\u671F`,value:d+1})),u=b(1),n=b([]),_=b({period:"",draw_date:"",draw_number_appear:[],draw_number_size:[],tails:new Map});let T=1;const K=b(49),X=b(!1),Be=async()=>{var P,d,Y;try{f.startCalculating(),g.value=[],T=u.value,K.value=y.getMaxNumber,X.value=y.isSuperLotto;const ee=await ge.getLottoList({draw_type:y.getDrawType,date_end:(d=(P=D.value)==null?void 0:P.draw_date)!=null?d:"",limit:M.value});n.value=ee.data;const W=await ge.getLottoPredict({draw_type:y.getDrawType,draw_date:(Y=y.getLotto)==null?void 0:Y.draw_date,ahead_count:T});if(_.value=W.data,_.value.period){const Q=new Set,G=[..._.value.draw_number_size];!X.value&&_.value.special_number&&G.push(_.value.special_number);for(const pe of G)Q.add(pe%10);const V=Array.from(Q).sort((pe,fe)=>pe===0?1:fe===0?-1:pe-fe);_.value.tailSet=V}const ae=n.value.map(Q=>{const G=[...Q.draw_number_size];return Q.special_number&&!y.isSuperLotto&&G.push(Q.special_number),{numbers:[...G],period:String(Q.period)}}).reverse();r(ae),F({firstGroupSize:U.value,secondGroupSize:L.value,targetGroupSize:c.value,maxRange:l.value,lookAheadCount:u.value});let ne=Date.now();const ce=8,de=await ie(async Q=>{const G=Date.now();G-ne>=ce&&(await f.updateProgress(Q),ne=G)},Q=>{f.addWarning(Q)});g.value=de.data,B.value=de.occurrences}catch(ee){console.error("\u6578\u64DA\u8F09\u5165\u5931\u6557:",ee)}finally{be()}},be=()=>{I(),f.stopCalculating()};return(P,d)=>(t(),x(Qe,{class:"justify-center"},{default:p(()=>[o(H,null,{default:p(()=>[o(J,null,{default:p(()=>{var Y,ee,W,ae,ne,ce,de,Q,G;return[(Y=m(y).getLotto)!=null&&Y.draw_date?(t(),s(w,{key:0},[e("div",cs,[e("div",ps,[e("div",null,a(m(y).getDrawLabel),1),d[7]||(d[7]=e("span",null,"\u53C3\u8003\u671F\u865F\uFF1A",-1)),e("span",null,a((ee=D.value)==null?void 0:ee.period),1),e("span",null,"\uFF08"+a((W=D.value)==null?void 0:W.draw_date)+"\uFF09",1)]),e("div",ms,[e("div",vs,[(t(!0),s(w,null,k((ae=D.value)==null?void 0:ae.draw_number_size,V=>(t(),s("div",{class:"col-auto",key:V},[e("div",_s,a(m(h)(V)),1)]))),128)),(ne=D.value)!=null&&ne.special_number?(t(),s("div",bs,[(t(),s("div",{class:"ball special-number",key:(ce=D.value)==null?void 0:ce.special_number},a(m(h)((de=D.value)==null?void 0:de.special_number)),1))])):R("",!0)])])]),e("div",fs,[e("div",gs,[C.value?(t(),x(se,{key:1,type:"button",label:"\u53D6\u6D88\u9078\u64C7",color:"negative",class:"text-h6 q-ml-md",onClick:A})):(t(),x(se,{key:0,type:"button",label:"\u91CD\u65B0\u9078\u64C7",color:"primary",class:"text-h6 q-ml-md",onClick:Z}))])])],64)):(t(),s("div",ys,d[8]||(d[8]=[e("div",{class:"text-h6"},"\u203B\u8ACB\u9078\u64C7\u53C3\u8003\u671F\u865F",-1)]))),o(Pe,{class:"q-mb-md"}),!C.value&&((Q=m(y).getLotto)==null?void 0:Q.draw_date)?(t(),s(w,{key:2},[d[14]||(d[14]=e("div",{class:"row q-mb-md"},[e("div",{class:"col-12 text-h5 text-weight-bolder text-center"}," \u7248\u8DEF\u5206\u6790\u8A2D\u5B9A ")],-1)),e("div",ws,[d[9]||(d[9]=e("div",{class:"col-12 text-h6 text-weight-bold"},"\u62D6\u724C\u7D44\u5408",-1)),e("div",hs,[o(te,{outlined:"",dense:"",modelValue:U.value,"onUpdate:modelValue":d[0]||(d[0]=V=>U.value=V),options:E,"emit-value":"","map-options":""},null,8,["modelValue"])]),e("div",Fs,[o(te,{outlined:"",dense:"",modelValue:L.value,"onUpdate:modelValue":d[1]||(d[1]=V=>L.value=V),options:E,"emit-value":"","map-options":""},null,8,["modelValue"])]),e("div",Bs,[o(te,{outlined:"",dense:"",modelValue:c.value,"onUpdate:modelValue":d[2]||(d[2]=V=>c.value=V),options:E,"emit-value":"","map-options":""},null,8,["modelValue"])])]),e("div",Cs,[e("div",Ds,[d[11]||(d[11]=e("div",{class:"text-h6 text-weight-bold"},"\u63A8\u7B97\u671F\u6578",-1)),e("div",As,[o(te,{outlined:"",dense:"",modelValue:M.value,"onUpdate:modelValue":d[3]||(d[3]=V=>M.value=V),options:m($),"input-debounce":"0","use-input":"","hide-selected":"","fill-input":"",onFilter:j,"emit-value":"","map-options":""},{"no-option":p(()=>[o($e,null,{default:p(()=>[o(Ve,{class:"text-grey"},{default:p(()=>d[10]||(d[10]=[N(" \u7121\u53EF\u7528\u9078\u9805 ")])),_:1})]),_:1})]),_:1},8,["modelValue","options"])])]),e("div",ks,[d[12]||(d[12]=e("div",{class:"text-h6 text-weight-bold"},"\u6700\u5927\u5340\u9593",-1)),e("div",qs,[o(te,{outlined:"",dense:"",modelValue:l.value,"onUpdate:modelValue":d[4]||(d[4]=V=>l.value=V),options:m(v),"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),e("div",Ns,[d[13]||(d[13]=e("div",{class:"text-h6 text-weight-bold"},"\u9810\u6E2C\u671F\u6578",-1)),e("div",xs,[o(te,{outlined:"",dense:"",modelValue:u.value,"onUpdate:modelValue":d[5]||(d[5]=V=>u.value=V),options:m(i),"emit-value":"","map-options":""},null,8,["modelValue","options"])])])]),o(Ee,{align:"right",class:"q-my-lg q-py-none q-px-md"},{default:p(()=>[m(f).isCalculating?(t(),x(se,{key:0,type:"button",label:"\u4E2D\u65B7\u8A08\u7B97",color:"negative",onClick:be,class:"text-h6 q-mr-md"})):R("",!0),o(se,{type:"button",label:"\u958B\u59CB\u8A08\u7B97",color:"positive",class:"text-h6 q-px-lg q-py-sm",onClick:Be,loading:m(f).isCalculating},{loading:p(()=>[o(Re)]),_:1},8,["loading"])]),_:1})],64)):(t(),x(Ie,{key:3,"draw-type-query":m(y).drawType,"date-query":((G=D.value)==null?void 0:G.draw_date)||"","is-select-ref":!0,onSelectRef:ue},null,8,["draw-type-query","date-query"]))]}),_:1}),m(f).isCalculating?(t(),x(J,{key:0},{default:p(()=>[e("div",Ps,a(m(f).progressMessage),1),o(Se,{rounded:"",size:"md",value:m(f).progress,"animation-speed":50,color:"primary",class:"q-mb-xs"},null,8,["value"])]),_:1})):R("",!0)]),_:1}),!m(f).isCalculating&&n.value.length>0?(t(),x(Mt,{key:0,"max-number":K.value,"is-super-lotto":X.value,"draw-results":n.value,"predict-result":_.value,results:g.value,"occurrence-results":B.value,"page-size":50,onViewDetails:q},null,8,["max-number","is-super-lotto","draw-results","predict-result","results","occurrence-results"])):R("",!0),o(ds,{modelValue:O.value,"onUpdate:modelValue":d[6]||(d[6]=Y=>O.value=Y),results:n.value,"selected-detail":S.value,occurrences:B.value,"predict-result":_.value},null,8,["modelValue","results","selected-detail","occurrences","predict-result"])]),_:1}))}});var Ys=Fe(Es,[["__scopeId","data-v-d50c406a"]]);export{Ys as default};
