# 尾數分析匯出功能完成說明

## 功能概述
已完成 `BatchAnalysisPage.vue` 中尾數分析的匯出功能，參考版路分析的匯出格式，並整合了 `TailFollowResult.vue` 中 tab=1 的預測結果(出現次數)部分。

## 主要修改內容

### 1. 更新匯出邏輯
在 `downloadExcel()` 函數中，將原來的尾數分析匯出：
```javascript
case 'tail':
  createTailSheet(workbook);
  createActualNumbersSheet(workbook);
  break;
```

修改為：
```javascript
case 'tail':
  createTailAnalysisSheet(workbook);
  createTailAppearancesSheet(workbook);
  createActualNumbersSheet(workbook);
  break;
```

### 2. 新增兩個匯出工作表函數

#### A. `createTailAnalysisSheet()` - 尾數分析主要工作表
- 保留原有的尾數分析功能
- 包含：日期、預測尾數、實際尾數、命中尾數、命中數量
- 格式與原來的 `createTailSheet()` 相同

#### B. `createTailAppearancesSheet()` - 尾數分析統計工作表
- **新增功能**：參考 `TailFollowResult.vue` 中 tab=1 的預測結果
- 顯示尾數出現次數統計 (`tailNumAppearances`)
- 按出現次數排序（次數高的在前）
- 每10個尾數一列的格式
- 尾數不使用 padding zero（與版路分析的號碼格式區分）

### 3. 工作表結構

#### 尾數分析統計工作表內容：
- **工作表名稱**：「尾數分析」
- **標題**：「尾數分析統計」
- **格式**：
  - 第一欄：日期
  - 後續欄位：按出現次數排序的尾數（每列最多10個）
  - 列寬設置：日期欄12字符，尾數欄6字符

#### 實際開獎號碼工作表：
- 保持原有功能不變
- 顯示每期的實際開獎號碼

### 4. 數據來源
- 使用 `batchResults.value` 中的 `tailNumAppearances` 數據
- 這個數據來自於尾數分析的詳細統計結果
- 與 `TailFollowResult.vue` 中 `paginatedResults.targetNumAppearances` 的數據結構相同

### 5. 綜合分析更新
同時更新了綜合分析 (`createPatternSheet`) 中的尾數分析部分，確保包含新的統計工作表。

## 匯出結果
當選擇「尾數分析」並執行匯出時，Excel 文件將包含：
1. **預測尾數** - 統計結果（按出現次數排序的尾數）
2. **實際開獎號碼** - 各期開獎號碼

## 問題修復與優化
- **修復工作表名稱重複問題**：原本兩個工作表都叫「尾數分析」導致 Excel 錯誤
- **刪除不需要的工作表**：移除「尾數分析結果」工作表，只保留「預測尾數」統計
- **簡化匯出結構**：尾數分析只需要統計結果，不需要詳細的分析結果
- **新增篩選條件**：為尾數分析添加「連續拖出次數」篩選功能（最多10次）
- **最終工作表名稱**：
  - 統計結果：「預測尾數」
  - 開獎號碼：「實際開獎號碼」

## 技術特點
- 保持與版路分析匯出格式的一致性
- 尾數格式不使用 padding zero（如：1, 2, 3 而非 01, 02, 03）
- 按出現次數降序排列，符合用戶查看習慣
- **獨立篩選條件**：尾數分析有專用的篩選條件變數 `tailAccuracy` 和 `tailFilterCondition`
- **連續拖出次數範圍**：支援1-10次的連續拖出次數篩選
- 自動設置適當的列寬

## 新增篩選功能詳細說明
### UI 組件
- **連續拖出次數**：下拉選單，選項從「已連續拖出 1 次」到「已連續拖出 10 次」
- **篩選條件**：下拉選單，包含「(含)以上」、「(含)以下」、「剛好」三個選項

### 變數定義
```javascript
// 尾數分析篩選條件
const tailAccuracy = ref(1);
const tailAccuracyOpts = ref([
  { label: '已連續拖出 1 次', value: 1 },
  // ... 到 10 次
]);

const tailFilterCondition = ref('above');
```

### 邏輯應用
- 尾數分析使用 `tailAccuracy.value` 和 `tailFilterCondition.value`
- 版路分析仍使用原有的 `accuracy.value` 和 `filterCondition.value`
- 綜合分析中的尾數部分也使用尾數專用的篩選條件

## 格式說明
### 預測尾數工作表
- **格式**：參考版路分析的預測號碼格式
- **內容**：按出現次數排序的尾數統計
- **每列最多10個尾數**，便於閱讀

## 測試建議
1. 選擇尾數分析方法
2. 設置分析參數並執行分析
3. 點擊下載按鈕
4. 檢查生成的 Excel 文件是否包含兩個尾數相關的工作表
5. 驗證數據格式和排序是否正確
