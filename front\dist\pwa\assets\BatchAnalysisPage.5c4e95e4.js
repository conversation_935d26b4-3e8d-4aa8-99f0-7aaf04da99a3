import{Q as be}from"./QSelect.88ca58b5.js";import{k as ls,r as Ye,c as O0,b as os,I as Jr,J as ln,L as Zr,M as Se,a3 as Xn,P as te,a4 as us,az as on,aA as zn,aB as lt,a6 as cs,a8 as $n,O as Kn,a2 as D0,af as ot}from"./index.5bec8c58.js";import{Q as hs}from"./QSpinnerDots.4c09bdd2.js";import{Q as xs}from"./QLinearProgress.ab26c7b0.js";import{Q as ds}from"./QPage.c6f07f5b.js";import{L as Yn}from"./lotto.5fc4e377.js";import{u as ps}from"./useLotteryAnalysis.8aefd45f.js";import{h as ms}from"./error-handler.3b164cca.js";import{_ as vs}from"./plugin-vue_export-helper.21dcd24c.js";import"./QItem.ca9e1076.js";import"./position-engine.c9e2754b.js";import"./selection.d62715cb.js";/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */var gn={};gn.version="0.18.5";var ma=1252,gs=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],va=function(e){gs.indexOf(e)!=-1&&(ma=e)};function _s(){va(1252)}var $t=function(e){va(e)};function Es(){$t(1200),_s()}function Ts(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var un=function(t){return String.fromCharCode(t)},N0=function(t){return String.fromCharCode(t)},ut,et="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function Kt(e){for(var t="",r=0,n=0,a=0,i=0,s=0,f=0,o=0,l=0;l<e.length;)r=e.charCodeAt(l++),i=r>>2,n=e.charCodeAt(l++),s=(r&3)<<4|n>>4,a=e.charCodeAt(l++),f=(n&15)<<2|a>>6,o=a&63,isNaN(n)?f=o=64:isNaN(a)&&(o=64),t+=et.charAt(i)+et.charAt(s)+et.charAt(f)+et.charAt(o);return t}function Kr(e){var t="",r=0,n=0,a=0,i=0,s=0,f=0,o=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var l=0;l<e.length;)i=et.indexOf(e.charAt(l++)),s=et.indexOf(e.charAt(l++)),r=i<<2|s>>4,t+=String.fromCharCode(r),f=et.indexOf(e.charAt(l++)),n=(s&15)<<4|f>>2,f!==64&&(t+=String.fromCharCode(n)),o=et.indexOf(e.charAt(l++)),a=(f&3)<<6|o,o!==64&&(t+=String.fromCharCode(a));return t}var ye=function(){return typeof Buffer!="undefined"&&typeof process!="undefined"&&typeof process.versions!="undefined"&&!!process.versions.node}(),jr=function(){if(typeof Buffer!="undefined"){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch{e=!0}return e?function(t,r){return r?new Buffer(t,r):new Buffer(t)}:Buffer.from.bind(Buffer)}return function(){}}();function xt(e){return ye?Buffer.alloc?Buffer.alloc(e):new Buffer(e):typeof Uint8Array!="undefined"?new Uint8Array(e):new Array(e)}function R0(e){return ye?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):typeof Uint8Array!="undefined"?new Uint8Array(e):new Array(e)}var Br=function(t){return ye?jr(t,"binary"):t.split("").map(function(r){return r.charCodeAt(0)&255})};function In(e){if(typeof ArrayBuffer=="undefined")return Br(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=0;n!=e.length;++n)r[n]=e.charCodeAt(n)&255;return t}function Zt(e){if(Array.isArray(e))return e.map(function(n){return String.fromCharCode(n)}).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function ws(e){if(typeof Uint8Array=="undefined")throw new Error("Unsupported");return new Uint8Array(e)}var ir=ye?function(e){return Buffer.concat(e.map(function(t){return Buffer.isBuffer(t)?t:jr(t)}))}:function(e){if(typeof Uint8Array!="undefined"){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var n=new Uint8Array(r),a=0;for(t=0,r=0;t<e.length;r+=a,++t)if(a=e[t].length,e[t]instanceof Uint8Array)n.set(e[t],r);else{if(typeof e[t]=="string")throw"wtf";n.set(new Uint8Array(e[t]),r)}return n}return[].concat.apply([],e.map(function(i){return Array.isArray(i)?i:[].slice.call(i)}))};function As(e){for(var t=[],r=0,n=e.length+250,a=xt(e.length+255),i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s<128)a[r++]=s;else if(s<2048)a[r++]=192|s>>6&31,a[r++]=128|s&63;else if(s>=55296&&s<57344){s=(s&1023)+64;var f=e.charCodeAt(++i)&1023;a[r++]=240|s>>8&7,a[r++]=128|s>>2&63,a[r++]=128|f>>6&15|(s&3)<<4,a[r++]=128|f&63}else a[r++]=224|s>>12&15,a[r++]=128|s>>6&63,a[r++]=128|s&63;r>n&&(t.push(a.slice(0,r)),r=0,a=xt(65535),n=65530)}return t.push(a.slice(0,r)),ir(t)}var Wt=/\u0000/g,cn=/[\u0001-\u0006]/g;function Ct(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function Mr(e,t){var r=""+e;return r.length>=t?r:Ge("0",t-r.length)+r}function f0(e,t){var r=""+e;return r.length>=t?r:Ge(" ",t-r.length)+r}function _n(e,t){var r=""+e;return r.length>=t?r:r+Ge(" ",t-r.length)}function Ss(e,t){var r=""+Math.round(e);return r.length>=t?r:Ge("0",t-r.length)+r}function Fs(e,t){var r=""+e;return r.length>=t?r:Ge("0",t-r.length)+r}var I0=Math.pow(2,32);function Tt(e,t){if(e>I0||e<-I0)return Ss(e,t);var r=Math.round(e);return Fs(r,t)}function En(e,t){return t=t||0,e.length>=7+t&&(e.charCodeAt(t)|32)===103&&(e.charCodeAt(t+1)|32)===101&&(e.charCodeAt(t+2)|32)===110&&(e.charCodeAt(t+3)|32)===101&&(e.charCodeAt(t+4)|32)===114&&(e.charCodeAt(t+5)|32)===97&&(e.charCodeAt(t+6)|32)===108}var k0=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],jn=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function Cs(e){return e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"\u4E0A\u5348/\u4E0B\u5348 "hh"\u6642"mm"\u5206"ss"\u79D2 "',e}var Xe={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"\u4E0A\u5348/\u4E0B\u5348 "hh"\u6642"mm"\u5206"ss"\u79D2 "'},P0={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},ys={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function Tn(e,t,r){for(var n=e<0?-1:1,a=e*n,i=0,s=1,f=0,o=1,l=0,u=0,x=Math.floor(a);l<t&&(x=Math.floor(a),f=x*s+i,u=x*l+o,!(a-x<5e-8));)a=1/(a-x),i=s,s=f,o=l,l=u;if(u>t&&(l>t?(u=o,f=i):(u=l,f=s)),!r)return[0,n*f,u];var d=Math.floor(n*f/u);return[d,n*f-d*u,u]}function hn(e,t,r){if(e>2958465||e<0)return null;var n=e|0,a=Math.floor(86400*(e-n)),i=0,s=[],f={D:n,T:a,u:86400*(e-n)-a,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(f.u)<1e-6&&(f.u=0),t&&t.date1904&&(n+=1462),f.u>.9999&&(f.u=0,++a==86400&&(f.T=a=0,++n,++f.D)),n===60)s=r?[1317,10,29]:[1900,2,29],i=3;else if(n===0)s=r?[1317,8,29]:[1900,1,0],i=6;else{n>60&&--n;var o=new Date(1900,0,1);o.setDate(o.getDate()+n-1),s=[o.getFullYear(),o.getMonth()+1,o.getDate()],i=o.getDay(),n<60&&(i=(i+6)%7),r&&(i=Ps(o,s))}return f.y=s[0],f.m=s[1],f.d=s[2],f.S=a%60,a=Math.floor(a/60),f.M=a%60,a=Math.floor(a/60),f.H=a,f.q=i,f}var ga=new Date(1899,11,31,0,0,0),Os=ga.getTime(),Ds=new Date(1900,2,1,0,0,0);function _a(e,t){var r=e.getTime();return t?r-=1461*24*60*60*1e3:e>=Ds&&(r+=24*60*60*1e3),(r-(Os+(e.getTimezoneOffset()-ga.getTimezoneOffset())*6e4))/(24*60*60*1e3)}function l0(e){return e.indexOf(".")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function Ns(e){return e.indexOf("E")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function Rs(e){var t=e<0?12:11,r=l0(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10),r.length<=t)?r:e.toExponential(5)}function Is(e){var t=l0(e.toFixed(11));return t.length>(e<0?12:11)||t==="0"||t==="-0"?e.toPrecision(6):t}function ks(e){var t=Math.floor(Math.log(Math.abs(e))*Math.LOG10E),r;return t>=-4&&t<=-1?r=e.toPrecision(10+t):Math.abs(t)<=9?r=Rs(e):t===10?r=e.toFixed(10).substr(0,12):r=Is(e),l0(Ns(r.toUpperCase()))}function n0(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(e|0)===e?e.toString(10):ks(e);case"undefined":return"";case"object":if(e==null)return"";if(e instanceof Date)return tt(14,_a(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function Ps(e,t){t[0]-=581;var r=e.getDay();return e<60&&(r=(r+6)%7),r}function Ls(e,t,r,n){var a="",i=0,s=0,f=r.y,o,l=0;switch(e){case 98:f=r.y+543;case 121:switch(t.length){case 1:case 2:o=f%100,l=2;break;default:o=f%1e4,l=4;break}break;case 109:switch(t.length){case 1:case 2:o=r.m,l=t.length;break;case 3:return jn[r.m-1][1];case 5:return jn[r.m-1][0];default:return jn[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:o=r.d,l=t.length;break;case 3:return k0[r.q][0];default:return k0[r.q][1]}break;case 104:switch(t.length){case 1:case 2:o=1+(r.H+11)%12,l=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:o=r.H,l=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:o=r.M,l=t.length;break;default:throw"bad minute format: "+t}break;case 115:if(t!="s"&&t!="ss"&&t!=".0"&&t!=".00"&&t!=".000")throw"bad second format: "+t;return r.u===0&&(t=="s"||t=="ss")?Mr(r.S,t.length):(n>=2?s=n===3?1e3:100:s=n===1?10:1,i=Math.round(s*(r.S+r.u)),i>=60*s&&(i=0),t==="s"?i===0?"0":""+i/s:(a=Mr(i,2+n),t==="ss"?a.substr(0,2):"."+a.substr(2,t.length-1)));case 90:switch(t){case"[h]":case"[hh]":o=r.D*24+r.H;break;case"[m]":case"[mm]":o=(r.D*24+r.H)*60+r.M;break;case"[s]":case"[ss]":o=((r.D*24+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}l=t.length===3?1:2;break;case 101:o=f,l=1;break}var u=l>0?Mr(o,l):"";return u}function rt(e){var t=3;if(e.length<=t)return e;for(var r=e.length%t,n=e.substr(0,r);r!=e.length;r+=t)n+=(n.length>0?",":"")+e.substr(r,t);return n}var Ea=/%/g;function Bs(e,t,r){var n=t.replace(Ea,""),a=t.length-n.length;return Xr(e,n,r*Math.pow(10,2*a))+Ge("%",a)}function Ms(e,t,r){for(var n=t.length-1;t.charCodeAt(n-1)===44;)--n;return Xr(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function Ta(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+Ta(e,-t);var a=e.indexOf(".");a===-1&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a),r.indexOf("e")===-1){var s=Math.floor(Math.log(t)*Math.LOG10E);for(r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i);r.substr(0,2)==="0.";)r=r.charAt(0)+r.substr(2,a)+"."+r.substr(2+a),r=r.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(f,o,l,u){return o+l+u.substr(0,(a+i)%a)+"."+u.substr(i)+"E"})}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var wa=/# (\?+)( ?)\/( ?)(\d+)/;function bs(e,t,r){var n=parseInt(e[4],10),a=Math.round(t*n),i=Math.floor(a/n),s=a-i*n,f=n;return r+(i===0?"":""+i)+" "+(s===0?Ge(" ",e[1].length+1+e[4].length):f0(s,e[1].length)+e[2]+"/"+e[3]+Mr(f,e[4].length))}function Us(e,t,r){return r+(t===0?"":""+t)+Ge(" ",e[1].length+2+e[4].length)}var Aa=/^#*0*\.([0#]+)/,Sa=/\).*[0#]/,Fa=/\(###\) ###\\?-####/;function vr(e){for(var t="",r,n=0;n!=e.length;++n)switch(r=e.charCodeAt(n)){case 35:break;case 63:t+=" ";break;case 48:t+="0";break;default:t+=String.fromCharCode(r)}return t}function L0(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function B0(e,t){var r=e-Math.floor(e),n=Math.pow(10,t);return t<(""+Math.round(r*n)).length?0:Math.round(r*n)}function Ws(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}function Vs(e){return e<2147483647&&e>-2147483648?""+(e>=0?e|0:e-1|0):""+Math.floor(e)}function Rr(e,t,r){if(e.charCodeAt(0)===40&&!t.match(Sa)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Rr("n",n,r):"("+Rr("n",n,-r)+")"}if(t.charCodeAt(t.length-1)===44)return Ms(e,t,r);if(t.indexOf("%")!==-1)return Bs(e,t,r);if(t.indexOf("E")!==-1)return Ta(t,r);if(t.charCodeAt(0)===36)return"$"+Rr(e,t.substr(t.charAt(1)==" "?2:1),r);var a,i,s,f,o=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+Tt(o,t.length);if(t.match(/^[#?]+$/))return a=Tt(r,0),a==="0"&&(a=""),a.length>t.length?a:vr(t.substr(0,t.length-a.length))+a;if(i=t.match(wa))return bs(i,o,l);if(t.match(/^#+0+$/))return l+Tt(o,t.length-t.indexOf("0"));if(i=t.match(Aa))return a=L0(r,i[1].length).replace(/^([^\.]+)$/,"$1."+vr(i[1])).replace(/\.$/,"."+vr(i[1])).replace(/\.(\d*)$/,function(g,h){return"."+h+Ge("0",vr(i[1]).length-h.length)}),t.indexOf("0.")!==-1?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return l+L0(o,i[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return l+rt(Tt(o,0));if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Rr(e,t,-r):rt(""+(Math.floor(r)+Ws(r,i[1].length)))+"."+Mr(B0(r,i[1].length),i[1].length);if(i=t.match(/^#,#*,#0/))return Rr(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=Ct(Rr(e,t.replace(/[\\-]/g,""),r)),s=0,Ct(Ct(t.replace(/\\/g,"")).replace(/[0#]/g,function(g){return s<a.length?a.charAt(s++):g==="0"?"0":""}));if(t.match(Fa))return a=Rr(e,"##########",r),"("+a.substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var u="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),f=Tn(o,Math.pow(10,s)-1,!1),a=""+l,u=Xr("n",i[1],f[1]),u.charAt(u.length-1)==" "&&(u=u.substr(0,u.length-1)+"0"),a+=u+i[2]+"/"+i[3],u=_n(f[2],s),u.length<i[4].length&&(u=vr(i[4].substr(i[4].length-u.length))+u),a+=u,a;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f=Tn(o,Math.pow(10,s)-1,!0),l+(f[0]||(f[1]?"":"0"))+" "+(f[1]?f0(f[1],s)+i[2]+"/"+i[3]+_n(f[2],s):Ge(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=Tt(r,0),t.length<=a.length?a:vr(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0?]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=a.indexOf(".");var x=t.indexOf(".")-s,d=t.length-a.length-x;return vr(t.substr(0,x)+a+t.substr(t.length-d))}if(i=t.match(/^00,000\.([#0]*0)$/))return s=B0(r,i[1].length),r<0?"-"+Rr(e,t,-r):rt(Vs(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(g){return"00,"+(g.length<3?Mr(0,3-g.length):"")+g})+"."+Mr(s,i[1].length);switch(t){case"###,##0.00":return Rr(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var p=rt(Tt(o,0));return p!=="0"?l+p:"";case"###,###.00":return Rr(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return Rr(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function Hs(e,t,r){for(var n=t.length-1;t.charCodeAt(n-1)===44;)--n;return Xr(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function Gs(e,t,r){var n=t.replace(Ea,""),a=t.length-n.length;return Xr(e,n,r*Math.pow(10,2*a))+Ge("%",a)}function Ca(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+Ca(e,-t);var a=e.indexOf(".");a===-1&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a),!r.match(/[Ee]/)){var s=Math.floor(Math.log(t)*Math.LOG10E);r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(f,o,l,u){return o+l+u.substr(0,(a+i)%a)+"."+u.substr(i)+"E"})}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function Ur(e,t,r){if(e.charCodeAt(0)===40&&!t.match(Sa)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Ur("n",n,r):"("+Ur("n",n,-r)+")"}if(t.charCodeAt(t.length-1)===44)return Hs(e,t,r);if(t.indexOf("%")!==-1)return Gs(e,t,r);if(t.indexOf("E")!==-1)return Ca(t,r);if(t.charCodeAt(0)===36)return"$"+Ur(e,t.substr(t.charAt(1)==" "?2:1),r);var a,i,s,f,o=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+Mr(o,t.length);if(t.match(/^[#?]+$/))return a=""+r,r===0&&(a=""),a.length>t.length?a:vr(t.substr(0,t.length-a.length))+a;if(i=t.match(wa))return Us(i,o,l);if(t.match(/^#+0+$/))return l+Mr(o,t.length-t.indexOf("0"));if(i=t.match(Aa))return a=(""+r).replace(/^([^\.]+)$/,"$1."+vr(i[1])).replace(/\.$/,"."+vr(i[1])),a=a.replace(/\.(\d*)$/,function(g,h){return"."+h+Ge("0",vr(i[1]).length-h.length)}),t.indexOf("0.")!==-1?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return l+(""+o).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return l+rt(""+o);if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Ur(e,t,-r):rt(""+r)+"."+Ge("0",i[1].length);if(i=t.match(/^#,#*,#0/))return Ur(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=Ct(Ur(e,t.replace(/[\\-]/g,""),r)),s=0,Ct(Ct(t.replace(/\\/g,"")).replace(/[0#]/g,function(g){return s<a.length?a.charAt(s++):g==="0"?"0":""}));if(t.match(Fa))return a=Ur(e,"##########",r),"("+a.substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var u="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),f=Tn(o,Math.pow(10,s)-1,!1),a=""+l,u=Xr("n",i[1],f[1]),u.charAt(u.length-1)==" "&&(u=u.substr(0,u.length-1)+"0"),a+=u+i[2]+"/"+i[3],u=_n(f[2],s),u.length<i[4].length&&(u=vr(i[4].substr(i[4].length-u.length))+u),a+=u,a;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f=Tn(o,Math.pow(10,s)-1,!0),l+(f[0]||(f[1]?"":"0"))+" "+(f[1]?f0(f[1],s)+i[2]+"/"+i[3]+_n(f[2],s):Ge(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=""+r,t.length<=a.length?a:vr(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=a.indexOf(".");var x=t.indexOf(".")-s,d=t.length-a.length-x;return vr(t.substr(0,x)+a+t.substr(t.length-d))}if(i=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+Ur(e,t,-r):rt(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(g){return"00,"+(g.length<3?Mr(0,3-g.length):"")+g})+"."+Mr(0,i[1].length);switch(t){case"###,###":case"##,###":case"#,###":var p=rt(""+o);return p!=="0"?l+p:"";default:if(t.match(/\.[0#?]*$/))return Ur(e,t.slice(0,t.lastIndexOf(".")),r)+vr(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function Xr(e,t,r){return(r|0)===r?Ur(e,t,r):Rr(e,t,r)}function Xs(e){for(var t=[],r=!1,n=0,a=0;n<e.length;++n)switch(e.charCodeAt(n)){case 34:r=!r;break;case 95:case 42:case 92:++n;break;case 59:t[t.length]=e.substr(a,n-a),a=n+1}if(t[t.length]=e.substr(a),r===!0)throw new Error("Format |"+e+"| unterminated string ");return t}var ya=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function Oa(e){for(var t=0,r="",n="";t<e.length;)switch(r=e.charAt(t)){case"G":En(e,t)&&(t+=6),t++;break;case'"':for(;e.charCodeAt(++t)!==34&&t<e.length;);++t;break;case"\\":t+=2;break;case"_":t+=2;break;case"@":++t;break;case"B":case"b":if(e.charAt(t+1)==="1"||e.charAt(t+1)==="2")return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"\u4E0A":if(e.substr(t,3).toUpperCase()==="A/P"||e.substr(t,5).toUpperCase()==="AM/PM"||e.substr(t,5).toUpperCase()==="\u4E0A\u5348/\u4E0B\u5348")return!0;++t;break;case"[":for(n=r;e.charAt(t++)!=="]"&&t<e.length;)n+=e.charAt(t);if(n.match(ya))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||r=="\\"&&e.charAt(t+1)=="-"&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t,(e.charAt(t)==" "||e.charAt(t)=="*")&&++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;case" ":++t;break;default:++t;break}return!1}function zs(e,t,r,n){for(var a=[],i="",s=0,f="",o="t",l,u,x,d="H";s<e.length;)switch(f=e.charAt(s)){case"G":if(!En(e,s))throw new Error("unrecognized character "+f+" in "+e);a[a.length]={t:"G",v:"General"},s+=7;break;case'"':for(i="";(x=e.charCodeAt(++s))!==34&&s<e.length;)i+=String.fromCharCode(x);a[a.length]={t:"t",v:i},++s;break;case"\\":var p=e.charAt(++s),g=p==="("||p===")"?p:"t";a[a.length]={t:g,v:p},++s;break;case"_":a[a.length]={t:"t",v:" "},s+=2;break;case"@":a[a.length]={t:"T",v:t},++s;break;case"B":case"b":if(e.charAt(s+1)==="1"||e.charAt(s+1)==="2"){if(l==null&&(l=hn(t,r,e.charAt(s+1)==="2"),l==null))return"";a[a.length]={t:"X",v:e.substr(s,2)},o=f,s+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0||l==null&&(l=hn(t,r),l==null))return"";for(i=f;++s<e.length&&e.charAt(s).toLowerCase()===f;)i+=f;f==="m"&&o.toLowerCase()==="h"&&(f="M"),f==="h"&&(f=d),a[a.length]={t:f,v:i},o=f;break;case"A":case"a":case"\u4E0A":var h={t:f,v:f};if(l==null&&(l=hn(t,r)),e.substr(s,3).toUpperCase()==="A/P"?(l!=null&&(h.v=l.H>=12?"P":"A"),h.t="T",d="h",s+=3):e.substr(s,5).toUpperCase()==="AM/PM"?(l!=null&&(h.v=l.H>=12?"PM":"AM"),h.t="T",s+=5,d="h"):e.substr(s,5).toUpperCase()==="\u4E0A\u5348/\u4E0B\u5348"?(l!=null&&(h.v=l.H>=12?"\u4E0B\u5348":"\u4E0A\u5348"),h.t="T",s+=5,d="h"):(h.t="t",++s),l==null&&h.t==="T")return"";a[a.length]=h,o=f;break;case"[":for(i=f;e.charAt(s++)!=="]"&&s<e.length;)i+=e.charAt(s);if(i.slice(-1)!=="]")throw'unterminated "[" block: |'+i+"|";if(i.match(ya)){if(l==null&&(l=hn(t,r),l==null))return"";a[a.length]={t:"Z",v:i.toLowerCase()},o=i.charAt(1)}else i.indexOf("$")>-1&&(i=(i.match(/\$([^-\[\]]*)/)||[])[1]||"$",Oa(e)||(a[a.length]={t:"t",v:i}));break;case".":if(l!=null){for(i=f;++s<e.length&&(f=e.charAt(s))==="0";)i+=f;a[a.length]={t:"s",v:i};break}case"0":case"#":for(i=f;++s<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(s))>-1;)i+=f;a[a.length]={t:"n",v:i};break;case"?":for(i=f;e.charAt(++s)===f;)i+=f;a[a.length]={t:f,v:i},o=f;break;case"*":++s,(e.charAt(s)==" "||e.charAt(s)=="*")&&++s;break;case"(":case")":a[a.length]={t:n===1?"t":f,v:f},++s;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(i=f;s<e.length&&"0123456789".indexOf(e.charAt(++s))>-1;)i+=e.charAt(s);a[a.length]={t:"D",v:i};break;case" ":a[a.length]={t:f,v:f},++s;break;case"$":a[a.length]={t:"t",v:"$"},++s;break;default:if(",$-+/():!^&'~{}<>=\u20ACacfijklopqrtuvwxzP".indexOf(f)===-1)throw new Error("unrecognized character "+f+" in "+e);a[a.length]={t:"t",v:f},++s;break}var _=0,N=0,O;for(s=a.length-1,o="t";s>=0;--s)switch(a[s].t){case"h":case"H":a[s].t=d,o="h",_<1&&(_=1);break;case"s":(O=a[s].v.match(/\.0+$/))&&(N=Math.max(N,O[0].length-1)),_<3&&(_=3);case"d":case"y":case"M":case"e":o=a[s].t;break;case"m":o==="s"&&(a[s].t="M",_<2&&(_=2));break;case"X":break;case"Z":_<1&&a[s].v.match(/[Hh]/)&&(_=1),_<2&&a[s].v.match(/[Mm]/)&&(_=2),_<3&&a[s].v.match(/[Ss]/)&&(_=3)}switch(_){case 0:break;case 1:l.u>=.5&&(l.u=0,++l.S),l.S>=60&&(l.S=0,++l.M),l.M>=60&&(l.M=0,++l.H);break;case 2:l.u>=.5&&(l.u=0,++l.S),l.S>=60&&(l.S=0,++l.M);break}var F="",R;for(s=0;s<a.length;++s)switch(a[s].t){case"t":case"T":case" ":case"D":break;case"X":a[s].v="",a[s].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":a[s].v=Ls(a[s].t.charCodeAt(0),a[s].v,l,N),a[s].t="t";break;case"n":case"?":for(R=s+1;a[R]!=null&&((f=a[R].t)==="?"||f==="D"||(f===" "||f==="t")&&a[R+1]!=null&&(a[R+1].t==="?"||a[R+1].t==="t"&&a[R+1].v==="/")||a[s].t==="("&&(f===" "||f==="n"||f===")")||f==="t"&&(a[R].v==="/"||a[R].v===" "&&a[R+1]!=null&&a[R+1].t=="?"));)a[s].v+=a[R].v,a[R]={v:"",t:";"},++R;F+=a[s].v,s=R-1;break;case"G":a[s].t="t",a[s].v=n0(t,r);break}var q="",ne,D;if(F.length>0){F.charCodeAt(0)==40?(ne=t<0&&F.charCodeAt(0)===45?-t:t,D=Xr("n",F,ne)):(ne=t<0&&n>1?-t:t,D=Xr("n",F,ne),ne<0&&a[0]&&a[0].t=="t"&&(D=D.substr(1),a[0].v="-"+a[0].v)),R=D.length-1;var W=a.length;for(s=0;s<a.length;++s)if(a[s]!=null&&a[s].t!="t"&&a[s].v.indexOf(".")>-1){W=s;break}var M=a.length;if(W===a.length&&D.indexOf("E")===-1){for(s=a.length-1;s>=0;--s)a[s]==null||"n?".indexOf(a[s].t)===-1||(R>=a[s].v.length-1?(R-=a[s].v.length,a[s].v=D.substr(R+1,a[s].v.length)):R<0?a[s].v="":(a[s].v=D.substr(0,R+1),R=-1),a[s].t="t",M=s);R>=0&&M<a.length&&(a[M].v=D.substr(0,R+1)+a[M].v)}else if(W!==a.length&&D.indexOf("E")===-1){for(R=D.indexOf(".")-1,s=W;s>=0;--s)if(!(a[s]==null||"n?".indexOf(a[s].t)===-1)){for(u=a[s].v.indexOf(".")>-1&&s===W?a[s].v.indexOf(".")-1:a[s].v.length-1,q=a[s].v.substr(u+1);u>=0;--u)R>=0&&(a[s].v.charAt(u)==="0"||a[s].v.charAt(u)==="#")&&(q=D.charAt(R--)+q);a[s].v=q,a[s].t="t",M=s}for(R>=0&&M<a.length&&(a[M].v=D.substr(0,R+1)+a[M].v),R=D.indexOf(".")+1,s=W;s<a.length;++s)if(!(a[s]==null||"n?(".indexOf(a[s].t)===-1&&s!==W)){for(u=a[s].v.indexOf(".")>-1&&s===W?a[s].v.indexOf(".")+1:0,q=a[s].v.substr(0,u);u<a[s].v.length;++u)R<D.length&&(q+=D.charAt(R++));a[s].v=q,a[s].t="t",M=s}}}for(s=0;s<a.length;++s)a[s]!=null&&"n?".indexOf(a[s].t)>-1&&(ne=n>1&&t<0&&s>0&&a[s-1].v==="-"?-t:t,a[s].v=Xr(a[s].t,a[s].v,ne),a[s].t="t");var X="";for(s=0;s!==a.length;++s)a[s]!=null&&(X+=a[s].v);return X}var M0=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function b0(e,t){if(t==null)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0;break}return!1}function $s(e,t){var r=Xs(e),n=r.length,a=r[n-1].indexOf("@");if(n<4&&a>-1&&--n,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if(typeof t!="number")return[4,r.length===4||a>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=a>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=a>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=a>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"];break}var i=t>0?r[0]:t<0?r[1]:r[2];if(r[0].indexOf("[")===-1&&r[1].indexOf("[")===-1)return[n,i];if(r[0].match(/\[[=<>]/)!=null||r[1].match(/\[[=<>]/)!=null){var s=r[0].match(M0),f=r[1].match(M0);return b0(t,s)?[n,r[0]]:b0(t,f)?[n,r[1]]:[n,r[s!=null&&f!=null?2:1]]}return[n,i]}function tt(e,t,r){r==null&&(r={});var n="";switch(typeof e){case"string":e=="m/d/yy"&&r.dateNF?n=r.dateNF:n=e;break;case"number":e==14&&r.dateNF?n=r.dateNF:n=(r.table!=null?r.table:Xe)[e],n==null&&(n=r.table&&r.table[P0[e]]||Xe[P0[e]]),n==null&&(n=ys[e]||"General");break}if(En(n,0))return n0(t,r);t instanceof Date&&(t=_a(t,r.date1904));var a=$s(n,t);if(En(a[1]))return n0(t,r);if(t===!0)t="TRUE";else if(t===!1)t="FALSE";else if(t===""||t==null)return"";return zs(a[1],t,r,a[0])}function Da(e,t){if(typeof t!="number"){t=+t||-1;for(var r=0;r<392;++r){if(Xe[r]==null){t<0&&(t=r);continue}if(Xe[r]==e){t=r;break}}t<0&&(t=391)}return Xe[t]=e,t}function kn(e){for(var t=0;t!=392;++t)e[t]!==void 0&&Da(e[t],t)}function Pn(){Xe=Cs()}var Na=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function Ks(e){var t=typeof e=="number"?Xe[e]:e;return t=t.replace(Na,"(\\d+)"),new RegExp("^"+t+"$")}function Ys(e,t,r){var n=-1,a=-1,i=-1,s=-1,f=-1,o=-1;(t.match(Na)||[]).forEach(function(x,d){var p=parseInt(r[d+1],10);switch(x.toLowerCase().charAt(0)){case"y":n=p;break;case"d":i=p;break;case"h":s=p;break;case"s":o=p;break;case"m":s>=0?f=p:a=p;break}}),o>=0&&f==-1&&a>=0&&(f=a,a=-1);var l=(""+(n>=0?n:new Date().getFullYear())).slice(-4)+"-"+("00"+(a>=1?a:1)).slice(-2)+"-"+("00"+(i>=1?i:1)).slice(-2);l.length==7&&(l="0"+l),l.length==8&&(l="20"+l);var u=("00"+(s>=0?s:0)).slice(-2)+":"+("00"+(f>=0?f:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2);return s==-1&&f==-1&&o==-1?l:n==-1&&a==-1&&i==-1?u:l+"T"+u}var js=function(){var e={};e.version="1.2.0";function t(){for(var D=0,W=new Array(256),M=0;M!=256;++M)D=M,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,W[M]=D;return typeof Int32Array!="undefined"?new Int32Array(W):W}var r=t();function n(D){var W=0,M=0,X=0,z=typeof Int32Array!="undefined"?new Int32Array(4096):new Array(4096);for(X=0;X!=256;++X)z[X]=D[X];for(X=0;X!=256;++X)for(M=D[X],W=256+X;W<4096;W+=256)M=z[W]=M>>>8^D[M&255];var K=[];for(X=1;X!=16;++X)K[X-1]=typeof Int32Array!="undefined"?z.subarray(X*256,X*256+256):z.slice(X*256,X*256+256);return K}var a=n(r),i=a[0],s=a[1],f=a[2],o=a[3],l=a[4],u=a[5],x=a[6],d=a[7],p=a[8],g=a[9],h=a[10],_=a[11],N=a[12],O=a[13],F=a[14];function R(D,W){for(var M=W^-1,X=0,z=D.length;X<z;)M=M>>>8^r[(M^D.charCodeAt(X++))&255];return~M}function q(D,W){for(var M=W^-1,X=D.length-15,z=0;z<X;)M=F[D[z++]^M&255]^O[D[z++]^M>>8&255]^N[D[z++]^M>>16&255]^_[D[z++]^M>>>24]^h[D[z++]]^g[D[z++]]^p[D[z++]]^d[D[z++]]^x[D[z++]]^u[D[z++]]^l[D[z++]]^o[D[z++]]^f[D[z++]]^s[D[z++]]^i[D[z++]]^r[D[z++]];for(X+=15;z<X;)M=M>>>8^r[(M^D[z++])&255];return~M}function ne(D,W){for(var M=W^-1,X=0,z=D.length,K=0,ie=0;X<z;)K=D.charCodeAt(X++),K<128?M=M>>>8^r[(M^K)&255]:K<2048?(M=M>>>8^r[(M^(192|K>>6&31))&255],M=M>>>8^r[(M^(128|K&63))&255]):K>=55296&&K<57344?(K=(K&1023)+64,ie=D.charCodeAt(X++)&1023,M=M>>>8^r[(M^(240|K>>8&7))&255],M=M>>>8^r[(M^(128|K>>2&63))&255],M=M>>>8^r[(M^(128|ie>>6&15|(K&3)<<4))&255],M=M>>>8^r[(M^(128|ie&63))&255]):(M=M>>>8^r[(M^(224|K>>12&15))&255],M=M>>>8^r[(M^(128|K>>6&63))&255],M=M>>>8^r[(M^(128|K&63))&255]);return~M}return e.table=r,e.bstr=R,e.buf=q,e.str=ne,e}(),ke=function(){var t={};t.version="1.2.1";function r(c,E){for(var m=c.split("/"),v=E.split("/"),T=0,w=0,k=Math.min(m.length,v.length);T<k;++T){if(w=m[T].length-v[T].length)return w;if(m[T]!=v[T])return m[T]<v[T]?-1:1}return m.length-v.length}function n(c){if(c.charAt(c.length-1)=="/")return c.slice(0,-1).indexOf("/")===-1?c:n(c.slice(0,-1));var E=c.lastIndexOf("/");return E===-1?c:c.slice(0,E+1)}function a(c){if(c.charAt(c.length-1)=="/")return a(c.slice(0,-1));var E=c.lastIndexOf("/");return E===-1?c:c.slice(E+1)}function i(c,E){typeof E=="string"&&(E=new Date(E));var m=E.getHours();m=m<<6|E.getMinutes(),m=m<<5|E.getSeconds()>>>1,c.write_shift(2,m);var v=E.getFullYear()-1980;v=v<<4|E.getMonth()+1,v=v<<5|E.getDate(),c.write_shift(2,v)}function s(c){var E=c.read_shift(2)&65535,m=c.read_shift(2)&65535,v=new Date,T=m&31;m>>>=5;var w=m&15;m>>>=4,v.setMilliseconds(0),v.setFullYear(m+1980),v.setMonth(w-1),v.setDate(T);var k=E&31;E>>>=5;var U=E&63;return E>>>=6,v.setHours(E),v.setMinutes(U),v.setSeconds(k<<1),v}function f(c){yr(c,0);for(var E={},m=0;c.l<=c.length-4;){var v=c.read_shift(2),T=c.read_shift(2),w=c.l+T,k={};switch(v){case 21589:m=c.read_shift(1),m&1&&(k.mtime=c.read_shift(4)),T>5&&(m&2&&(k.atime=c.read_shift(4)),m&4&&(k.ctime=c.read_shift(4))),k.mtime&&(k.mt=new Date(k.mtime*1e3));break}c.l=w,E[v]=k}return E}var o;function l(){return o||(o={})}function u(c,E){if(c[0]==80&&c[1]==75)return sn(c,E);if((c[0]|32)==109&&(c[1]|32)==105)return ts(c,E);if(c.length<512)throw new Error("CFB file size "+c.length+" < 512");var m=3,v=512,T=0,w=0,k=0,U=0,I=0,P=[],L=c.slice(0,512);yr(L,0);var j=x(L);switch(m=j[0],m){case 3:v=512;break;case 4:v=4096;break;case 0:if(j[1]==0)return sn(c,E);default:throw new Error("Major Version: Expected 3 or 4 saw "+m)}v!==512&&(L=c.slice(0,v),yr(L,28));var re=c.slice(0,v);d(L,m);var fe=L.read_shift(4,"i");if(m===3&&fe!==0)throw new Error("# Directory Sectors: Expected 0 saw "+fe);L.l+=4,k=L.read_shift(4,"i"),L.l+=4,L.chk("00100000","Mini Stream Cutoff Size: "),U=L.read_shift(4,"i"),T=L.read_shift(4,"i"),I=L.read_shift(4,"i"),w=L.read_shift(4,"i");for(var J=-1,se=0;se<109&&(J=L.read_shift(4,"i"),!(J<0));++se)P[se]=J;var _e=p(c,v);_(I,w,_e,v,P);var Ve=O(_e,k,P,v);Ve[k].name="!Directory",T>0&&U!==ie&&(Ve[U].name="!MiniFAT"),Ve[P[0]].name="!FAT",Ve.fat_addrs=P,Ve.ssz=v;var He={},ur=[],Lt=[],Bt=[];F(k,Ve,_e,ur,T,He,Lt,U),g(Lt,Bt,ur),ur.shift();var Mt={FileIndex:Lt,FullPaths:Bt};return E&&E.raw&&(Mt.raw={header:re,sectors:_e}),Mt}function x(c){if(c[c.l]==80&&c[c.l+1]==75)return[0,0];c.chk(Ae,"Header Signature: "),c.l+=16;var E=c.read_shift(2,"u");return[c.read_shift(2,"u"),E]}function d(c,E){var m=9;switch(c.l+=2,m=c.read_shift(2)){case 9:if(E!=3)throw new Error("Sector Shift: Expected 9 saw "+m);break;case 12:if(E!=4)throw new Error("Sector Shift: Expected 12 saw "+m);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+m)}c.chk("0600","Mini Sector Shift: "),c.chk("000000000000","Reserved: ")}function p(c,E){for(var m=Math.ceil(c.length/E)-1,v=[],T=1;T<m;++T)v[T-1]=c.slice(T*E,(T+1)*E);return v[m-1]=c.slice(m*E),v}function g(c,E,m){for(var v=0,T=0,w=0,k=0,U=0,I=m.length,P=[],L=[];v<I;++v)P[v]=L[v]=v,E[v]=m[v];for(;U<L.length;++U)v=L[U],T=c[v].L,w=c[v].R,k=c[v].C,P[v]===v&&(T!==-1&&P[T]!==T&&(P[v]=P[T]),w!==-1&&P[w]!==w&&(P[v]=P[w])),k!==-1&&(P[k]=v),T!==-1&&v!=P[v]&&(P[T]=P[v],L.lastIndexOf(T)<U&&L.push(T)),w!==-1&&v!=P[v]&&(P[w]=P[v],L.lastIndexOf(w)<U&&L.push(w));for(v=1;v<I;++v)P[v]===v&&(w!==-1&&P[w]!==w?P[v]=P[w]:T!==-1&&P[T]!==T&&(P[v]=P[T]));for(v=1;v<I;++v)if(c[v].type!==0){if(U=v,U!=P[U])do U=P[U],E[v]=E[U]+"/"+E[v];while(U!==0&&P[U]!==-1&&U!=P[U]);P[v]=-1}for(E[0]+="/",v=1;v<I;++v)c[v].type!==2&&(E[v]+="/")}function h(c,E,m){for(var v=c.start,T=c.size,w=[],k=v;m&&T>0&&k>=0;)w.push(E.slice(k*K,k*K+K)),T-=K,k=ct(m,k*4);return w.length===0?b(0):ir(w).slice(0,c.size)}function _(c,E,m,v,T){var w=ie;if(c===ie){if(E!==0)throw new Error("DIFAT chain shorter than expected")}else if(c!==-1){var k=m[c],U=(v>>>2)-1;if(!k)return;for(var I=0;I<U&&(w=ct(k,I*4))!==ie;++I)T.push(w);_(ct(k,v-4),E-1,m,v,T)}}function N(c,E,m,v,T){var w=[],k=[];T||(T=[]);var U=v-1,I=0,P=0;for(I=E;I>=0;){T[I]=!0,w[w.length]=I,k.push(c[I]);var L=m[Math.floor(I*4/v)];if(P=I*4&U,v<4+P)throw new Error("FAT boundary crossed: "+I+" 4 "+v);if(!c[L])break;I=ct(c[L],P)}return{nodes:w,data:$0([k])}}function O(c,E,m,v){var T=c.length,w=[],k=[],U=[],I=[],P=v-1,L=0,j=0,re=0,fe=0;for(L=0;L<T;++L)if(U=[],re=L+E,re>=T&&(re-=T),!k[re]){I=[];var J=[];for(j=re;j>=0;){J[j]=!0,k[j]=!0,U[U.length]=j,I.push(c[j]);var se=m[Math.floor(j*4/v)];if(fe=j*4&P,v<4+fe)throw new Error("FAT boundary crossed: "+j+" 4 "+v);if(!c[se]||(j=ct(c[se],fe),J[j]))break}w[re]={nodes:U,data:$0([I])}}return w}function F(c,E,m,v,T,w,k,U){for(var I=0,P=v.length?2:0,L=E[c].data,j=0,re=0,fe;j<L.length;j+=128){var J=L.slice(j,j+128);yr(J,64),re=J.read_shift(2),fe=x0(J,0,re-P),v.push(fe);var se={name:fe,type:J.read_shift(1),color:J.read_shift(1),L:J.read_shift(4,"i"),R:J.read_shift(4,"i"),C:J.read_shift(4,"i"),clsid:J.read_shift(16),state:J.read_shift(4,"i"),start:0,size:0},_e=J.read_shift(2)+J.read_shift(2)+J.read_shift(2)+J.read_shift(2);_e!==0&&(se.ct=R(J,J.l-8));var Ve=J.read_shift(2)+J.read_shift(2)+J.read_shift(2)+J.read_shift(2);Ve!==0&&(se.mt=R(J,J.l-8)),se.start=J.read_shift(4,"i"),se.size=J.read_shift(4,"i"),se.size<0&&se.start<0&&(se.size=se.type=0,se.start=ie,se.name=""),se.type===5?(I=se.start,T>0&&I!==ie&&(E[I].name="!StreamData")):se.size>=4096?(se.storage="fat",E[se.start]===void 0&&(E[se.start]=N(m,se.start,E.fat_addrs,E.ssz)),E[se.start].name=se.name,se.content=E[se.start].data.slice(0,se.size)):(se.storage="minifat",se.size<0?se.size=0:I!==ie&&se.start!==ie&&E[I]&&(se.content=h(se,E[I].data,(E[U]||{}).data))),se.content&&yr(se.content,0),w[fe]=se,k.push(se)}}function R(c,E){return new Date((Dr(c,E+4)/1e7*Math.pow(2,32)+Dr(c,E)/1e7-11644473600)*1e3)}function q(c,E){return l(),u(o.readFileSync(c),E)}function ne(c,E){var m=E&&E.type;switch(m||ye&&Buffer.isBuffer(c)&&(m="buffer"),m||"base64"){case"file":return q(c,E);case"base64":return u(Br(Kr(c)),E);case"binary":return u(Br(c),E)}return u(c,E)}function D(c,E){var m=E||{},v=m.root||"Root Entry";if(c.FullPaths||(c.FullPaths=[]),c.FileIndex||(c.FileIndex=[]),c.FullPaths.length!==c.FileIndex.length)throw new Error("inconsistent CFB structure");c.FullPaths.length===0&&(c.FullPaths[0]=v+"/",c.FileIndex[0]={name:v,type:5}),m.CLSID&&(c.FileIndex[0].clsid=m.CLSID),W(c)}function W(c){var E="Sh33tJ5";if(!ke.find(c,"/"+E)){var m=b(4);m[0]=55,m[1]=m[3]=50,m[2]=54,c.FileIndex.push({name:E,type:2,content:m,size:4,L:69,R:69,C:69}),c.FullPaths.push(c.FullPaths[0]+E),M(c)}}function M(c,E){D(c);for(var m=!1,v=!1,T=c.FullPaths.length-1;T>=0;--T){var w=c.FileIndex[T];switch(w.type){case 0:v?m=!0:(c.FileIndex.pop(),c.FullPaths.pop());break;case 1:case 2:case 5:v=!0,isNaN(w.R*w.L*w.C)&&(m=!0),w.R>-1&&w.L>-1&&w.R==w.L&&(m=!0);break;default:m=!0;break}}if(!(!m&&!E)){var k=new Date(1987,1,19),U=0,I=Object.create?Object.create(null):{},P=[];for(T=0;T<c.FullPaths.length;++T)I[c.FullPaths[T]]=!0,c.FileIndex[T].type!==0&&P.push([c.FullPaths[T],c.FileIndex[T]]);for(T=0;T<P.length;++T){var L=n(P[T][0]);v=I[L],v||(P.push([L,{name:a(L).replace("/",""),type:1,clsid:We,ct:k,mt:k,content:null}]),I[L]=!0)}for(P.sort(function(fe,J){return r(fe[0],J[0])}),c.FullPaths=[],c.FileIndex=[],T=0;T<P.length;++T)c.FullPaths[T]=P[T][0],c.FileIndex[T]=P[T][1];for(T=0;T<P.length;++T){var j=c.FileIndex[T],re=c.FullPaths[T];if(j.name=a(re).replace("/",""),j.L=j.R=j.C=-(j.color=1),j.size=j.content?j.content.length:0,j.start=0,j.clsid=j.clsid||We,T===0)j.C=P.length>1?1:-1,j.size=0,j.type=5;else if(re.slice(-1)=="/"){for(U=T+1;U<P.length&&n(c.FullPaths[U])!=re;++U);for(j.C=U>=P.length?-1:U,U=T+1;U<P.length&&n(c.FullPaths[U])!=n(re);++U);j.R=U>=P.length?-1:U,j.type=1}else n(c.FullPaths[T+1]||"")==n(re)&&(j.R=T+1),j.type=2}}}function X(c,E){var m=E||{};if(m.fileType=="mad")return ns(c,m);switch(M(c),m.fileType){case"zip":return qi(c,m)}var v=function(fe){for(var J=0,se=0,_e=0;_e<fe.FileIndex.length;++_e){var Ve=fe.FileIndex[_e];if(!!Ve.content){var He=Ve.content.length;He>0&&(He<4096?J+=He+63>>6:se+=He+511>>9)}}for(var ur=fe.FullPaths.length+3>>2,Lt=J+7>>3,Bt=J+127>>7,Mt=Lt+se+ur+Bt,ft=Mt+127>>7,Gn=ft<=109?0:Math.ceil((ft-109)/127);Mt+ft+Gn+127>>7>ft;)Gn=++ft<=109?0:Math.ceil((ft-109)/127);var Hr=[1,Gn,ft,Bt,ur,se,J,0];return fe.FileIndex[0].size=J<<6,Hr[7]=(fe.FileIndex[0].start=Hr[0]+Hr[1]+Hr[2]+Hr[3]+Hr[4]+Hr[5])+(Hr[6]+7>>3),Hr}(c),T=b(v[7]<<9),w=0,k=0;{for(w=0;w<8;++w)T.write_shift(1,me[w]);for(w=0;w<8;++w)T.write_shift(2,0);for(T.write_shift(2,62),T.write_shift(2,3),T.write_shift(2,65534),T.write_shift(2,9),T.write_shift(2,6),w=0;w<3;++w)T.write_shift(2,0);for(T.write_shift(4,0),T.write_shift(4,v[2]),T.write_shift(4,v[0]+v[1]+v[2]+v[3]-1),T.write_shift(4,0),T.write_shift(4,1<<12),T.write_shift(4,v[3]?v[0]+v[1]+v[2]-1:ie),T.write_shift(4,v[3]),T.write_shift(-4,v[1]?v[0]-1:ie),T.write_shift(4,v[1]),w=0;w<109;++w)T.write_shift(-4,w<v[2]?v[1]+w:-1)}if(v[1])for(k=0;k<v[1];++k){for(;w<236+k*127;++w)T.write_shift(-4,w<v[2]?v[1]+w:-1);T.write_shift(-4,k===v[1]-1?ie:k+1)}var U=function(fe){for(k+=fe;w<k-1;++w)T.write_shift(-4,w+1);fe&&(++w,T.write_shift(-4,ie))};for(k=w=0,k+=v[1];w<k;++w)T.write_shift(-4,Pe.DIFSECT);for(k+=v[2];w<k;++w)T.write_shift(-4,Pe.FATSECT);U(v[3]),U(v[4]);for(var I=0,P=0,L=c.FileIndex[0];I<c.FileIndex.length;++I)L=c.FileIndex[I],L.content&&(P=L.content.length,!(P<4096)&&(L.start=k,U(P+511>>9)));for(U(v[6]+7>>3);T.l&511;)T.write_shift(-4,Pe.ENDOFCHAIN);for(k=w=0,I=0;I<c.FileIndex.length;++I)L=c.FileIndex[I],L.content&&(P=L.content.length,!(!P||P>=4096)&&(L.start=k,U(P+63>>6)));for(;T.l&511;)T.write_shift(-4,Pe.ENDOFCHAIN);for(w=0;w<v[4]<<2;++w){var j=c.FullPaths[w];if(!j||j.length===0){for(I=0;I<17;++I)T.write_shift(4,0);for(I=0;I<3;++I)T.write_shift(4,-1);for(I=0;I<12;++I)T.write_shift(4,0);continue}L=c.FileIndex[w],w===0&&(L.start=L.size?L.start-1:ie);var re=w===0&&m.root||L.name;if(P=2*(re.length+1),T.write_shift(64,re,"utf16le"),T.write_shift(2,P),T.write_shift(1,L.type),T.write_shift(1,L.color),T.write_shift(-4,L.L),T.write_shift(-4,L.R),T.write_shift(-4,L.C),L.clsid)T.write_shift(16,L.clsid,"hex");else for(I=0;I<4;++I)T.write_shift(4,0);T.write_shift(4,L.state||0),T.write_shift(4,0),T.write_shift(4,0),T.write_shift(4,0),T.write_shift(4,0),T.write_shift(4,L.start),T.write_shift(4,L.size),T.write_shift(4,0)}for(w=1;w<c.FileIndex.length;++w)if(L=c.FileIndex[w],L.size>=4096)if(T.l=L.start+1<<9,ye&&Buffer.isBuffer(L.content))L.content.copy(T,T.l,0,L.size),T.l+=L.size+511&-512;else{for(I=0;I<L.size;++I)T.write_shift(1,L.content[I]);for(;I&511;++I)T.write_shift(1,0)}for(w=1;w<c.FileIndex.length;++w)if(L=c.FileIndex[w],L.size>0&&L.size<4096)if(ye&&Buffer.isBuffer(L.content))L.content.copy(T,T.l,0,L.size),T.l+=L.size+63&-64;else{for(I=0;I<L.size;++I)T.write_shift(1,L.content[I]);for(;I&63;++I)T.write_shift(1,0)}if(ye)T.l=T.length;else for(;T.l<T.length;)T.write_shift(1,0);return T}function z(c,E){var m=c.FullPaths.map(function(I){return I.toUpperCase()}),v=m.map(function(I){var P=I.split("/");return P[P.length-(I.slice(-1)=="/"?2:1)]}),T=!1;E.charCodeAt(0)===47?(T=!0,E=m[0].slice(0,-1)+E):T=E.indexOf("/")!==-1;var w=E.toUpperCase(),k=T===!0?m.indexOf(w):v.indexOf(w);if(k!==-1)return c.FileIndex[k];var U=!w.match(cn);for(w=w.replace(Wt,""),U&&(w=w.replace(cn,"!")),k=0;k<m.length;++k)if((U?m[k].replace(cn,"!"):m[k]).replace(Wt,"")==w||(U?v[k].replace(cn,"!"):v[k]).replace(Wt,"")==w)return c.FileIndex[k];return null}var K=64,ie=-2,Ae="d0cf11e0a1b11ae1",me=[208,207,17,224,161,177,26,225],We="00000000000000000000000000000000",Pe={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:ie,FREESECT:-1,HEADER_SIGNATURE:Ae,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:We,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function Er(c,E,m){l();var v=X(c,m);o.writeFileSync(E,v)}function ze(c){for(var E=new Array(c.length),m=0;m<c.length;++m)E[m]=String.fromCharCode(c[m]);return E.join("")}function dr(c,E){var m=X(c,E);switch(E&&E.type||"buffer"){case"file":return l(),o.writeFileSync(E.filename,m),m;case"binary":return typeof m=="string"?m:ze(m);case"base64":return Kt(typeof m=="string"?m:ze(m));case"buffer":if(ye)return Buffer.isBuffer(m)?m:jr(m);case"array":return typeof m=="string"?Br(m):m}return m}var pr;function S(c){try{var E=c.InflateRaw,m=new E;if(m._processChunk(new Uint8Array([3,0]),m._finishFlushFlag),m.bytesRead)pr=c;else throw new Error("zlib does not expose bytesRead")}catch(v){console.error("cannot use native zlib: "+(v.message||v))}}function B(c,E){if(!pr)return qr(c,E);var m=pr.InflateRaw,v=new m,T=v._processChunk(c.slice(c.l),v._finishFlushFlag);return c.l+=v.bytesRead,T}function y(c){return pr?pr.deflateRawSync(c):Me(c)}var C=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],G=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],ce=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function he(c){var E=(c<<1|c<<11)&139536|(c<<5|c<<15)&558144;return(E>>16|E>>8|E)&255}for(var ue=typeof Uint8Array!="undefined",ae=ue?new Uint8Array(1<<8):[],Oe=0;Oe<1<<8;++Oe)ae[Oe]=he(Oe);function we(c,E){var m=ae[c&255];return E<=8?m>>>8-E:(m=m<<8|ae[c>>8&255],E<=16?m>>>16-E:(m=m<<8|ae[c>>16&255],m>>>24-E))}function Qe(c,E){var m=E&7,v=E>>>3;return(c[v]|(m<=6?0:c[v+1]<<8))>>>m&3}function Fe(c,E){var m=E&7,v=E>>>3;return(c[v]|(m<=5?0:c[v+1]<<8))>>>m&7}function Sr(c,E){var m=E&7,v=E>>>3;return(c[v]|(m<=4?0:c[v+1]<<8))>>>m&15}function Be(c,E){var m=E&7,v=E>>>3;return(c[v]|(m<=3?0:c[v+1]<<8))>>>m&31}function le(c,E){var m=E&7,v=E>>>3;return(c[v]|(m<=1?0:c[v+1]<<8))>>>m&127}function Y(c,E,m){var v=E&7,T=E>>>3,w=(1<<m)-1,k=c[T]>>>v;return m<8-v||(k|=c[T+1]<<8-v,m<16-v)||(k|=c[T+2]<<16-v,m<24-v)||(k|=c[T+3]<<24-v),k&w}function A(c,E,m){var v=E&7,T=E>>>3;return v<=5?c[T]|=(m&7)<<v:(c[T]|=m<<v&255,c[T+1]=(m&7)>>8-v),E+3}function V(c,E,m){var v=E&7,T=E>>>3;return m=(m&1)<<v,c[T]|=m,E+1}function oe(c,E,m){var v=E&7,T=E>>>3;return m<<=v,c[T]|=m&255,m>>>=8,c[T+1]=m,E+8}function ge(c,E,m){var v=E&7,T=E>>>3;return m<<=v,c[T]|=m&255,m>>>=8,c[T+1]=m&255,c[T+2]=m>>>8,E+16}function Te(c,E){var m=c.length,v=2*m>E?2*m:E+5,T=0;if(m>=E)return c;if(ye){var w=R0(v);if(c.copy)c.copy(w);else for(;T<c.length;++T)w[T]=c[T];return w}else if(ue){var k=new Uint8Array(v);if(k.set)k.set(c);else for(;T<m;++T)k[T]=c[T];return k}return c.length=v,c}function Q(c){for(var E=new Array(c),m=0;m<c;++m)E[m]=0;return E}function de(c,E,m){var v=1,T=0,w=0,k=0,U=0,I=c.length,P=ue?new Uint16Array(32):Q(32);for(w=0;w<32;++w)P[w]=0;for(w=I;w<m;++w)c[w]=0;I=c.length;var L=ue?new Uint16Array(I):Q(I);for(w=0;w<I;++w)P[T=c[w]]++,v<T&&(v=T),L[w]=0;for(P[0]=0,w=1;w<=v;++w)P[w+16]=U=U+P[w-1]<<1;for(w=0;w<I;++w)U=c[w],U!=0&&(L[w]=P[U+16]++);var j=0;for(w=0;w<I;++w)if(j=c[w],j!=0)for(U=we(L[w],v)>>v-j,k=(1<<v+4-j)-1;k>=0;--k)E[U|k<<j]=j&15|w<<4;return v}var Le=ue?new Uint16Array(512):Q(512),ve=ue?new Uint16Array(32):Q(32);if(!ue){for(var pe=0;pe<512;++pe)Le[pe]=0;for(pe=0;pe<32;++pe)ve[pe]=0}(function(){for(var c=[],E=0;E<32;E++)c.push(5);de(c,ve,32);var m=[];for(E=0;E<=143;E++)m.push(8);for(;E<=255;E++)m.push(9);for(;E<=279;E++)m.push(7);for(;E<=287;E++)m.push(8);de(m,Le,288)})();var xe=function(){for(var E=ue?new Uint8Array(32768):[],m=0,v=0;m<ce.length-1;++m)for(;v<ce[m+1];++v)E[v]=m;for(;v<32768;++v)E[v]=29;var T=ue?new Uint8Array(259):[];for(m=0,v=0;m<G.length-1;++m)for(;v<G[m+1];++v)T[v]=m;function w(U,I){for(var P=0;P<U.length;){var L=Math.min(65535,U.length-P),j=P+L==U.length;for(I.write_shift(1,+j),I.write_shift(2,L),I.write_shift(2,~L&65535);L-- >0;)I[I.l++]=U[P++]}return I.l}function k(U,I){for(var P=0,L=0,j=ue?new Uint16Array(32768):[];L<U.length;){var re=Math.min(65535,U.length-L);if(re<10){for(P=A(I,P,+(L+re==U.length)),P&7&&(P+=8-(P&7)),I.l=P/8|0,I.write_shift(2,re),I.write_shift(2,~re&65535);re-- >0;)I[I.l++]=U[L++];P=I.l*8;continue}P=A(I,P,+(L+re==U.length)+2);for(var fe=0;re-- >0;){var J=U[L];fe=(fe<<5^J)&32767;var se=-1,_e=0;if((se=j[fe])&&(se|=L&-32768,se>L&&(se-=32768),se<L))for(;U[se+_e]==U[L+_e]&&_e<250;)++_e;if(_e>2){J=T[_e],J<=22?P=oe(I,P,ae[J+1]>>1)-1:(oe(I,P,3),P+=5,oe(I,P,ae[J-23]>>5),P+=3);var Ve=J<8?0:J-4>>2;Ve>0&&(ge(I,P,_e-G[J]),P+=Ve),J=E[L-se],P=oe(I,P,ae[J]>>3),P-=3;var He=J<4?0:J-2>>1;He>0&&(ge(I,P,L-se-ce[J]),P+=He);for(var ur=0;ur<_e;++ur)j[fe]=L&32767,fe=(fe<<5^U[L])&32767,++L;re-=_e-1}else J<=143?J=J+48:P=V(I,P,1),P=oe(I,P,ae[J]),j[fe]=L&32767,++L}P=oe(I,P,0)-1}return I.l=(P+7)/8|0,I.l}return function(I,P){return I.length<8?w(I,P):k(I,P)}}();function Me(c){var E=b(50+Math.floor(c.length*1.1)),m=xe(c,E);return E.slice(0,m)}var Fr=ue?new Uint16Array(32768):Q(32768),mr=ue?new Uint16Array(32768):Q(32768),$e=ue?new Uint16Array(128):Q(128),or=1,Ke=1;function st(c,E){var m=Be(c,E)+257;E+=5;var v=Be(c,E)+1;E+=5;var T=Sr(c,E)+4;E+=4;for(var w=0,k=ue?new Uint8Array(19):Q(19),U=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],I=1,P=ue?new Uint8Array(8):Q(8),L=ue?new Uint8Array(8):Q(8),j=k.length,re=0;re<T;++re)k[C[re]]=w=Fe(c,E),I<w&&(I=w),P[w]++,E+=3;var fe=0;for(P[0]=0,re=1;re<=I;++re)L[re]=fe=fe+P[re-1]<<1;for(re=0;re<j;++re)(fe=k[re])!=0&&(U[re]=L[fe]++);var J=0;for(re=0;re<j;++re)if(J=k[re],J!=0){fe=ae[U[re]]>>8-J;for(var se=(1<<7-J)-1;se>=0;--se)$e[fe|se<<J]=J&7|re<<3}var _e=[];for(I=1;_e.length<m+v;)switch(fe=$e[le(c,E)],E+=fe&7,fe>>>=3){case 16:for(w=3+Qe(c,E),E+=2,fe=_e[_e.length-1];w-- >0;)_e.push(fe);break;case 17:for(w=3+Fe(c,E),E+=3;w-- >0;)_e.push(0);break;case 18:for(w=11+le(c,E),E+=7;w-- >0;)_e.push(0);break;default:_e.push(fe),I<fe&&(I=fe);break}var Ve=_e.slice(0,m),He=_e.slice(m);for(re=m;re<286;++re)Ve[re]=0;for(re=v;re<30;++re)He[re]=0;return or=de(Ve,Fr,286),Ke=de(He,mr,30),E}function Wn(c,E){if(c[0]==3&&!(c[1]&3))return[xt(E),2];for(var m=0,v=0,T=R0(E||1<<18),w=0,k=T.length>>>0,U=0,I=0;(v&1)==0;){if(v=Fe(c,m),m+=3,v>>>1==0){m&7&&(m+=8-(m&7));var P=c[m>>>3]|c[(m>>>3)+1]<<8;if(m+=32,P>0)for(!E&&k<w+P&&(T=Te(T,w+P),k=T.length);P-- >0;)T[w++]=c[m>>>3],m+=8;continue}else v>>1==1?(U=9,I=5):(m=st(c,m),U=or,I=Ke);for(;;){!E&&k<w+32767&&(T=Te(T,w+32767),k=T.length);var L=Y(c,m,U),j=v>>>1==1?Le[L]:Fr[L];if(m+=j&15,j>>>=4,(j>>>8&255)===0)T[w++]=j;else{if(j==256)break;j-=257;var re=j<8?0:j-4>>2;re>5&&(re=0);var fe=w+G[j];re>0&&(fe+=Y(c,m,re),m+=re),L=Y(c,m,I),j=v>>>1==1?ve[L]:mr[L],m+=j&15,j>>>=4;var J=j<4?0:j-2>>1,se=ce[j];for(J>0&&(se+=Y(c,m,J),m+=J),!E&&k<fe&&(T=Te(T,fe+100),k=T.length);w<fe;)T[w]=T[w-se],++w}}}return E?[T,m+7>>>3]:[T.slice(0,w),m+7>>>3]}function qr(c,E){var m=c.slice(c.l||0),v=Wn(m,E);return c.l+=v[1],v[0]}function an(c,E){if(c)typeof console!="undefined"&&console.error(E);else throw new Error(E)}function sn(c,E){var m=c;yr(m,0);var v=[],T=[],w={FileIndex:v,FullPaths:T};D(w,{root:E.root});for(var k=m.length-4;(m[k]!=80||m[k+1]!=75||m[k+2]!=5||m[k+3]!=6)&&k>=0;)--k;m.l=k+4,m.l+=4;var U=m.read_shift(2);m.l+=6;var I=m.read_shift(4);for(m.l=I,k=0;k<U;++k){m.l+=20;var P=m.read_shift(4),L=m.read_shift(4),j=m.read_shift(2),re=m.read_shift(2),fe=m.read_shift(2);m.l+=8;var J=m.read_shift(4),se=f(m.slice(m.l+j,m.l+j+re));m.l+=j+re+fe;var _e=m.l;m.l=J+4,Vn(m,P,L,w,se),m.l=_e}return w}function Vn(c,E,m,v,T){c.l+=2;var w=c.read_shift(2),k=c.read_shift(2),U=s(c);if(w&8257)throw new Error("Unsupported ZIP encryption");for(var I=c.read_shift(4),P=c.read_shift(4),L=c.read_shift(4),j=c.read_shift(2),re=c.read_shift(2),fe="",J=0;J<j;++J)fe+=String.fromCharCode(c[c.l++]);if(re){var se=f(c.slice(c.l,c.l+re));(se[21589]||{}).mt&&(U=se[21589].mt),((T||{})[21589]||{}).mt&&(U=T[21589].mt)}c.l+=re;var _e=c.slice(c.l,c.l+P);switch(k){case 8:_e=B(c,L);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+k)}var Ve=!1;w&8&&(I=c.read_shift(4),I==134695760&&(I=c.read_shift(4),Ve=!0),P=c.read_shift(4),L=c.read_shift(4)),P!=E&&an(Ve,"Bad compressed size: "+E+" != "+P),L!=m&&an(Ve,"Bad uncompressed size: "+m+" != "+L),Hn(v,fe,_e,{unsafe:!0,mt:U})}function qi(c,E){var m=E||{},v=[],T=[],w=b(1),k=m.compression?8:0,U=0,I=0,P=0,L=0,j=0,re=c.FullPaths[0],fe=re,J=c.FileIndex[0],se=[],_e=0;for(I=1;I<c.FullPaths.length;++I)if(fe=c.FullPaths[I].slice(re.length),J=c.FileIndex[I],!(!J.size||!J.content||fe=="Sh33tJ5")){var Ve=L,He=b(fe.length);for(P=0;P<fe.length;++P)He.write_shift(1,fe.charCodeAt(P)&127);He=He.slice(0,He.l),se[j]=js.buf(J.content,0);var ur=J.content;k==8&&(ur=y(ur)),w=b(30),w.write_shift(4,67324752),w.write_shift(2,20),w.write_shift(2,U),w.write_shift(2,k),J.mt?i(w,J.mt):w.write_shift(4,0),w.write_shift(-4,se[j]),w.write_shift(4,ur.length),w.write_shift(4,J.content.length),w.write_shift(2,He.length),w.write_shift(2,0),L+=w.length,v.push(w),L+=He.length,v.push(He),L+=ur.length,v.push(ur),w=b(46),w.write_shift(4,33639248),w.write_shift(2,0),w.write_shift(2,20),w.write_shift(2,U),w.write_shift(2,k),w.write_shift(4,0),w.write_shift(-4,se[j]),w.write_shift(4,ur.length),w.write_shift(4,J.content.length),w.write_shift(2,He.length),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(4,0),w.write_shift(4,Ve),_e+=w.l,T.push(w),_e+=He.length,T.push(He),++j}return w=b(22),w.write_shift(4,101010256),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(2,j),w.write_shift(2,j),w.write_shift(4,_e),w.write_shift(4,L),w.write_shift(2,0),ir([ir(v),ir(T),w])}var fn={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function Ji(c,E){if(c.ctype)return c.ctype;var m=c.name||"",v=m.match(/\.([^\.]+)$/);return v&&fn[v[1]]||E&&(v=(m=E).match(/[\.\\]([^\.\\])+$/),v&&fn[v[1]])?fn[v[1]]:"application/octet-stream"}function Zi(c){for(var E=Kt(c),m=[],v=0;v<E.length;v+=76)m.push(E.slice(v,v+76));return m.join(`\r
`)+`\r
`}function Qi(c){var E=c.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(P){var L=P.charCodeAt(0).toString(16).toUpperCase();return"="+(L.length==1?"0"+L:L)});E=E.replace(/ $/mg,"=20").replace(/\t$/mg,"=09"),E.charAt(0)==`
`&&(E="=0D"+E.slice(1)),E=E.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,`
=0A`).replace(/([^\r\n])\n/mg,"$1=0A");for(var m=[],v=E.split(`\r
`),T=0;T<v.length;++T){var w=v[T];if(w.length==0){m.push("");continue}for(var k=0;k<w.length;){var U=76,I=w.slice(k,k+U);I.charAt(U-1)=="="?U--:I.charAt(U-2)=="="?U-=2:I.charAt(U-3)=="="&&(U-=3),I=w.slice(k,k+U),k+=U,k<w.length&&(I+="="),m.push(I)}}return m.join(`\r
`)}function es(c){for(var E=[],m=0;m<c.length;++m){for(var v=c[m];m<=c.length&&v.charAt(v.length-1)=="=";)v=v.slice(0,v.length-1)+c[++m];E.push(v)}for(var T=0;T<E.length;++T)E[T]=E[T].replace(/[=][0-9A-Fa-f]{2}/g,function(w){return String.fromCharCode(parseInt(w.slice(1),16))});return Br(E.join(`\r
`))}function rs(c,E,m){for(var v="",T="",w="",k,U=0;U<10;++U){var I=E[U];if(!I||I.match(/^\s*$/))break;var P=I.match(/^(.*?):\s*([^\s].*)$/);if(P)switch(P[1].toLowerCase()){case"content-location":v=P[2].trim();break;case"content-type":w=P[2].trim();break;case"content-transfer-encoding":T=P[2].trim();break}}switch(++U,T.toLowerCase()){case"base64":k=Br(Kr(E.slice(U).join("")));break;case"quoted-printable":k=es(E.slice(U));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+T)}var L=Hn(c,v.slice(m.length),k,{unsafe:!0});w&&(L.ctype=w)}function ts(c,E){if(ze(c.slice(0,13)).toLowerCase()!="mime-version:")throw new Error("Unsupported MAD header");var m=E&&E.root||"",v=(ye&&Buffer.isBuffer(c)?c.toString("binary"):ze(c)).split(`\r
`),T=0,w="";for(T=0;T<v.length;++T)if(w=v[T],!!/^Content-Location:/i.test(w)&&(w=w.slice(w.indexOf("file")),m||(m=w.slice(0,w.lastIndexOf("/")+1)),w.slice(0,m.length)!=m))for(;m.length>0&&(m=m.slice(0,m.length-1),m=m.slice(0,m.lastIndexOf("/")+1),w.slice(0,m.length)!=m););var k=(v[1]||"").match(/boundary="(.*?)"/);if(!k)throw new Error("MAD cannot find boundary");var U="--"+(k[1]||""),I=[],P=[],L={FileIndex:I,FullPaths:P};D(L);var j,re=0;for(T=0;T<v.length;++T){var fe=v[T];fe!==U&&fe!==U+"--"||(re++&&rs(L,v.slice(j,T),m),j=T)}return L}function ns(c,E){var m=E||{},v=m.boundary||"SheetJS";v="------="+v;for(var T=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+v.slice(2)+'"',"","",""],w=c.FullPaths[0],k=w,U=c.FileIndex[0],I=1;I<c.FullPaths.length;++I)if(k=c.FullPaths[I].slice(w.length),U=c.FileIndex[I],!(!U.size||!U.content||k=="Sh33tJ5")){k=k.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(_e){return"_x"+_e.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(_e){return"_u"+_e.charCodeAt(0).toString(16)+"_"});for(var P=U.content,L=ye&&Buffer.isBuffer(P)?P.toString("binary"):ze(P),j=0,re=Math.min(1024,L.length),fe=0,J=0;J<=re;++J)(fe=L.charCodeAt(J))>=32&&fe<128&&++j;var se=j>=re*4/5;T.push(v),T.push("Content-Location: "+(m.root||"file:///C:/SheetJS/")+k),T.push("Content-Transfer-Encoding: "+(se?"quoted-printable":"base64")),T.push("Content-Type: "+Ji(U,k)),T.push(""),T.push(se?Qi(L):Zi(L))}return T.push(v+`--\r
`),T.join(`\r
`)}function as(c){var E={};return D(E,c),E}function Hn(c,E,m,v){var T=v&&v.unsafe;T||D(c);var w=!T&&ke.find(c,E);if(!w){var k=c.FullPaths[0];E.slice(0,k.length)==k?k=E:(k.slice(-1)!="/"&&(k+="/"),k=(k+E).replace("//","/")),w={name:a(E),type:2},c.FileIndex.push(w),c.FullPaths.push(k),T||ke.utils.cfb_gc(c)}return w.content=m,w.size=m?m.length:0,v&&(v.CLSID&&(w.clsid=v.CLSID),v.mt&&(w.mt=v.mt),v.ct&&(w.ct=v.ct)),w}function is(c,E){D(c);var m=ke.find(c,E);if(m){for(var v=0;v<c.FileIndex.length;++v)if(c.FileIndex[v]==m)return c.FileIndex.splice(v,1),c.FullPaths.splice(v,1),!0}return!1}function ss(c,E,m){D(c);var v=ke.find(c,E);if(v){for(var T=0;T<c.FileIndex.length;++T)if(c.FileIndex[T]==v)return c.FileIndex[T].name=a(m),c.FullPaths[T]=m,!0}return!1}function fs(c){M(c,!0)}return t.find=z,t.read=ne,t.parse=u,t.write=dr,t.writeFile=Er,t.utils={cfb_new:as,cfb_add:Hn,cfb_del:is,cfb_mov:ss,cfb_gc:fs,ReadShift:Ht,CheckField:Ya,prep_blob:yr,bconcat:ir,use_zlib:S,_deflateRaw:Me,_inflateRaw:qr,consts:Pe},t}();function qs(e){return typeof e=="string"?In(e):Array.isArray(e)?ws(e):e}function Qt(e,t,r){if(typeof Deno!="undefined"){if(r&&typeof t=="string")switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=In(t);break;default:throw new Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var n=r=="utf8"?Gr(t):t;if(typeof IE_SaveFile!="undefined")return IE_SaveFile(n,e);if(typeof Blob!="undefined"){var a=new Blob([qs(n)],{type:"application/octet-stream"});if(typeof navigator!="undefined"&&navigator.msSaveBlob)return navigator.msSaveBlob(a,e);if(typeof saveAs!="undefined")return saveAs(a,e);if(typeof URL!="undefined"&&typeof document!="undefined"&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(a);if(typeof chrome=="object"&&typeof(chrome.downloads||{}).download=="function")return URL.revokeObjectURL&&typeof setTimeout!="undefined"&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),chrome.downloads.download({url:i,filename:e,saveAs:!0});var s=document.createElement("a");if(s.download!=null)return s.download=e,s.href=i,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL&&typeof setTimeout!="undefined"&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),i}}if(typeof $!="undefined"&&typeof File!="undefined"&&typeof Folder!="undefined")try{var f=File(e);return f.open("w"),f.encoding="binary",Array.isArray(t)&&(t=Zt(t)),f.write(t),f.close(),t}catch(o){if(!o.message||!o.message.match(/onstruct/))throw o}throw new Error("cannot save file "+e)}function lr(e){for(var t=Object.keys(e),r=[],n=0;n<t.length;++n)Object.prototype.hasOwnProperty.call(e,t[n])&&r.push(t[n]);return r}function U0(e,t){for(var r=[],n=lr(e),a=0;a!==n.length;++a)r[e[n[a]][t]]==null&&(r[e[n[a]][t]]=n[a]);return r}function o0(e){for(var t=[],r=lr(e),n=0;n!==r.length;++n)t[e[r[n]]]=r[n];return t}function Ln(e){for(var t=[],r=lr(e),n=0;n!==r.length;++n)t[e[r[n]]]=parseInt(r[n],10);return t}function Js(e){for(var t=[],r=lr(e),n=0;n!==r.length;++n)t[e[r[n]]]==null&&(t[e[r[n]]]=[]),t[e[r[n]]].push(r[n]);return t}var wn=new Date(1899,11,30,0,0,0);function wr(e,t){var r=e.getTime();t&&(r-=1462*24*60*60*1e3);var n=wn.getTime()+(e.getTimezoneOffset()-wn.getTimezoneOffset())*6e4;return(r-n)/(24*60*60*1e3)}var Ra=new Date,Zs=wn.getTime()+(Ra.getTimezoneOffset()-wn.getTimezoneOffset())*6e4,W0=Ra.getTimezoneOffset();function Ia(e){var t=new Date;return t.setTime(e*24*60*60*1e3+Zs),t.getTimezoneOffset()!==W0&&t.setTime(t.getTime()+(t.getTimezoneOffset()-W0)*6e4),t}var V0=new Date("2017-02-19T19:06:09.000Z"),ka=isNaN(V0.getFullYear())?new Date("2/19/17"):V0,Qs=ka.getFullYear()==2017;function _r(e,t){var r=new Date(e);if(Qs)return t>0?r.setTime(r.getTime()+r.getTimezoneOffset()*60*1e3):t<0&&r.setTime(r.getTime()-r.getTimezoneOffset()*60*1e3),r;if(e instanceof Date)return e;if(ka.getFullYear()==1917&&!isNaN(r.getFullYear())){var n=r.getFullYear();return e.indexOf(""+n)>-1||r.setFullYear(r.getFullYear()+100),r}var a=e.match(/\d+/g)||["2017","2","19","0","0","0"],i=new Date(+a[0],+a[1]-1,+a[2],+a[3]||0,+a[4]||0,+a[5]||0);return e.indexOf("Z")>-1&&(i=new Date(i.getTime()-i.getTimezoneOffset()*60*1e3)),i}function Bn(e,t){if(ye&&Buffer.isBuffer(e)){if(t){if(e[0]==255&&e[1]==254)return Gr(e.slice(2).toString("utf16le"));if(e[1]==254&&e[2]==255)return Gr(Ts(e.slice(2).toString("binary")))}return e.toString("binary")}if(typeof TextDecoder!="undefined")try{if(t){if(e[0]==255&&e[1]==254)return Gr(new TextDecoder("utf-16le").decode(e.slice(2)));if(e[0]==254&&e[1]==255)return Gr(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"\u20AC":"\x80","\u201A":"\x82",\u0192:"\x83","\u201E":"\x84","\u2026":"\x85","\u2020":"\x86","\u2021":"\x87","\u02C6":"\x88","\u2030":"\x89",\u0160:"\x8A","\u2039":"\x8B",\u0152:"\x8C",\u017D:"\x8E","\u2018":"\x91","\u2019":"\x92","\u201C":"\x93","\u201D":"\x94","\u2022":"\x95","\u2013":"\x96","\u2014":"\x97","\u02DC":"\x98","\u2122":"\x99",\u0161:"\x9A","\u203A":"\x9B",\u0153:"\x9C",\u017E:"\x9E",\u0178:"\x9F"};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(i){return r[i]||i})}catch{}for(var n=[],a=0;a!=e.length;++a)n.push(String.fromCharCode(e[a]));return n.join("")}function Ar(e){if(typeof JSON!="undefined"&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if(typeof e!="object"||e==null)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=Ar(e[r]));return t}function Ge(e,t){for(var r="";r.length<t;)r+=e;return r}function zr(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,n=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return!isNaN(t=Number(n))||(n=n.replace(/[(](.*)[)]/,function(a,i){return r=-r,i}),!isNaN(t=Number(n)))?t/r:t}var ef=["january","february","march","april","may","june","july","august","september","october","november","december"];function Yt(e){var t=new Date(e),r=new Date(NaN),n=t.getYear(),a=t.getMonth(),i=t.getDate();if(isNaN(i))return r;var s=e.toLowerCase();if(s.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if(s=s.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,""),s.length>3&&ef.indexOf(s)==-1)return r}else if(s.match(/[a-z]/))return r;return n<0||n>8099?r:(a>0||i>1)&&n!=101?t:e.match(/[^-0-9:,\/\\]/)?r:t}function Ee(e,t,r){if(e.FullPaths){if(typeof r=="string"){var n;return ye?n=jr(r):n=As(r),ke.utils.cfb_add(e,t,n)}ke.utils.cfb_add(e,t,r)}else e.file(t,r)}function u0(){return ke.utils.cfb_new()}var Je=`<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r
`,rf={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},c0=o0(rf),h0=/[&<>'"]/g,tf=/[\u0000-\u0008\u000b-\u001f]/g;function Re(e){var t=e+"";return t.replace(h0,function(r){return c0[r]}).replace(tf,function(r){return"_x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+"_"})}function H0(e){return Re(e).replace(/ /g,"_x0020_")}var Pa=/[\u0000-\u001f]/g;function nf(e){var t=e+"";return t.replace(h0,function(r){return c0[r]}).replace(/\n/g,"<br/>").replace(Pa,function(r){return"&#x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+";"})}function af(e){var t=e+"";return t.replace(h0,function(r){return c0[r]}).replace(Pa,function(r){return"&#x"+r.charCodeAt(0).toString(16).toUpperCase()+";"})}function sf(e){return e.replace(/(\r\n|[\r\n])/g,"&#10;")}function ff(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function qn(e){for(var t="",r=0,n=0,a=0,i=0,s=0,f=0;r<e.length;){if(n=e.charCodeAt(r++),n<128){t+=String.fromCharCode(n);continue}if(a=e.charCodeAt(r++),n>191&&n<224){s=(n&31)<<6,s|=a&63,t+=String.fromCharCode(s);continue}if(i=e.charCodeAt(r++),n<240){t+=String.fromCharCode((n&15)<<12|(a&63)<<6|i&63);continue}s=e.charCodeAt(r++),f=((n&7)<<18|(a&63)<<12|(i&63)<<6|s&63)-65536,t+=String.fromCharCode(55296+(f>>>10&1023)),t+=String.fromCharCode(56320+(f&1023))}return t}function G0(e){var t=xt(2*e.length),r,n,a=1,i=0,s=0,f;for(n=0;n<e.length;n+=a)a=1,(f=e.charCodeAt(n))<128?r=f:f<224?(r=(f&31)*64+(e.charCodeAt(n+1)&63),a=2):f<240?(r=(f&15)*4096+(e.charCodeAt(n+1)&63)*64+(e.charCodeAt(n+2)&63),a=3):(a=4,r=(f&7)*262144+(e.charCodeAt(n+1)&63)*4096+(e.charCodeAt(n+2)&63)*64+(e.charCodeAt(n+3)&63),r-=65536,s=55296+(r>>>10&1023),r=56320+(r&1023)),s!==0&&(t[i++]=s&255,t[i++]=s>>>8,s=0),t[i++]=r%256,t[i++]=r>>>8;return t.slice(0,i).toString("ucs2")}function X0(e){return jr(e,"binary").toString("utf8")}var xn="foo bar baz\xE2\x98\x83\xF0\x9F\x8D\xA3",Vt=ye&&(X0(xn)==qn(xn)&&X0||G0(xn)==qn(xn)&&G0)||qn,Gr=ye?function(e){return jr(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,n=0,a=0;r<e.length;)switch(n=e.charCodeAt(r++),!0){case n<128:t.push(String.fromCharCode(n));break;case n<2048:t.push(String.fromCharCode(192+(n>>6))),t.push(String.fromCharCode(128+(n&63)));break;case(n>=55296&&n<57344):n-=55296,a=e.charCodeAt(r++)-56320+(n<<10),t.push(String.fromCharCode(240+(a>>18&7))),t.push(String.fromCharCode(144+(a>>12&63))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(a&63)));break;default:t.push(String.fromCharCode(224+(n>>12))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(n&63)))}return t.join("")},lf=function(){var e=[["nbsp"," "],["middot","\xB7"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(t){return[new RegExp("&"+t[0]+";","ig"),t[1]]});return function(r){for(var n=r.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,`
`).replace(/<[^>]*>/g,""),a=0;a<e.length;++a)n=n.replace(e[a][0],e[a][1]);return n}}(),La=/(^\s|\s$|\n)/;function sr(e,t){return"<"+e+(t.match(La)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function jt(e){return lr(e).map(function(t){return" "+t+'="'+e[t]+'"'}).join("")}function Z(e,t,r){return"<"+e+(r!=null?jt(r):"")+(t!=null?(t.match(La)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function a0(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(r){if(t)throw r}return""}function of(e,t){switch(typeof e){case"string":var r=Z("vt:lpwstr",Re(e));return t&&(r=r.replace(/&quot;/g,"_x0022_")),r;case"number":return Z((e|0)==e?"vt:i4":"vt:r8",Re(String(e)));case"boolean":return Z("vt:bool",e?"true":"false")}if(e instanceof Date)return Z("vt:filetime",a0(e));throw new Error("Unable to serialize "+e)}var er={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",mx:"http://schemas.microsoft.com/office/mac/excel/2008/main",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",sjs:"http://schemas.openxmlformats.org/package/2006/sheetjs/core-properties",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},Rt=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],Or={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"};function uf(e,t){for(var r=1-2*(e[t+7]>>>7),n=((e[t+7]&127)<<4)+(e[t+6]>>>4&15),a=e[t+6]&15,i=5;i>=0;--i)a=a*256+e[t+i];return n==2047?a==0?r*(1/0):NaN:(n==0?n=-1022:(n-=1023,a+=Math.pow(2,52)),r*Math.pow(2,n-52)*a)}function cf(e,t,r){var n=(t<0||1/t==-1/0?1:0)<<7,a=0,i=0,s=n?-t:t;isFinite(s)?s==0?a=i=0:(a=Math.floor(Math.log(s)/Math.LN2),i=s*Math.pow(2,52-a),a<=-1023&&(!isFinite(i)||i<Math.pow(2,52))?a=-1022:(i-=Math.pow(2,52),a+=1023)):(a=2047,i=isNaN(t)?26985:0);for(var f=0;f<=5;++f,i/=256)e[r+f]=i&255;e[r+6]=(a&15)<<4|i&15,e[r+7]=a>>4|n}var z0=function(e){for(var t=[],r=10240,n=0;n<e[0].length;++n)if(e[0][n])for(var a=0,i=e[0][n].length;a<i;a+=r)t.push.apply(t,e[0][n].slice(a,a+r));return t},$0=ye?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(t){return Buffer.isBuffer(t)?t:jr(t)})):z0(e)}:z0,K0=function(e,t,r){for(var n=[],a=t;a<r;a+=2)n.push(String.fromCharCode(Ut(e,a)));return n.join("").replace(Wt,"")},x0=ye?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(Wt,""):K0(e,t,r)}:K0,Y0=function(e,t,r){for(var n=[],a=t;a<t+r;++a)n.push(("0"+e[a].toString(16)).slice(-2));return n.join("")},Ba=ye?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):Y0(e,t,r)}:Y0,j0=function(e,t,r){for(var n=[],a=t;a<r;a++)n.push(String.fromCharCode(St(e,a)));return n.join("")},en=ye?function(t,r,n){return Buffer.isBuffer(t)?t.toString("utf8",r,n):j0(t,r,n)}:j0,Ma=function(e,t){var r=Dr(e,t);return r>0?en(e,t+4,t+4+r-1):""},ba=Ma,Ua=function(e,t){var r=Dr(e,t);return r>0?en(e,t+4,t+4+r-1):""},Wa=Ua,Va=function(e,t){var r=2*Dr(e,t);return r>0?en(e,t+4,t+4+r-1):""},Ha=Va,Ga=function(t,r){var n=Dr(t,r);return n>0?x0(t,r+4,r+4+n):""},Xa=Ga,za=function(e,t){var r=Dr(e,t);return r>0?en(e,t+4,t+4+r):""},$a=za,Ka=function(e,t){return uf(e,t)},An=Ka,d0=function(t){return Array.isArray(t)||typeof Uint8Array!="undefined"&&t instanceof Uint8Array};ye&&(ba=function(t,r){if(!Buffer.isBuffer(t))return Ma(t,r);var n=t.readUInt32LE(r);return n>0?t.toString("utf8",r+4,r+4+n-1):""},Wa=function(t,r){if(!Buffer.isBuffer(t))return Ua(t,r);var n=t.readUInt32LE(r);return n>0?t.toString("utf8",r+4,r+4+n-1):""},Ha=function(t,r){if(!Buffer.isBuffer(t))return Va(t,r);var n=2*t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+n-1)},Xa=function(t,r){if(!Buffer.isBuffer(t))return Ga(t,r);var n=t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+n)},$a=function(t,r){if(!Buffer.isBuffer(t))return za(t,r);var n=t.readUInt32LE(r);return t.toString("utf8",r+4,r+4+n)},An=function(t,r){return Buffer.isBuffer(t)?t.readDoubleLE(r):Ka(t,r)},d0=function(t){return Buffer.isBuffer(t)||Array.isArray(t)||typeof Uint8Array!="undefined"&&t instanceof Uint8Array});var St=function(e,t){return e[t]},Ut=function(e,t){return e[t+1]*(1<<8)+e[t]},hf=function(e,t){var r=e[t+1]*256+e[t];return r<32768?r:(65535-r+1)*-1},Dr=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},ct=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},xf=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function Ht(e,t){var r="",n,a,i=[],s,f,o,l;switch(t){case"dbcs":if(l=this.l,ye&&Buffer.isBuffer(this))r=this.slice(this.l,this.l+2*e).toString("utf16le");else for(o=0;o<e;++o)r+=String.fromCharCode(Ut(this,l)),l+=2;e*=2;break;case"utf8":r=en(this,this.l,this.l+e);break;case"utf16le":e*=2,r=x0(this,this.l,this.l+e);break;case"wstr":return Ht.call(this,e,"dbcs");case"lpstr-ansi":r=ba(this,this.l),e=4+Dr(this,this.l);break;case"lpstr-cp":r=Wa(this,this.l),e=4+Dr(this,this.l);break;case"lpwstr":r=Ha(this,this.l),e=4+2*Dr(this,this.l);break;case"lpp4":e=4+Dr(this,this.l),r=Xa(this,this.l),e&2&&(e+=2);break;case"8lpp4":e=4+Dr(this,this.l),r=$a(this,this.l),e&3&&(e+=4-(e&3));break;case"cstr":for(e=0,r="";(s=St(this,this.l+e++))!==0;)i.push(un(s));r=i.join("");break;case"_wstr":for(e=0,r="";(s=Ut(this,this.l+e))!==0;)i.push(un(s)),e+=2;e+=2,r=i.join("");break;case"dbcs-cont":for(r="",l=this.l,o=0;o<e;++o){if(this.lens&&this.lens.indexOf(l)!==-1)return s=St(this,l),this.l=l+1,f=Ht.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),i.join("")+f;i.push(un(Ut(this,l))),l+=2}r=i.join(""),e*=2;break;case"cpstr":case"sbcs-cont":for(r="",l=this.l,o=0;o!=e;++o){if(this.lens&&this.lens.indexOf(l)!==-1)return s=St(this,l),this.l=l+1,f=Ht.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),i.join("")+f;i.push(un(St(this,l))),l+=1}r=i.join("");break;default:switch(e){case 1:return n=St(this,this.l),this.l++,n;case 2:return n=(t==="i"?hf:Ut)(this,this.l),this.l+=2,n;case 4:case-4:return t==="i"||(this[this.l+3]&128)===0?(n=(e>0?ct:xf)(this,this.l),this.l+=4,n):(a=Dr(this,this.l),this.l+=4,a);case 8:case-8:if(t==="f")return e==8?a=An(this,this.l):a=An([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,a;e=8;case 16:r=Ba(this,this.l,e);break}}return this.l+=e,r}var df=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},pf=function(e,t,r){e[r]=t&255,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},mf=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255};function vf(e,t,r){var n=0,a=0;if(r==="dbcs"){for(a=0;a!=t.length;++a)mf(this,t.charCodeAt(a),this.l+2*a);n=2*t.length}else if(r==="sbcs"){for(t=t.replace(/[^\x00-\x7F]/g,"_"),a=0;a!=t.length;++a)this[this.l+a]=t.charCodeAt(a)&255;n=t.length}else if(r==="hex"){for(;a<e;++a)this[this.l++]=parseInt(t.slice(2*a,2*a+2),16)||0;return this}else if(r==="utf16le"){var i=Math.min(this.l+e,this.length);for(a=0;a<Math.min(t.length,e);++a){var s=t.charCodeAt(a);this[this.l++]=s&255,this[this.l++]=s>>8}for(;this.l<i;)this[this.l++]=0;return this}else switch(e){case 1:n=1,this[this.l]=t&255;break;case 2:n=2,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255;break;case 3:n=3,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255,t>>>=8,this[this.l+2]=t&255;break;case 4:n=4,df(this,t,this.l);break;case 8:if(n=8,r==="f"){cf(this,t,this.l);break}case 16:break;case-4:n=4,pf(this,t,this.l);break}return this.l+=n,this}function Ya(e,t){var r=Ba(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function yr(e,t){e.l=t,e.read_shift=Ht,e.chk=Ya,e.write_shift=vf}function Vr(e,t){e.l+=t}function b(e){var t=xt(e);return yr(t,0),t}function Tr(){var e=[],t=ye?256:2048,r=function(l){var u=b(l);return yr(u,0),u},n=r(t),a=function(){!n||(n.length>n.l&&(n=n.slice(0,n.l),n.l=n.length),n.length>0&&e.push(n),n=null)},i=function(l){return n&&l<n.length-n.l?n:(a(),n=r(Math.max(l+1,t)))},s=function(){return a(),ir(e)},f=function(l){a(),n=l,n.l==null&&(n.l=n.length),i(t)};return{next:i,push:f,end:s,_bufs:e}}function H(e,t,r,n){var a=+t,i;if(!isNaN(a)){n||(n=ch[a].p||(r||[]).length||0),i=1+(a>=128?1:0)+1,n>=128&&++i,n>=16384&&++i,n>=2097152&&++i;var s=e.next(i);a<=127?s.write_shift(1,a):(s.write_shift(1,(a&127)+128),s.write_shift(1,a>>7));for(var f=0;f!=4;++f)if(n>=128)s.write_shift(1,(n&127)+128),n>>=7;else{s.write_shift(1,n);break}n>0&&d0(r)&&e.push(r)}}function Gt(e,t,r){var n=Ar(e);if(t.s?(n.cRel&&(n.c+=t.s.c),n.rRel&&(n.r+=t.s.r)):(n.cRel&&(n.c+=t.c),n.rRel&&(n.r+=t.r)),!r||r.biff<12){for(;n.c>=256;)n.c-=256;for(;n.r>=65536;)n.r-=65536}return n}function q0(e,t,r){var n=Ar(e);return n.s=Gt(n.s,t.s,r),n.e=Gt(n.e,t.s,r),n}function Xt(e,t){if(e.cRel&&e.c<0)for(e=Ar(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=Ar(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=Ie(e);return!e.cRel&&e.cRel!=null&&(r=Ef(r)),!e.rRel&&e.rRel!=null&&(r=gf(r)),r}function Jn(e,t){return e.s.r==0&&!e.s.rRel&&e.e.r==(t.biff>=12?1048575:t.biff>=8?65536:16384)&&!e.e.rRel?(e.s.cRel?"":"$")+hr(e.s.c)+":"+(e.e.cRel?"":"$")+hr(e.e.c):e.s.c==0&&!e.s.cRel&&e.e.c==(t.biff>=12?16383:255)&&!e.e.cRel?(e.s.rRel?"":"$")+fr(e.s.r)+":"+(e.e.rRel?"":"$")+fr(e.e.r):Xt(e.s,t.biff)+":"+Xt(e.e,t.biff)}function p0(e){return parseInt(_f(e),10)-1}function fr(e){return""+(e+1)}function gf(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function _f(e){return e.replace(/\$(\d+)$/,"$1")}function m0(e){for(var t=Tf(e),r=0,n=0;n!==t.length;++n)r=26*r+t.charCodeAt(n)-64;return r-1}function hr(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function Ef(e){return e.replace(/^([A-Z])/,"$$$1")}function Tf(e){return e.replace(/^\$([A-Z])/,"$1")}function wf(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function rr(e){for(var t=0,r=0,n=0;n<e.length;++n){var a=e.charCodeAt(n);a>=48&&a<=57?t=10*t+(a-48):a>=65&&a<=90&&(r=26*r+(a-64))}return{c:r-1,r:t-1}}function Ie(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function Nr(e){var t=e.indexOf(":");return t==-1?{s:rr(e),e:rr(e)}:{s:rr(e.slice(0,t)),e:rr(e.slice(t+1))}}function qe(e,t){return typeof t=="undefined"||typeof t=="number"?qe(e.s,e.e):(typeof e!="string"&&(e=Ie(e)),typeof t!="string"&&(t=Ie(t)),e==t?e:e+":"+t)}function Ue(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,n=0,a=0,i=e.length;for(r=0;n<i&&!((a=e.charCodeAt(n)-64)<1||a>26);++n)r=26*r+a;for(t.s.c=--r,r=0;n<i&&!((a=e.charCodeAt(n)-48)<0||a>9);++n)r=10*r+a;if(t.s.r=--r,n===i||a!=10)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++n,r=0;n!=i&&!((a=e.charCodeAt(n)-64)<1||a>26);++n)r=26*r+a;for(t.e.c=--r,r=0;n!=i&&!((a=e.charCodeAt(n)-48)<0||a>9);++n)r=10*r+a;return t.e.r=--r,t}function J0(e,t){var r=e.t=="d"&&t instanceof Date;if(e.z!=null)try{return e.w=tt(e.z,r?wr(t):t)}catch{}try{return e.w=tt((e.XF||{}).numFmtId||(r?14:0),r?wr(t):t)}catch{return""+t}}function Yr(e,t,r){return e==null||e.t==null||e.t=="z"?"":e.w!==void 0?e.w:(e.t=="d"&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),e.t=="e"?rn[e.v]||e.v:t==null?J0(e,e.v):J0(e,t))}function mt(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",n={};return n[r]=e,{SheetNames:[r],Sheets:n}}function ja(e,t,r){var n=r||{},a=e?Array.isArray(e):n.dense,i=e||(a?[]:{}),s=0,f=0;if(i&&n.origin!=null){if(typeof n.origin=="number")s=n.origin;else{var o=typeof n.origin=="string"?rr(n.origin):n.origin;s=o.r,f=o.c}i["!ref"]||(i["!ref"]="A1:A1")}var l={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(i["!ref"]){var u=Ue(i["!ref"]);l.s.c=u.s.c,l.s.r=u.s.r,l.e.c=Math.max(l.e.c,u.e.c),l.e.r=Math.max(l.e.r,u.e.r),s==-1&&(l.e.r=s=u.e.r+1)}for(var x=0;x!=t.length;++x)if(!!t[x]){if(!Array.isArray(t[x]))throw new Error("aoa_to_sheet expects an array of arrays");for(var d=0;d!=t[x].length;++d)if(typeof t[x][d]!="undefined"){var p={v:t[x][d]},g=s+x,h=f+d;if(l.s.r>g&&(l.s.r=g),l.s.c>h&&(l.s.c=h),l.e.r<g&&(l.e.r=g),l.e.c<h&&(l.e.c=h),t[x][d]&&typeof t[x][d]=="object"&&!Array.isArray(t[x][d])&&!(t[x][d]instanceof Date))p=t[x][d];else if(Array.isArray(p.v)&&(p.f=t[x][d][1],p.v=p.v[0]),p.v===null)if(p.f)p.t="n";else if(n.nullError)p.t="e",p.v=0;else if(n.sheetStubs)p.t="z";else continue;else typeof p.v=="number"?p.t="n":typeof p.v=="boolean"?p.t="b":p.v instanceof Date?(p.z=n.dateNF||Xe[14],n.cellDates?(p.t="d",p.w=tt(p.z,wr(p.v))):(p.t="n",p.v=wr(p.v),p.w=tt(p.z,p.v))):p.t="s";if(a)i[g]||(i[g]=[]),i[g][h]&&i[g][h].z&&(p.z=i[g][h].z),i[g][h]=p;else{var _=Ie({c:h,r:g});i[_]&&i[_].z&&(p.z=i[_].z),i[_]=p}}}return l.s.c<1e7&&(i["!ref"]=qe(l)),i}function It(e,t){return ja(null,e,t)}function Af(e){return e.read_shift(4,"i")}function br(e,t){return t||(t=b(4)),t.write_shift(4,e),t}function xr(e){var t=e.read_shift(4);return t===0?"":e.read_shift(t,"dbcs")}function tr(e,t){var r=!1;return t==null&&(r=!0,t=b(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function Sf(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function Ff(e,t){return t||(t=b(4)),t.write_shift(2,e.ich||0),t.write_shift(2,e.ifnt||0),t}function v0(e,t){var r=e.l,n=e.read_shift(1),a=xr(e),i=[],s={t:a,h:a};if((n&1)!==0){for(var f=e.read_shift(4),o=0;o!=f;++o)i.push(Sf(e));s.r=i}else s.r=[{ich:0,ifnt:0}];return e.l=r+t,s}function Cf(e,t){var r=!1;return t==null&&(r=!0,t=b(15+4*e.t.length)),t.write_shift(1,0),tr(e.t,t),r?t.slice(0,t.l):t}var yf=v0;function Of(e,t){var r=!1;return t==null&&(r=!0,t=b(23+4*e.t.length)),t.write_shift(1,1),tr(e.t,t),t.write_shift(4,1),Ff({ich:0,ifnt:0},t),r?t.slice(0,t.l):t}function kr(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function vt(e,t){return t==null&&(t=b(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function gt(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function _t(e,t){return t==null&&(t=b(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var Df=xr,qa=tr;function g0(e){var t=e.read_shift(4);return t===0||t===4294967295?"":e.read_shift(t,"dbcs")}function Sn(e,t){var r=!1;return t==null&&(r=!0,t=b(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var Nf=xr,i0=g0,_0=Sn;function Ja(e){var t=e.slice(e.l,e.l+4),r=t[0]&1,n=t[0]&2;e.l+=4;var a=n===0?An([0,0,0,0,t[0]&252,t[1],t[2],t[3]],0):ct(t,0)>>2;return r?a/100:a}function Za(e,t){t==null&&(t=b(4));var r=0,n=0,a=e*100;if(e==(e|0)&&e>=-(1<<29)&&e<1<<29?n=1:a==(a|0)&&a>=-(1<<29)&&a<1<<29&&(n=1,r=1),n)t.write_shift(-4,((r?a:e)<<2)+(r+2));else throw new Error("unsupported RkNumber "+e)}function Qa(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}function Rf(e,t){return t||(t=b(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t}var Et=Qa,kt=Rf;function Pt(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function dt(e,t){return(t||b(8)).write_shift(8,e,"f")}function If(e){var t={},r=e.read_shift(1),n=r>>>1,a=e.read_shift(1),i=e.read_shift(2,"i"),s=e.read_shift(1),f=e.read_shift(1),o=e.read_shift(1);switch(e.l++,n){case 0:t.auto=1;break;case 1:t.index=a;var l=Vf[a];l&&(t.rgb=la(l));break;case 2:t.rgb=la([s,f,o]);break;case 3:t.theme=a;break}return i!=0&&(t.tint=i>0?i/32767:i/32768),t}function Fn(e,t){if(t||(t=b(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;e.index!=null?(t.write_shift(1,2),t.write_shift(1,e.index)):e.theme!=null?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),!e.rgb||e.theme!=null)t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);else{var n=e.rgb||"FFFFFF";typeof n=="number"&&(n=("000000"+n.toString(16)).slice(-6)),t.write_shift(1,parseInt(n.slice(0,2),16)),t.write_shift(1,parseInt(n.slice(2,4),16)),t.write_shift(1,parseInt(n.slice(4,6),16)),t.write_shift(1,255)}return t}function kf(e){var t=e.read_shift(1);e.l++;var r={fBold:t&1,fItalic:t&2,fUnderline:t&4,fStrikeout:t&8,fOutline:t&16,fShadow:t&32,fCondense:t&64,fExtend:t&128};return r}function Pf(e,t){t||(t=b(2));var r=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);return t.write_shift(1,r),t.write_shift(1,0),t}var ei=2,Cr=3,dn=11,Cn=19,pn=64,Lf=65,Bf=71,Mf=4108,bf=4126,ar=80,Z0={1:{n:"CodePage",t:ei},2:{n:"Category",t:ar},3:{n:"PresentationFormat",t:ar},4:{n:"ByteCount",t:Cr},5:{n:"LineCount",t:Cr},6:{n:"ParagraphCount",t:Cr},7:{n:"SlideCount",t:Cr},8:{n:"NoteCount",t:Cr},9:{n:"HiddenCount",t:Cr},10:{n:"MultimediaClipCount",t:Cr},11:{n:"ScaleCrop",t:dn},12:{n:"HeadingPairs",t:Mf},13:{n:"TitlesOfParts",t:bf},14:{n:"Manager",t:ar},15:{n:"Company",t:ar},16:{n:"LinksUpToDate",t:dn},17:{n:"CharacterCount",t:Cr},19:{n:"SharedDoc",t:dn},22:{n:"HyperlinksChanged",t:dn},23:{n:"AppVersion",t:Cr,p:"version"},24:{n:"DigSig",t:Lf},26:{n:"ContentType",t:ar},27:{n:"ContentStatus",t:ar},28:{n:"Language",t:ar},29:{n:"Version",t:ar},255:{},2147483648:{n:"Locale",t:Cn},2147483651:{n:"Behavior",t:Cn},1919054434:{}},Q0={1:{n:"CodePage",t:ei},2:{n:"Title",t:ar},3:{n:"Subject",t:ar},4:{n:"Author",t:ar},5:{n:"Keywords",t:ar},6:{n:"Comments",t:ar},7:{n:"Template",t:ar},8:{n:"LastAuthor",t:ar},9:{n:"RevNumber",t:ar},10:{n:"EditTime",t:pn},11:{n:"LastPrinted",t:pn},12:{n:"CreatedDate",t:pn},13:{n:"ModifiedDate",t:pn},14:{n:"PageCount",t:Cr},15:{n:"WordCount",t:Cr},16:{n:"CharCount",t:Cr},17:{n:"Thumbnail",t:Bf},18:{n:"Application",t:ar},19:{n:"DocSecurity",t:Cr},255:{},2147483648:{n:"Locale",t:Cn},2147483651:{n:"Behavior",t:Cn},1919054434:{}};function Uf(e){return e.map(function(t){return[t>>16&255,t>>8&255,t&255]})}var Wf=Uf([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),Vf=Ar(Wf),rn={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},Hf={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},mn={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function ri(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function ti(e,t){var r=Js(Hf),n=[],a;n[n.length]=Je,n[n.length]=Z("Types",null,{xmlns:er.CT,"xmlns:xsd":er.xsd,"xmlns:xsi":er.xsi}),n=n.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(o){return Z("Default",null,{Extension:o[0],ContentType:o[1]})}));var i=function(o){e[o]&&e[o].length>0&&(a=e[o][0],n[n.length]=Z("Override",null,{PartName:(a[0]=="/"?"":"/")+a,ContentType:mn[o][t.bookType]||mn[o].xlsx}))},s=function(o){(e[o]||[]).forEach(function(l){n[n.length]=Z("Override",null,{PartName:(l[0]=="/"?"":"/")+l,ContentType:mn[o][t.bookType]||mn[o].xlsx})})},f=function(o){(e[o]||[]).forEach(function(l){n[n.length]=Z("Override",null,{PartName:(l[0]=="/"?"":"/")+l,ContentType:r[o][0]})})};return i("workbooks"),s("sheets"),s("charts"),f("themes"),["strs","styles"].forEach(i),["coreprops","extprops","custprops"].forEach(f),f("vba"),f("comments"),f("threadedcomments"),f("drawings"),s("metadata"),f("people"),n.length>2&&(n[n.length]="</Types>",n[1]=n[1].replace("/>",">")),n.join("")}var Ce={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function ni(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function yt(e){var t=[Je,Z("Relationships",null,{xmlns:er.RELS})];return lr(e["!id"]).forEach(function(r){t[t.length]=Z("Relationship",null,e["!id"][r])}),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function Ne(e,t,r,n,a,i){if(a||(a={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,a.Id="rId"+t,a.Type=n,a.Target=r,i?a.TargetMode=i:[Ce.HLINK,Ce.XPATH,Ce.XMISS].indexOf(a.Type)>-1&&(a.TargetMode="External"),e["!id"][a.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][a.Id]=a,e[("/"+a.Target).replace("//","/")]=a,t}function Gf(e){var t=[Je];t.push(`<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">
`),t.push(`  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>
`);for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+`"/>
`);return t.push("</manifest:manifest>"),t.join("")}function ea(e,t,r){return['  <rdf:Description rdf:about="'+e+`">
`,'    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+`"/>
`,`  </rdf:Description>
`].join("")}function Xf(e,t){return['  <rdf:Description rdf:about="'+e+`">
`,'    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+t+`"/>
`,`  </rdf:Description>
`].join("")}function zf(e){var t=[Je];t.push(`<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
`);for(var r=0;r!=e.length;++r)t.push(ea(e[r][0],e[r][1])),t.push(Xf("",e[r][0]));return t.push(ea("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}function ai(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+gn.version+"</meta:generator></office:meta></office:document-meta>"}var ht=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];function Zn(e,t,r,n,a){a[e]!=null||t==null||t===""||(a[e]=t,t=Re(t),n[n.length]=r?Z(e,t,r):sr(e,t))}function ii(e,t){var r=t||{},n=[Je,Z("cp:coreProperties",null,{"xmlns:cp":er.CORE_PROPS,"xmlns:dc":er.dc,"xmlns:dcterms":er.dcterms,"xmlns:dcmitype":er.dcmitype,"xmlns:xsi":er.xsi})],a={};if(!e&&!r.Props)return n.join("");e&&(e.CreatedDate!=null&&Zn("dcterms:created",typeof e.CreatedDate=="string"?e.CreatedDate:a0(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a),e.ModifiedDate!=null&&Zn("dcterms:modified",typeof e.ModifiedDate=="string"?e.ModifiedDate:a0(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a));for(var i=0;i!=ht.length;++i){var s=ht[i],f=r.Props&&r.Props[s[1]]!=null?r.Props[s[1]]:e?e[s[1]]:null;f===!0?f="1":f===!1?f="0":typeof f=="number"&&(f=String(f)),f!=null&&Zn(s[0],f,null,n,a)}return n.length>2&&(n[n.length]="</cp:coreProperties>",n[1]=n[1].replace("/>",">")),n.join("")}var Ot=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],si=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function fi(e){var t=[],r=Z;return e||(e={}),e.Application="SheetJS",t[t.length]=Je,t[t.length]=Z("Properties",null,{xmlns:er.EXT_PROPS,"xmlns:vt":er.vt}),Ot.forEach(function(n){if(e[n[1]]!==void 0){var a;switch(n[2]){case"string":a=Re(String(e[n[1]]));break;case"bool":a=e[n[1]]?"true":"false";break}a!==void 0&&(t[t.length]=r(n[0],a))}}),t[t.length]=r("HeadingPairs",r("vt:vector",r("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+r("vt:variant",r("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=r("TitlesOfParts",r("vt:vector",e.SheetNames.map(function(n){return"<vt:lpstr>"+Re(n)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}function li(e){var t=[Je,Z("Properties",null,{xmlns:er.CUST_PROPS,"xmlns:vt":er.vt})];if(!e)return t.join("");var r=1;return lr(e).forEach(function(a){++r,t[t.length]=Z("property",of(e[a],!0),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:Re(a)})}),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var ra={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function $f(e,t){var r=[];return lr(ra).map(function(n){for(var a=0;a<ht.length;++a)if(ht[a][1]==n)return ht[a];for(a=0;a<Ot.length;++a)if(Ot[a][1]==n)return Ot[a];throw n}).forEach(function(n){if(e[n[1]]!=null){var a=t&&t.Props&&t.Props[n[1]]!=null?t.Props[n[1]]:e[n[1]];switch(n[2]){case"date":a=new Date(a).toISOString().replace(/\.\d*Z/,"Z");break}typeof a=="number"?a=String(a):a===!0||a===!1?a=a?"1":"0":a instanceof Date&&(a=new Date(a).toISOString().replace(/\.\d*Z/,"")),r.push(sr(ra[n[1]]||n[1],a))}}),Z("DocumentProperties",r.join(""),{xmlns:Or.o})}function Kf(e,t){var r=["Worksheets","SheetNames"],n="CustomDocumentProperties",a=[];return e&&lr(e).forEach(function(i){if(!!Object.prototype.hasOwnProperty.call(e,i)){for(var s=0;s<ht.length;++s)if(i==ht[s][1])return;for(s=0;s<Ot.length;++s)if(i==Ot[s][1])return;for(s=0;s<r.length;++s)if(i==r[s])return;var f=e[i],o="string";typeof f=="number"?(o="float",f=String(f)):f===!0||f===!1?(o="boolean",f=f?"1":"0"):f=String(f),a.push(Z(H0(i),f,{"dt:dt":o}))}}),t&&lr(t).forEach(function(i){if(!!Object.prototype.hasOwnProperty.call(t,i)&&!(e&&Object.prototype.hasOwnProperty.call(e,i))){var s=t[i],f="string";typeof s=="number"?(f="float",s=String(s)):s===!0||s===!1?(f="boolean",s=s?"1":"0"):s instanceof Date?(f="dateTime.tz",s=s.toISOString()):s=String(s),a.push(Z(H0(i),s,{"dt:dt":f}))}}),"<"+n+' xmlns="'+Or.o+'">'+a.join("")+"</"+n+">"}function Yf(e){var t=typeof e=="string"?new Date(Date.parse(e)):e,r=t.getTime()/1e3+11644473600,n=r%Math.pow(2,32),a=(r-n)/Math.pow(2,32);n*=1e7,a*=1e7;var i=n/Math.pow(2,32)|0;i>0&&(n=n%Math.pow(2,32),a+=i);var s=b(8);return s.write_shift(4,n),s.write_shift(4,a),s}function ta(e,t){var r=b(4),n=b(4);switch(r.write_shift(4,e==80?31:e),e){case 3:n.write_shift(-4,t);break;case 5:n=b(8),n.write_shift(8,t,"f");break;case 11:n.write_shift(4,t?1:0);break;case 64:n=Yf(t);break;case 31:case 80:for(n=b(4+2*(t.length+1)+(t.length%2?0:2)),n.write_shift(4,t.length+1),n.write_shift(0,t,"dbcs");n.l!=n.length;)n.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return ir([r,n])}var oi=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function jf(e){switch(typeof e){case"boolean":return 11;case"number":return(e|0)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64;break}return-1}function na(e,t,r){var n=b(8),a=[],i=[],s=8,f=0,o=b(8),l=b(8);if(o.write_shift(4,2),o.write_shift(4,1200),l.write_shift(4,1),i.push(o),a.push(l),s+=8+o.length,!t){l=b(8),l.write_shift(4,0),a.unshift(l);var u=[b(4)];for(u[0].write_shift(4,e.length),f=0;f<e.length;++f){var x=e[f][0];for(o=b(4+4+2*(x.length+1)+(x.length%2?0:2)),o.write_shift(4,f+2),o.write_shift(4,x.length+1),o.write_shift(0,x,"dbcs");o.l!=o.length;)o.write_shift(1,0);u.push(o)}o=ir(u),i.unshift(o),s+=8+o.length}for(f=0;f<e.length;++f)if(!(t&&!t[e[f][0]])&&!(oi.indexOf(e[f][0])>-1||si.indexOf(e[f][0])>-1)&&e[f][1]!=null){var d=e[f][1],p=0;if(t){p=+t[e[f][0]];var g=r[p];if(g.p=="version"&&typeof d=="string"){var h=d.split(".");d=(+h[0]<<16)+(+h[1]||0)}o=ta(g.t,d)}else{var _=jf(d);_==-1&&(_=31,d=String(d)),o=ta(_,d)}i.push(o),l=b(8),l.write_shift(4,t?p:2+f),a.push(l),s+=8+o.length}var N=8*(i.length+1);for(f=0;f<i.length;++f)a[f].write_shift(4,N),N+=i[f].length;return n.write_shift(4,s),n.write_shift(4,i.length),ir([n].concat(a).concat(i))}function aa(e,t,r,n,a,i){var s=b(a?68:48),f=[s];s.write_shift(2,65534),s.write_shift(2,0),s.write_shift(4,842412599),s.write_shift(16,ke.utils.consts.HEADER_CLSID,"hex"),s.write_shift(4,a?2:1),s.write_shift(16,t,"hex"),s.write_shift(4,a?68:48);var o=na(e,r,n);if(f.push(o),a){var l=na(a,null,null);s.write_shift(16,i,"hex"),s.write_shift(4,68+o.length),f.push(l)}return ir(f)}function qf(e,t){t||(t=b(e));for(var r=0;r<e;++r)t.write_shift(1,0);return t}function Jf(e,t){return e.read_shift(t)===1}function gr(e,t){return t||(t=b(2)),t.write_shift(2,+!!e),t}function ui(e){return e.read_shift(2,"u")}function Ir(e,t){return t||(t=b(2)),t.write_shift(2,e),t}function ci(e,t,r){return r||(r=b(2)),r.write_shift(1,t=="e"?+e:+!!e),r.write_shift(1,t=="e"?1:0),r}function hi(e,t,r){var n=e.read_shift(r&&r.biff>=12?2:1),a="sbcs-cont";if(r&&r.biff>=8,!r||r.biff==8){var i=e.read_shift(1);i&&(a="dbcs-cont")}else r.biff==12&&(a="wstr");r.biff>=2&&r.biff<=5&&(a="cpstr");var s=n?e.read_shift(n,a):"";return s}function Zf(e){var t=e.t||"",r=b(3+0);r.write_shift(2,t.length),r.write_shift(1,1);var n=b(2*t.length);n.write_shift(2*t.length,t,"utf16le");var a=[r,n];return ir(a)}function Qf(e,t,r){var n;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}var a=e.read_shift(1);return a===0?n=e.read_shift(t,"sbcs-cont"):n=e.read_shift(t,"dbcs-cont"),n}function el(e,t,r){var n=e.read_shift(r&&r.biff==2?1:2);return n===0?(e.l++,""):Qf(e,n,r)}function rl(e,t,r){if(r.biff>5)return el(e,t,r);var n=e.read_shift(1);return n===0?(e.l++,""):e.read_shift(n,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function xi(e,t,r){return r||(r=b(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function ia(e,t){t||(t=b(6+e.length*2)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function tl(e){var t=b(512),r=0,n=e.Target;n.slice(0,7)=="file://"&&(n=n.slice(7));var a=n.indexOf("#"),i=a>-1?31:23;switch(n.charAt(0)){case"#":i=28;break;case".":i&=-3;break}t.write_shift(4,2),t.write_shift(4,i);var s=[8,6815827,6619237,4849780,83];for(r=0;r<s.length;++r)t.write_shift(4,s[r]);if(i==28)n=n.slice(1),ia(n,t);else if(i&2){for(s="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));var f=a>-1?n.slice(0,a):n;for(t.write_shift(4,2*(f.length+1)),r=0;r<f.length;++r)t.write_shift(2,f.charCodeAt(r));t.write_shift(2,0),i&8&&ia(a>-1?n.slice(a+1):"",t)}else{for(s="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));for(var o=0;n.slice(o*3,o*3+3)=="../"||n.slice(o*3,o*3+3)=="..\\";)++o;for(t.write_shift(2,o),t.write_shift(4,n.length-3*o+1),r=0;r<n.length-3*o;++r)t.write_shift(1,n.charCodeAt(r+3*o)&255);for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}function pt(e,t,r,n){return n||(n=b(6)),n.write_shift(2,e),n.write_shift(2,t),n.write_shift(2,r||0),n}function nl(e,t,r){var n=r.biff>8?4:2,a=e.read_shift(n),i=e.read_shift(n,"i"),s=e.read_shift(n,"i");return[a,i,s]}function al(e){var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2),a=e.read_shift(2);return{s:{c:n,r:t},e:{c:a,r}}}function di(e,t){return t||(t=b(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function E0(e,t,r){var n=1536,a=16;switch(r.bookType){case"biff8":break;case"biff5":n=1280,a=8;break;case"biff4":n=4,a=6;break;case"biff3":n=3,a=6;break;case"biff2":n=2,a=4;break;case"xla":break;default:throw new Error("unsupported BIFF version")}var i=b(a);return i.write_shift(2,n),i.write_shift(2,t),a>4&&i.write_shift(2,29282),a>6&&i.write_shift(2,1997),a>8&&(i.write_shift(2,49161),i.write_shift(2,1),i.write_shift(2,1798),i.write_shift(2,0)),i}function il(e,t){var r=!t||t.biff==8,n=b(r?112:54);for(n.write_shift(t.biff==8?2:1,7),r&&n.write_shift(1,0),n.write_shift(4,859007059),n.write_shift(4,5458548|(r?0:536870912));n.l<n.length;)n.write_shift(1,r?0:32);return n}function sl(e,t){var r=!t||t.biff>=8?2:1,n=b(8+r*e.name.length);n.write_shift(4,e.pos),n.write_shift(1,e.hs||0),n.write_shift(1,e.dt),n.write_shift(1,e.name.length),t.biff>=8&&n.write_shift(1,1),n.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var a=n.slice(0,n.l);return a.l=n.l,a}function fl(e,t){var r=b(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var n=[],a=0;a<e.length;++a)n[a]=Zf(e[a]);var i=ir([r].concat(n));return i.parts=[r.length].concat(n.map(function(s){return s.length})),i}function ll(){var e=b(18);return e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,29280),e.write_shift(2,17600),e.write_shift(2,56),e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,1),e.write_shift(2,500),e}function ol(e){var t=b(18),r=1718;return e&&e.RTL&&(r|=64),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,64),t.write_shift(4,0),t.write_shift(4,0),t}function ul(e,t){var r=e.name||"Arial",n=t&&t.biff==5,a=n?15+r.length:16+2*r.length,i=b(a);return i.write_shift(2,(e.sz||12)*20),i.write_shift(4,0),i.write_shift(2,400),i.write_shift(4,0),i.write_shift(2,0),i.write_shift(1,r.length),n||i.write_shift(1,1),i.write_shift((n?1:2)*r.length,r,n?"sbcs":"utf16le"),i}function cl(e,t,r,n){var a=b(10);return pt(e,t,n,a),a.write_shift(4,r),a}function hl(e,t,r,n,a){var i=!a||a.biff==8,s=b(6+2+ +i+(1+i)*r.length);return pt(e,t,n,s),s.write_shift(2,r.length),i&&s.write_shift(1,1),s.write_shift((1+i)*r.length,r,i?"utf16le":"sbcs"),s}function xl(e,t,r,n){var a=r&&r.biff==5;n||(n=b(a?3+t.length:5+2*t.length)),n.write_shift(2,e),n.write_shift(a?1:2,t.length),a||n.write_shift(1,1),n.write_shift((a?1:2)*t.length,t,a?"sbcs":"utf16le");var i=n.length>n.l?n.slice(0,n.l):n;return i.l==null&&(i.l=i.length),i}function dl(e,t){var r=t.biff==8||!t.biff?4:2,n=b(2*r+6);return n.write_shift(r,e.s.r),n.write_shift(r,e.e.r+1),n.write_shift(2,e.s.c),n.write_shift(2,e.e.c+1),n.write_shift(2,0),n}function sa(e,t,r,n){var a=r&&r.biff==5;n||(n=b(a?16:20)),n.write_shift(2,0),e.style?(n.write_shift(2,e.numFmtId||0),n.write_shift(2,65524)):(n.write_shift(2,e.numFmtId||0),n.write_shift(2,t<<4));var i=0;return e.numFmtId>0&&a&&(i|=1024),n.write_shift(4,i),n.write_shift(4,0),a||n.write_shift(4,0),n.write_shift(2,0),n}function pl(e){var t=b(8);return t.write_shift(4,0),t.write_shift(2,e[0]?e[0]+1:0),t.write_shift(2,e[1]?e[1]+1:0),t}function ml(e,t,r,n,a,i){var s=b(8);return pt(e,t,n,s),ci(r,i,s),s}function vl(e,t,r,n){var a=b(14);return pt(e,t,n,a),dt(r,a),a}function gl(e,t,r){if(r.biff<8)return _l(e,t,r);for(var n=[],a=e.l+t,i=e.read_shift(r.biff>8?4:2);i--!==0;)n.push(nl(e,r.biff>8?12:6,r));if(e.l!=a)throw new Error("Bad ExternSheet: "+e.l+" != "+a);return n}function _l(e,t,r){e[e.l+1]==3&&e[e.l]++;var n=hi(e,t,r);return n.charCodeAt(0)==3?n.slice(1):n}function El(e){var t=b(2+e.length*8);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)di(e[r],t);return t}function Tl(e){var t=b(24),r=rr(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var n="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),a=0;a<16;++a)t.write_shift(1,parseInt(n[a],16));return ir([t,tl(e[1])])}function wl(e){var t=e[1].Tooltip,r=b(10+2*(t.length+1));r.write_shift(2,2048);var n=rr(e[0]);r.write_shift(2,n.r),r.write_shift(2,n.r),r.write_shift(2,n.c),r.write_shift(2,n.c);for(var a=0;a<t.length;++a)r.write_shift(2,t.charCodeAt(a));return r.write_shift(2,0),r}function Al(e){return e||(e=b(4)),e.write_shift(2,1),e.write_shift(2,1),e}function Sl(e,t,r){if(!r.cellStyles)return Vr(e,t);var n=r&&r.biff>=12?4:2,a=e.read_shift(n),i=e.read_shift(n),s=e.read_shift(n),f=e.read_shift(n),o=e.read_shift(2);n==2&&(e.l+=2);var l={s:a,e:i,w:s,ixfe:f,flags:o};return(r.biff>=5||!r.biff)&&(l.level=o>>8&7),l}function Fl(e,t){var r=b(12);r.write_shift(2,t),r.write_shift(2,t),r.write_shift(2,e.width*256),r.write_shift(2,0);var n=0;return e.hidden&&(n|=1),r.write_shift(1,n),n=e.level||0,r.write_shift(1,n),r.write_shift(2,0),r}function Cl(e){for(var t=b(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}function yl(e,t,r){var n=b(15);return nn(n,e,t),n.write_shift(8,r,"f"),n}function Ol(e,t,r){var n=b(9);return nn(n,e,t),n.write_shift(2,r),n}var Dl=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=o0({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(f,o){var l=[],u=xt(1);switch(o.type){case"base64":u=Br(Kr(f));break;case"binary":u=Br(f);break;case"buffer":case"array":u=f;break}yr(u,0);var x=u.read_shift(1),d=!!(x&136),p=!1,g=!1;switch(x){case 2:break;case 3:break;case 48:p=!0,d=!0;break;case 49:p=!0,d=!0;break;case 131:break;case 139:break;case 140:g=!0;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+x.toString(16))}var h=0,_=521;x==2&&(h=u.read_shift(2)),u.l+=3,x!=2&&(h=u.read_shift(4)),h>1048576&&(h=1e6),x!=2&&(_=u.read_shift(2));var N=u.read_shift(2),O=o.codepage||1252;x!=2&&(u.l+=16,u.read_shift(1),u[u.l]!==0&&(O=e[u[u.l]]),u.l+=1,u.l+=2),g&&(u.l+=36);for(var F=[],R={},q=Math.min(u.length,x==2?521:_-10-(p?264:0)),ne=g?32:11;u.l<q&&u[u.l]!=13;)switch(R={},R.name=ut.utils.decode(O,u.slice(u.l,u.l+ne)).replace(/[\u0000\r\n].*$/g,""),u.l+=ne,R.type=String.fromCharCode(u.read_shift(1)),x!=2&&!g&&(R.offset=u.read_shift(4)),R.len=u.read_shift(1),x==2&&(R.offset=u.read_shift(2)),R.dec=u.read_shift(1),R.name.length&&F.push(R),x!=2&&(u.l+=g?13:14),R.type){case"B":(!p||R.len!=8)&&o.WTF&&console.log("Skipping "+R.name+":"+R.type);break;case"G":case"P":o.WTF&&console.log("Skipping "+R.name+":"+R.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+R.type)}if(u[u.l]!==13&&(u.l=_-1),u.read_shift(1)!==13)throw new Error("DBF Terminator not found "+u.l+" "+u[u.l]);u.l=_;var D=0,W=0;for(l[0]=[],W=0;W!=F.length;++W)l[0][W]=F[W].name;for(;h-- >0;){if(u[u.l]===42){u.l+=N;continue}for(++u.l,l[++D]=[],W=0,W=0;W!=F.length;++W){var M=u.slice(u.l,u.l+F[W].len);u.l+=F[W].len,yr(M,0);var X=ut.utils.decode(O,M);switch(F[W].type){case"C":X.trim().length&&(l[D][W]=X.replace(/\s+$/,""));break;case"D":X.length===8?l[D][W]=new Date(+X.slice(0,4),+X.slice(4,6)-1,+X.slice(6,8)):l[D][W]=X;break;case"F":l[D][W]=parseFloat(X.trim());break;case"+":case"I":l[D][W]=g?M.read_shift(-4,"i")^2147483648:M.read_shift(4,"i");break;case"L":switch(X.trim().toUpperCase()){case"Y":case"T":l[D][W]=!0;break;case"N":case"F":l[D][W]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+X+"|")}break;case"M":if(!d)throw new Error("DBF Unexpected MEMO for type "+x.toString(16));l[D][W]="##MEMO##"+(g?parseInt(X.trim(),10):M.read_shift(4));break;case"N":X=X.replace(/\u0000/g,"").trim(),X&&X!="."&&(l[D][W]=+X||0);break;case"@":l[D][W]=new Date(M.read_shift(-8,"f")-621356832e5);break;case"T":l[D][W]=new Date((M.read_shift(4)-2440588)*864e5+M.read_shift(4));break;case"Y":l[D][W]=M.read_shift(4,"i")/1e4+M.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":l[D][W]=-M.read_shift(-8,"f");break;case"B":if(p&&F[W].len==8){l[D][W]=M.read_shift(8,"f");break}case"G":case"P":M.l+=F[W].len;break;case"0":if(F[W].name==="_NullFlags")break;default:throw new Error("DBF Unsupported data type "+F[W].type)}}}if(x!=2&&u.l<u.length&&u[u.l++]!=26)throw new Error("DBF EOF Marker missing "+(u.l-1)+" of "+u.length+" "+u[u.l-1].toString(16));return o&&o.sheetRows&&(l=l.slice(0,o.sheetRows)),o.DBF=F,l}function n(f,o){var l=o||{};l.dateNF||(l.dateNF="yyyymmdd");var u=It(r(f,l),l);return u["!cols"]=l.DBF.map(function(x){return{wch:x.len,DBF:x}}),delete l.DBF,u}function a(f,o){try{return mt(n(f,o),o)}catch(l){if(o&&o.WTF)throw l}return{SheetNames:[],Sheets:{}}}var i={B:8,C:250,L:1,D:8,"?":0,"":0};function s(f,o){var l=o||{};if(+l.codepage>=0&&$t(+l.codepage),l.type=="string")throw new Error("Cannot write DBF to JS string");var u=Tr(),x=Rn(f,{header:1,raw:!0,cellDates:!0}),d=x[0],p=x.slice(1),g=f["!cols"]||[],h=0,_=0,N=0,O=1;for(h=0;h<d.length;++h){if(((g[h]||{}).DBF||{}).name){d[h]=g[h].DBF.name,++N;continue}if(d[h]!=null){if(++N,typeof d[h]=="number"&&(d[h]=d[h].toString(10)),typeof d[h]!="string")throw new Error("DBF Invalid column name "+d[h]+" |"+typeof d[h]+"|");if(d.indexOf(d[h])!==h){for(_=0;_<1024;++_)if(d.indexOf(d[h]+"_"+_)==-1){d[h]+="_"+_;break}}}}var F=Ue(f["!ref"]),R=[],q=[],ne=[];for(h=0;h<=F.e.c-F.s.c;++h){var D="",W="",M=0,X=[];for(_=0;_<p.length;++_)p[_][h]!=null&&X.push(p[_][h]);if(X.length==0||d[h]==null){R[h]="?";continue}for(_=0;_<X.length;++_){switch(typeof X[_]){case"number":W="B";break;case"string":W="C";break;case"boolean":W="L";break;case"object":W=X[_]instanceof Date?"D":"C";break;default:W="C"}M=Math.max(M,String(X[_]).length),D=D&&D!=W?"C":W}M>250&&(M=250),W=((g[h]||{}).DBF||{}).type,W=="C"&&g[h].DBF.len>M&&(M=g[h].DBF.len),D=="B"&&W=="N"&&(D="N",ne[h]=g[h].DBF.dec,M=g[h].DBF.len),q[h]=D=="C"||W=="N"?M:i[D]||0,O+=q[h],R[h]=D}var z=u.next(32);for(z.write_shift(4,318902576),z.write_shift(4,p.length),z.write_shift(2,296+32*N),z.write_shift(2,O),h=0;h<4;++h)z.write_shift(4,0);for(z.write_shift(4,0|(+t[ma]||3)<<8),h=0,_=0;h<d.length;++h)if(d[h]!=null){var K=u.next(32),ie=(d[h].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);K.write_shift(1,ie,"sbcs"),K.write_shift(1,R[h]=="?"?"C":R[h],"sbcs"),K.write_shift(4,_),K.write_shift(1,q[h]||i[R[h]]||0),K.write_shift(1,ne[h]||0),K.write_shift(1,2),K.write_shift(4,0),K.write_shift(1,0),K.write_shift(4,0),K.write_shift(4,0),_+=q[h]||i[R[h]]||0}var Ae=u.next(264);for(Ae.write_shift(4,13),h=0;h<65;++h)Ae.write_shift(4,0);for(h=0;h<p.length;++h){var me=u.next(O);for(me.write_shift(1,0),_=0;_<d.length;++_)if(d[_]!=null)switch(R[_]){case"L":me.write_shift(1,p[h][_]==null?63:p[h][_]?84:70);break;case"B":me.write_shift(8,p[h][_]||0,"f");break;case"N":var We="0";for(typeof p[h][_]=="number"&&(We=p[h][_].toFixed(ne[_]||0)),N=0;N<q[_]-We.length;++N)me.write_shift(1,32);me.write_shift(1,We,"sbcs");break;case"D":p[h][_]?(me.write_shift(4,("0000"+p[h][_].getFullYear()).slice(-4),"sbcs"),me.write_shift(2,("00"+(p[h][_].getMonth()+1)).slice(-2),"sbcs"),me.write_shift(2,("00"+p[h][_].getDate()).slice(-2),"sbcs")):me.write_shift(8,"00000000","sbcs");break;case"C":var Pe=String(p[h][_]!=null?p[h][_]:"").slice(0,q[_]);for(me.write_shift(1,Pe,"sbcs"),N=0;N<q[_]-Pe.length;++N)me.write_shift(1,32);break}}return u.next(1).write_shift(1,26),u.end()}return{to_workbook:a,to_sheet:n,from_sheet:s}}(),Nl=function(){var e={AA:"\xC0",BA:"\xC1",CA:"\xC2",DA:195,HA:"\xC4",JA:197,AE:"\xC8",BE:"\xC9",CE:"\xCA",HE:"\xCB",AI:"\xCC",BI:"\xCD",CI:"\xCE",HI:"\xCF",AO:"\xD2",BO:"\xD3",CO:"\xD4",DO:213,HO:"\xD6",AU:"\xD9",BU:"\xDA",CU:"\xDB",HU:"\xDC",Aa:"\xE0",Ba:"\xE1",Ca:"\xE2",Da:227,Ha:"\xE4",Ja:229,Ae:"\xE8",Be:"\xE9",Ce:"\xEA",He:"\xEB",Ai:"\xEC",Bi:"\xED",Ci:"\xEE",Hi:"\xEF",Ao:"\xF2",Bo:"\xF3",Co:"\xF4",Do:245,Ho:"\xF6",Au:"\xF9",Bu:"\xFA",Cu:"\xFB",Hu:"\xFC",KC:"\xC7",Kc:"\xE7",q:"\xE6",z:"\u0153",a:"\xC6",j:"\u0152",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("\x1BN("+lr(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(d,p){var g=e[p];return typeof g=="number"?N0(g):g},n=function(d,p,g){var h=p.charCodeAt(0)-32<<4|g.charCodeAt(0)-48;return h==59?d:N0(h)};e["|"]=254;function a(d,p){switch(p.type){case"base64":return i(Kr(d),p);case"binary":return i(d,p);case"buffer":return i(ye&&Buffer.isBuffer(d)?d.toString("binary"):Zt(d),p);case"array":return i(Bn(d),p)}throw new Error("Unrecognized type "+p.type)}function i(d,p){var g=d.split(/[\n\r]+/),h=-1,_=-1,N=0,O=0,F=[],R=[],q=null,ne={},D=[],W=[],M=[],X=0,z;for(+p.codepage>=0&&$t(+p.codepage);N!==g.length;++N){X=0;var K=g[N].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(t,r),ie=K.replace(/;;/g,"\0").split(";").map(function(C){return C.replace(/\u0000/g,";")}),Ae=ie[0],me;if(K.length>0)switch(Ae){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":ie[1].charAt(0)=="P"&&R.push(K.slice(3).replace(/;;/g,";"));break;case"C":var We=!1,Pe=!1,Er=!1,ze=!1,dr=-1,pr=-1;for(O=1;O<ie.length;++O)switch(ie[O].charAt(0)){case"A":break;case"X":_=parseInt(ie[O].slice(1))-1,Pe=!0;break;case"Y":for(h=parseInt(ie[O].slice(1))-1,Pe||(_=0),z=F.length;z<=h;++z)F[z]=[];break;case"K":me=ie[O].slice(1),me.charAt(0)==='"'?me=me.slice(1,me.length-1):me==="TRUE"?me=!0:me==="FALSE"?me=!1:isNaN(zr(me))?isNaN(Yt(me).getDate())||(me=_r(me)):(me=zr(me),q!==null&&Oa(q)&&(me=Ia(me))),We=!0;break;case"E":ze=!0;var S=Do(ie[O].slice(1),{r:h,c:_});F[h][_]=[F[h][_],S];break;case"S":Er=!0,F[h][_]=[F[h][_],"S5S"];break;case"G":break;case"R":dr=parseInt(ie[O].slice(1))-1;break;case"C":pr=parseInt(ie[O].slice(1))-1;break;default:if(p&&p.WTF)throw new Error("SYLK bad record "+K)}if(We&&(F[h][_]&&F[h][_].length==2?F[h][_][0]=me:F[h][_]=me,q=null),Er){if(ze)throw new Error("SYLK shared formula cannot have own formula");var B=dr>-1&&F[dr][pr];if(!B||!B[1])throw new Error("SYLK shared formula cannot find base");F[h][_][1]=No(B[1],{r:h-dr,c:_-pr})}break;case"F":var y=0;for(O=1;O<ie.length;++O)switch(ie[O].charAt(0)){case"X":_=parseInt(ie[O].slice(1))-1,++y;break;case"Y":for(h=parseInt(ie[O].slice(1))-1,z=F.length;z<=h;++z)F[z]=[];break;case"M":X=parseInt(ie[O].slice(1))/20;break;case"F":break;case"G":break;case"P":q=R[parseInt(ie[O].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":for(M=ie[O].slice(1).split(" "),z=parseInt(M[0],10);z<=parseInt(M[1],10);++z)X=parseInt(M[2],10),W[z-1]=X===0?{hidden:!0}:{wch:X},T0(W[z-1]);break;case"C":_=parseInt(ie[O].slice(1))-1,W[_]||(W[_]={});break;case"R":h=parseInt(ie[O].slice(1))-1,D[h]||(D[h]={}),X>0?(D[h].hpt=X,D[h].hpx=_i(X)):X===0&&(D[h].hidden=!0);break;default:if(p&&p.WTF)throw new Error("SYLK bad record "+K)}y<1&&(q=null);break;default:if(p&&p.WTF)throw new Error("SYLK bad record "+K)}}return D.length>0&&(ne["!rows"]=D),W.length>0&&(ne["!cols"]=W),p&&p.sheetRows&&(F=F.slice(0,p.sheetRows)),[F,ne]}function s(d,p){var g=a(d,p),h=g[0],_=g[1],N=It(h,p);return lr(_).forEach(function(O){N[O]=_[O]}),N}function f(d,p){return mt(s(d,p),p)}function o(d,p,g,h){var _="C;Y"+(g+1)+";X"+(h+1)+";K";switch(d.t){case"n":_+=d.v||0,d.f&&!d.F&&(_+=";E"+A0(d.f,{r:g,c:h}));break;case"b":_+=d.v?"TRUE":"FALSE";break;case"e":_+=d.w||d.v;break;case"d":_+='"'+(d.w||d.v)+'"';break;case"s":_+='"'+d.v.replace(/"/g,"").replace(/;/g,";;")+'"';break}return _}function l(d,p){p.forEach(function(g,h){var _="F;W"+(h+1)+" "+(h+1)+" ";g.hidden?_+="0":(typeof g.width=="number"&&!g.wpx&&(g.wpx=yn(g.width)),typeof g.wpx=="number"&&!g.wch&&(g.wch=On(g.wpx)),typeof g.wch=="number"&&(_+=Math.round(g.wch))),_.charAt(_.length-1)!=" "&&d.push(_)})}function u(d,p){p.forEach(function(g,h){var _="F;";g.hidden?_+="M0;":g.hpt?_+="M"+20*g.hpt+";":g.hpx&&(_+="M"+20*Dn(g.hpx)+";"),_.length>2&&d.push(_+"R"+(h+1))})}function x(d,p){var g=["ID;PWXL;N;E"],h=[],_=Ue(d["!ref"]),N,O=Array.isArray(d),F=`\r
`;g.push("P;PGeneral"),g.push("F;P0;DG0G8;M255"),d["!cols"]&&l(g,d["!cols"]),d["!rows"]&&u(g,d["!rows"]),g.push("B;Y"+(_.e.r-_.s.r+1)+";X"+(_.e.c-_.s.c+1)+";D"+[_.s.c,_.s.r,_.e.c,_.e.r].join(" "));for(var R=_.s.r;R<=_.e.r;++R)for(var q=_.s.c;q<=_.e.c;++q){var ne=Ie({r:R,c:q});N=O?(d[R]||[])[q]:d[ne],!(!N||N.v==null&&(!N.f||N.F))&&h.push(o(N,d,R,q))}return g.join(F)+F+h.join(F)+F+"E"+F}return{to_workbook:f,to_sheet:s,from_sheet:x}}(),Rl=function(){function e(i,s){switch(s.type){case"base64":return t(Kr(i),s);case"binary":return t(i,s);case"buffer":return t(ye&&Buffer.isBuffer(i)?i.toString("binary"):Zt(i),s);case"array":return t(Bn(i),s)}throw new Error("Unrecognized type "+s.type)}function t(i,s){for(var f=i.split(`
`),o=-1,l=-1,u=0,x=[];u!==f.length;++u){if(f[u].trim()==="BOT"){x[++o]=[],l=0;continue}if(!(o<0)){var d=f[u].trim().split(","),p=d[0],g=d[1];++u;for(var h=f[u]||"";(h.match(/["]/g)||[]).length&1&&u<f.length-1;)h+=`
`+f[++u];switch(h=h.trim(),+p){case-1:if(h==="BOT"){x[++o]=[],l=0;continue}else if(h!=="EOD")throw new Error("Unrecognized DIF special command "+h);break;case 0:h==="TRUE"?x[o][l]=!0:h==="FALSE"?x[o][l]=!1:isNaN(zr(g))?isNaN(Yt(g).getDate())?x[o][l]=g:x[o][l]=_r(g):x[o][l]=zr(g),++l;break;case 1:h=h.slice(1,h.length-1),h=h.replace(/""/g,'"'),h&&h.match(/^=".*"$/)&&(h=h.slice(2,-1)),x[o][l++]=h!==""?h:null;break}if(h==="EOD")break}}return s&&s.sheetRows&&(x=x.slice(0,s.sheetRows)),x}function r(i,s){return It(e(i,s),s)}function n(i,s){return mt(r(i,s),s)}var a=function(){var i=function(o,l,u,x,d){o.push(l),o.push(u+","+x),o.push('"'+d.replace(/"/g,'""')+'"')},s=function(o,l,u,x){o.push(l+","+u),o.push(l==1?'"'+x.replace(/"/g,'""')+'"':x)};return function(o){var l=[],u=Ue(o["!ref"]),x,d=Array.isArray(o);i(l,"TABLE",0,1,"sheetjs"),i(l,"VECTORS",0,u.e.r-u.s.r+1,""),i(l,"TUPLES",0,u.e.c-u.s.c+1,""),i(l,"DATA",0,0,"");for(var p=u.s.r;p<=u.e.r;++p){s(l,-1,0,"BOT");for(var g=u.s.c;g<=u.e.c;++g){var h=Ie({r:p,c:g});if(x=d?(o[p]||[])[g]:o[h],!x){s(l,1,0,"");continue}switch(x.t){case"n":var _=x.w;!_&&x.v!=null&&(_=x.v),_==null?x.f&&!x.F?s(l,1,0,"="+x.f):s(l,1,0,""):s(l,0,_,"V");break;case"b":s(l,0,x.v?1:0,x.v?"TRUE":"FALSE");break;case"s":s(l,1,0,isNaN(x.v)?x.v:'="'+x.v+'"');break;case"d":x.w||(x.w=tt(x.z||Xe[14],wr(_r(x.v)))),s(l,0,x.w,"V");break;default:s(l,1,0,"")}}}s(l,-1,0,"EOD");var N=`\r
`,O=l.join(N);return O}}();return{to_workbook:n,to_sheet:r,from_sheet:a}}(),pi=function(){function e(x){return x.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,`
`)}function t(x){return x.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function r(x,d){for(var p=x.split(`
`),g=-1,h=-1,_=0,N=[];_!==p.length;++_){var O=p[_].trim().split(":");if(O[0]==="cell"){var F=rr(O[1]);if(N.length<=F.r)for(g=N.length;g<=F.r;++g)N[g]||(N[g]=[]);switch(g=F.r,h=F.c,O[2]){case"t":N[g][h]=e(O[3]);break;case"v":N[g][h]=+O[3];break;case"vtf":var R=O[O.length-1];case"vtc":switch(O[3]){case"nl":N[g][h]=!!+O[4];break;default:N[g][h]=+O[4];break}O[2]=="vtf"&&(N[g][h]=[N[g][h],R])}}}return d&&d.sheetRows&&(N=N.slice(0,d.sheetRows)),N}function n(x,d){return It(r(x,d),d)}function a(x,d){return mt(n(x,d),d)}var i=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join(`
`),s=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join(`
`)+`
`,f=["# SocialCalc Spreadsheet Control Save","part:sheet"].join(`
`),o="--SocialCalcSpreadsheetControlSave--";function l(x){if(!x||!x["!ref"])return"";for(var d=[],p=[],g,h="",_=Nr(x["!ref"]),N=Array.isArray(x),O=_.s.r;O<=_.e.r;++O)for(var F=_.s.c;F<=_.e.c;++F)if(h=Ie({r:O,c:F}),g=N?(x[O]||[])[F]:x[h],!(!g||g.v==null||g.t==="z")){switch(p=["cell",h,"t"],g.t){case"s":case"str":p.push(t(g.v));break;case"n":g.f?(p[2]="vtf",p[3]="n",p[4]=g.v,p[5]=t(g.f)):(p[2]="v",p[3]=g.v);break;case"b":p[2]="vt"+(g.f?"f":"c"),p[3]="nl",p[4]=g.v?"1":"0",p[5]=t(g.f||(g.v?"TRUE":"FALSE"));break;case"d":var R=wr(_r(g.v));p[2]="vtc",p[3]="nd",p[4]=""+R,p[5]=g.w||tt(g.z||Xe[14],R);break;case"e":continue}d.push(p.join(":"))}return d.push("sheet:c:"+(_.e.c-_.s.c+1)+":r:"+(_.e.r-_.s.r+1)+":tvf:1"),d.push("valueformat:1:text-wiki"),d.join(`
`)}function u(x){return[i,s,f,s,l(x),o].join(`
`)}return{to_workbook:a,to_sheet:n,from_sheet:u}}(),Il=function(){function e(u,x,d,p,g){g.raw?x[d][p]=u:u===""||(u==="TRUE"?x[d][p]=!0:u==="FALSE"?x[d][p]=!1:isNaN(zr(u))?isNaN(Yt(u).getDate())?x[d][p]=u:x[d][p]=_r(u):x[d][p]=zr(u))}function t(u,x){var d=x||{},p=[];if(!u||u.length===0)return p;for(var g=u.split(/[\r\n]/),h=g.length-1;h>=0&&g[h].length===0;)--h;for(var _=10,N=0,O=0;O<=h;++O)N=g[O].indexOf(" "),N==-1?N=g[O].length:N++,_=Math.max(_,N);for(O=0;O<=h;++O){p[O]=[];var F=0;for(e(g[O].slice(0,_).trim(),p,O,F,d),F=1;F<=(g[O].length-_)/10+1;++F)e(g[O].slice(_+(F-1)*10,_+F*10).trim(),p,O,F,d)}return d.sheetRows&&(p=p.slice(0,d.sheetRows)),p}var r={44:",",9:"	",59:";",124:"|"},n={44:3,9:2,59:1,124:0};function a(u){for(var x={},d=!1,p=0,g=0;p<u.length;++p)(g=u.charCodeAt(p))==34?d=!d:!d&&g in r&&(x[g]=(x[g]||0)+1);g=[];for(p in x)Object.prototype.hasOwnProperty.call(x,p)&&g.push([x[p],p]);if(!g.length){x=n;for(p in x)Object.prototype.hasOwnProperty.call(x,p)&&g.push([x[p],p])}return g.sort(function(h,_){return h[0]-_[0]||n[h[1]]-n[_[1]]}),r[g.pop()[1]]||44}function i(u,x){var d=x||{},p="",g=d.dense?[]:{},h={s:{c:0,r:0},e:{c:0,r:0}};u.slice(0,4)=="sep="?u.charCodeAt(5)==13&&u.charCodeAt(6)==10?(p=u.charAt(4),u=u.slice(7)):u.charCodeAt(5)==13||u.charCodeAt(5)==10?(p=u.charAt(4),u=u.slice(6)):p=a(u.slice(0,1024)):d&&d.FS?p=d.FS:p=a(u.slice(0,1024));var _=0,N=0,O=0,F=0,R=0,q=p.charCodeAt(0),ne=!1,D=0,W=u.charCodeAt(0);u=u.replace(/\r\n/mg,`
`);var M=d.dateNF!=null?Ks(d.dateNF):null;function X(){var z=u.slice(F,R),K={};if(z.charAt(0)=='"'&&z.charAt(z.length-1)=='"'&&(z=z.slice(1,-1).replace(/""/g,'"')),z.length===0)K.t="z";else if(d.raw)K.t="s",K.v=z;else if(z.trim().length===0)K.t="s",K.v=z;else if(z.charCodeAt(0)==61)z.charCodeAt(1)==34&&z.charCodeAt(z.length-1)==34?(K.t="s",K.v=z.slice(2,-1).replace(/""/g,'"')):Ro(z)?(K.t="n",K.f=z.slice(1)):(K.t="s",K.v=z);else if(z=="TRUE")K.t="b",K.v=!0;else if(z=="FALSE")K.t="b",K.v=!1;else if(!isNaN(O=zr(z)))K.t="n",d.cellText!==!1&&(K.w=z),K.v=O;else if(!isNaN(Yt(z).getDate())||M&&z.match(M)){K.z=d.dateNF||Xe[14];var ie=0;M&&z.match(M)&&(z=Ys(z,d.dateNF,z.match(M)||[]),ie=1),d.cellDates?(K.t="d",K.v=_r(z,ie)):(K.t="n",K.v=wr(_r(z,ie))),d.cellText!==!1&&(K.w=tt(K.z,K.v instanceof Date?wr(K.v):K.v)),d.cellNF||delete K.z}else K.t="s",K.v=z;if(K.t=="z"||(d.dense?(g[_]||(g[_]=[]),g[_][N]=K):g[Ie({c:N,r:_})]=K),F=R+1,W=u.charCodeAt(F),h.e.c<N&&(h.e.c=N),h.e.r<_&&(h.e.r=_),D==q)++N;else if(N=0,++_,d.sheetRows&&d.sheetRows<=_)return!0}e:for(;R<u.length;++R)switch(D=u.charCodeAt(R)){case 34:W===34&&(ne=!ne);break;case q:case 10:case 13:if(!ne&&X())break e;break}return R-F>0&&X(),g["!ref"]=qe(h),g}function s(u,x){return!(x&&x.PRN)||x.FS||u.slice(0,4)=="sep="||u.indexOf("	")>=0||u.indexOf(",")>=0||u.indexOf(";")>=0?i(u,x):It(t(u,x),x)}function f(u,x){var d="",p=x.type=="string"?[0,0,0,0]:zh(u,x);switch(x.type){case"base64":d=Kr(u);break;case"binary":d=u;break;case"buffer":x.codepage==65001?d=u.toString("utf8"):x.codepage&&typeof ut!="undefined"?d=ut.utils.decode(x.codepage,u):d=ye&&Buffer.isBuffer(u)?u.toString("binary"):Zt(u);break;case"array":d=Bn(u);break;case"string":d=u;break;default:throw new Error("Unrecognized type "+x.type)}return p[0]==239&&p[1]==187&&p[2]==191?d=Vt(d.slice(3)):x.type!="string"&&x.type!="buffer"&&x.codepage==65001?d=Vt(d):x.type=="binary"&&typeof ut!="undefined"&&x.codepage&&(d=ut.utils.decode(x.codepage,ut.utils.encode(28591,d))),d.slice(0,19)=="socialcalc:version:"?pi.to_sheet(x.type=="string"?d:Vt(d),x):s(d,x)}function o(u,x){return mt(f(u,x),x)}function l(u){for(var x=[],d=Ue(u["!ref"]),p,g=Array.isArray(u),h=d.s.r;h<=d.e.r;++h){for(var _=[],N=d.s.c;N<=d.e.c;++N){var O=Ie({r:h,c:N});if(p=g?(u[h]||[])[N]:u[O],!p||p.v==null){_.push("          ");continue}for(var F=(p.w||(Yr(p),p.w)||"").slice(0,10);F.length<10;)F+=" ";_.push(F+(N===0?" ":""))}x.push(_.join(""))}return x.join(`
`)}return{to_workbook:o,to_sheet:f,from_sheet:l}}(),fa=function(){function e(S,B,y){if(!!S){yr(S,S.l||0);for(var C=y.Enum||dr;S.l<S.length;){var G=S.read_shift(2),ce=C[G]||C[65535],he=S.read_shift(2),ue=S.l+he,ae=ce.f&&ce.f(S,he,y);if(S.l=ue,B(ae,ce,G))return}}}function t(S,B){switch(B.type){case"base64":return r(Br(Kr(S)),B);case"binary":return r(Br(S),B);case"buffer":case"array":return r(S,B)}throw"Unsupported type "+B.type}function r(S,B){if(!S)return S;var y=B||{},C=y.dense?[]:{},G="Sheet1",ce="",he=0,ue={},ae=[],Oe=[],we={s:{r:0,c:0},e:{r:0,c:0}},Qe=y.sheetRows||0;if(S[2]==0&&(S[3]==8||S[3]==9)&&S.length>=16&&S[14]==5&&S[15]===108)throw new Error("Unsupported Works 3 for Mac file");if(S[2]==2)y.Enum=dr,e(S,function(le,Y,A){switch(A){case 0:y.vers=le,le>=4096&&(y.qpro=!0);break;case 6:we=le;break;case 204:le&&(ce=le);break;case 222:ce=le;break;case 15:case 51:y.qpro||(le[1].v=le[1].v.slice(1));case 13:case 14:case 16:A==14&&(le[2]&112)==112&&(le[2]&15)>1&&(le[2]&15)<15&&(le[1].z=y.dateNF||Xe[14],y.cellDates&&(le[1].t="d",le[1].v=Ia(le[1].v))),y.qpro&&le[3]>he&&(C["!ref"]=qe(we),ue[G]=C,ae.push(G),C=y.dense?[]:{},we={s:{r:0,c:0},e:{r:0,c:0}},he=le[3],G=ce||"Sheet"+(he+1),ce="");var V=y.dense?(C[le[0].r]||[])[le[0].c]:C[Ie(le[0])];if(V){V.t=le[1].t,V.v=le[1].v,le[1].z!=null&&(V.z=le[1].z),le[1].f!=null&&(V.f=le[1].f);break}y.dense?(C[le[0].r]||(C[le[0].r]=[]),C[le[0].r][le[0].c]=le[1]):C[Ie(le[0])]=le[1];break}},y);else if(S[2]==26||S[2]==14)y.Enum=pr,S[2]==14&&(y.qpro=!0,S.l=0),e(S,function(le,Y,A){switch(A){case 204:G=le;break;case 22:le[1].v=le[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(le[3]>he&&(C["!ref"]=qe(we),ue[G]=C,ae.push(G),C=y.dense?[]:{},we={s:{r:0,c:0},e:{r:0,c:0}},he=le[3],G="Sheet"+(he+1)),Qe>0&&le[0].r>=Qe)break;y.dense?(C[le[0].r]||(C[le[0].r]=[]),C[le[0].r][le[0].c]=le[1]):C[Ie(le[0])]=le[1],we.e.c<le[0].c&&(we.e.c=le[0].c),we.e.r<le[0].r&&(we.e.r=le[0].r);break;case 27:le[14e3]&&(Oe[le[14e3][0]]=le[14e3][1]);break;case 1537:Oe[le[0]]=le[1],le[0]==he&&(G=le[1]);break}},y);else throw new Error("Unrecognized LOTUS BOF "+S[2]);if(C["!ref"]=qe(we),ue[ce||G]=C,ae.push(ce||G),!Oe.length)return{SheetNames:ae,Sheets:ue};for(var Fe={},Sr=[],Be=0;Be<Oe.length;++Be)ue[ae[Be]]?(Sr.push(Oe[Be]||ae[Be]),Fe[Oe[Be]]=ue[Oe[Be]]||ue[ae[Be]]):(Sr.push(Oe[Be]),Fe[Oe[Be]]={"!ref":"A1"});return{SheetNames:Sr,Sheets:Fe}}function n(S,B){var y=B||{};if(+y.codepage>=0&&$t(+y.codepage),y.type=="string")throw new Error("Cannot write WK1 to JS string");var C=Tr(),G=Ue(S["!ref"]),ce=Array.isArray(S),he=[];ee(C,0,i(1030)),ee(C,6,o(G));for(var ue=Math.min(G.e.r,8191),ae=G.s.r;ae<=ue;++ae)for(var Oe=fr(ae),we=G.s.c;we<=G.e.c;++we){ae===G.s.r&&(he[we]=hr(we));var Qe=he[we]+Oe,Fe=ce?(S[ae]||[])[we]:S[Qe];if(!(!Fe||Fe.t=="z"))if(Fe.t=="n")(Fe.v|0)==Fe.v&&Fe.v>=-32768&&Fe.v<=32767?ee(C,13,p(ae,we,Fe.v)):ee(C,14,h(ae,we,Fe.v));else{var Sr=Yr(Fe);ee(C,15,x(ae,we,Sr.slice(0,239)))}}return ee(C,1),C.end()}function a(S,B){var y=B||{};if(+y.codepage>=0&&$t(+y.codepage),y.type=="string")throw new Error("Cannot write WK3 to JS string");var C=Tr();ee(C,0,s(S));for(var G=0,ce=0;G<S.SheetNames.length;++G)(S.Sheets[S.SheetNames[G]]||{})["!ref"]&&ee(C,27,ze(S.SheetNames[G],ce++));var he=0;for(G=0;G<S.SheetNames.length;++G){var ue=S.Sheets[S.SheetNames[G]];if(!(!ue||!ue["!ref"])){for(var ae=Ue(ue["!ref"]),Oe=Array.isArray(ue),we=[],Qe=Math.min(ae.e.r,8191),Fe=ae.s.r;Fe<=Qe;++Fe)for(var Sr=fr(Fe),Be=ae.s.c;Be<=ae.e.c;++Be){Fe===ae.s.r&&(we[Be]=hr(Be));var le=we[Be]+Sr,Y=Oe?(ue[Fe]||[])[Be]:ue[le];if(!(!Y||Y.t=="z"))if(Y.t=="n")ee(C,23,X(Fe,Be,he,Y.v));else{var A=Yr(Y);ee(C,22,D(Fe,Be,he,A.slice(0,239)))}}++he}}return ee(C,1),C.end()}function i(S){var B=b(2);return B.write_shift(2,S),B}function s(S){var B=b(26);B.write_shift(2,4096),B.write_shift(2,4),B.write_shift(4,0);for(var y=0,C=0,G=0,ce=0;ce<S.SheetNames.length;++ce){var he=S.SheetNames[ce],ue=S.Sheets[he];if(!(!ue||!ue["!ref"])){++G;var ae=Nr(ue["!ref"]);y<ae.e.r&&(y=ae.e.r),C<ae.e.c&&(C=ae.e.c)}}return y>8191&&(y=8191),B.write_shift(2,y),B.write_shift(1,G),B.write_shift(1,C),B.write_shift(2,0),B.write_shift(2,0),B.write_shift(1,1),B.write_shift(1,2),B.write_shift(4,0),B.write_shift(4,0),B}function f(S,B,y){var C={s:{c:0,r:0},e:{c:0,r:0}};return B==8&&y.qpro?(C.s.c=S.read_shift(1),S.l++,C.s.r=S.read_shift(2),C.e.c=S.read_shift(1),S.l++,C.e.r=S.read_shift(2),C):(C.s.c=S.read_shift(2),C.s.r=S.read_shift(2),B==12&&y.qpro&&(S.l+=2),C.e.c=S.read_shift(2),C.e.r=S.read_shift(2),B==12&&y.qpro&&(S.l+=2),C.s.c==65535&&(C.s.c=C.e.c=C.s.r=C.e.r=0),C)}function o(S){var B=b(8);return B.write_shift(2,S.s.c),B.write_shift(2,S.s.r),B.write_shift(2,S.e.c),B.write_shift(2,S.e.r),B}function l(S,B,y){var C=[{c:0,r:0},{t:"n",v:0},0,0];return y.qpro&&y.vers!=20768?(C[0].c=S.read_shift(1),C[3]=S.read_shift(1),C[0].r=S.read_shift(2),S.l+=2):(C[2]=S.read_shift(1),C[0].c=S.read_shift(2),C[0].r=S.read_shift(2)),C}function u(S,B,y){var C=S.l+B,G=l(S,B,y);if(G[1].t="s",y.vers==20768){S.l++;var ce=S.read_shift(1);return G[1].v=S.read_shift(ce,"utf8"),G}return y.qpro&&S.l++,G[1].v=S.read_shift(C-S.l,"cstr"),G}function x(S,B,y){var C=b(7+y.length);C.write_shift(1,255),C.write_shift(2,B),C.write_shift(2,S),C.write_shift(1,39);for(var G=0;G<C.length;++G){var ce=y.charCodeAt(G);C.write_shift(1,ce>=128?95:ce)}return C.write_shift(1,0),C}function d(S,B,y){var C=l(S,B,y);return C[1].v=S.read_shift(2,"i"),C}function p(S,B,y){var C=b(7);return C.write_shift(1,255),C.write_shift(2,B),C.write_shift(2,S),C.write_shift(2,y,"i"),C}function g(S,B,y){var C=l(S,B,y);return C[1].v=S.read_shift(8,"f"),C}function h(S,B,y){var C=b(13);return C.write_shift(1,255),C.write_shift(2,B),C.write_shift(2,S),C.write_shift(8,y,"f"),C}function _(S,B,y){var C=S.l+B,G=l(S,B,y);if(G[1].v=S.read_shift(8,"f"),y.qpro)S.l=C;else{var ce=S.read_shift(2);R(S.slice(S.l,S.l+ce),G),S.l+=ce}return G}function N(S,B,y){var C=B&32768;return B&=-32769,B=(C?S:0)+(B>=8192?B-16384:B),(C?"":"$")+(y?hr(B):fr(B))}var O={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},F=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function R(S,B){yr(S,0);for(var y=[],C=0,G="",ce="",he="",ue="";S.l<S.length;){var ae=S[S.l++];switch(ae){case 0:y.push(S.read_shift(8,"f"));break;case 1:ce=N(B[0].c,S.read_shift(2),!0),G=N(B[0].r,S.read_shift(2),!1),y.push(ce+G);break;case 2:{var Oe=N(B[0].c,S.read_shift(2),!0),we=N(B[0].r,S.read_shift(2),!1);ce=N(B[0].c,S.read_shift(2),!0),G=N(B[0].r,S.read_shift(2),!1),y.push(Oe+we+":"+ce+G)}break;case 3:if(S.l<S.length){console.error("WK1 premature formula end");return}break;case 4:y.push("("+y.pop()+")");break;case 5:y.push(S.read_shift(2));break;case 6:{for(var Qe="";ae=S[S.l++];)Qe+=String.fromCharCode(ae);y.push('"'+Qe.replace(/"/g,'""')+'"')}break;case 8:y.push("-"+y.pop());break;case 23:y.push("+"+y.pop());break;case 22:y.push("NOT("+y.pop()+")");break;case 20:case 21:ue=y.pop(),he=y.pop(),y.push(["AND","OR"][ae-20]+"("+he+","+ue+")");break;default:if(ae<32&&F[ae])ue=y.pop(),he=y.pop(),y.push(he+F[ae]+ue);else if(O[ae]){if(C=O[ae][1],C==69&&(C=S[S.l++]),C>y.length){console.error("WK1 bad formula parse 0x"+ae.toString(16)+":|"+y.join("|")+"|");return}var Fe=y.slice(-C);y.length-=C,y.push(O[ae][0]+"("+Fe.join(",")+")")}else return ae<=7?console.error("WK1 invalid opcode "+ae.toString(16)):ae<=24?console.error("WK1 unsupported op "+ae.toString(16)):ae<=30?console.error("WK1 invalid opcode "+ae.toString(16)):ae<=115?console.error("WK1 unsupported function opcode "+ae.toString(16)):console.error("WK1 unrecognized opcode "+ae.toString(16))}}y.length==1?B[1].f=""+y[0]:console.error("WK1 bad formula parse |"+y.join("|")+"|")}function q(S){var B=[{c:0,r:0},{t:"n",v:0},0];return B[0].r=S.read_shift(2),B[3]=S[S.l++],B[0].c=S[S.l++],B}function ne(S,B){var y=q(S);return y[1].t="s",y[1].v=S.read_shift(B-4,"cstr"),y}function D(S,B,y,C){var G=b(6+C.length);G.write_shift(2,S),G.write_shift(1,y),G.write_shift(1,B),G.write_shift(1,39);for(var ce=0;ce<C.length;++ce){var he=C.charCodeAt(ce);G.write_shift(1,he>=128?95:he)}return G.write_shift(1,0),G}function W(S,B){var y=q(S);y[1].v=S.read_shift(2);var C=y[1].v>>1;if(y[1].v&1)switch(C&7){case 0:C=(C>>3)*5e3;break;case 1:C=(C>>3)*500;break;case 2:C=(C>>3)/20;break;case 3:C=(C>>3)/200;break;case 4:C=(C>>3)/2e3;break;case 5:C=(C>>3)/2e4;break;case 6:C=(C>>3)/16;break;case 7:C=(C>>3)/64;break}return y[1].v=C,y}function M(S,B){var y=q(S),C=S.read_shift(4),G=S.read_shift(4),ce=S.read_shift(2);if(ce==65535)return C===0&&G===3221225472?(y[1].t="e",y[1].v=15):C===0&&G===3489660928?(y[1].t="e",y[1].v=42):y[1].v=0,y;var he=ce&32768;return ce=(ce&32767)-16446,y[1].v=(1-he*2)*(G*Math.pow(2,ce+32)+C*Math.pow(2,ce)),y}function X(S,B,y,C){var G=b(14);if(G.write_shift(2,S),G.write_shift(1,y),G.write_shift(1,B),C==0)return G.write_shift(4,0),G.write_shift(4,0),G.write_shift(2,65535),G;var ce=0,he=0,ue=0,ae=0;return C<0&&(ce=1,C=-C),he=Math.log2(C)|0,C/=Math.pow(2,he-31),ae=C>>>0,(ae&2147483648)==0&&(C/=2,++he,ae=C>>>0),C-=ae,ae|=2147483648,ae>>>=0,C*=Math.pow(2,32),ue=C>>>0,G.write_shift(4,ue),G.write_shift(4,ae),he+=16383+(ce?32768:0),G.write_shift(2,he),G}function z(S,B){var y=M(S);return S.l+=B-14,y}function K(S,B){var y=q(S),C=S.read_shift(4);return y[1].v=C>>6,y}function ie(S,B){var y=q(S),C=S.read_shift(8,"f");return y[1].v=C,y}function Ae(S,B){var y=ie(S);return S.l+=B-10,y}function me(S,B){return S[S.l+B-1]==0?S.read_shift(B,"cstr"):""}function We(S,B){var y=S[S.l++];y>B-1&&(y=B-1);for(var C="";C.length<y;)C+=String.fromCharCode(S[S.l++]);return C}function Pe(S,B,y){if(!(!y.qpro||B<21)){var C=S.read_shift(1);S.l+=17,S.l+=1,S.l+=2;var G=S.read_shift(B-21,"cstr");return[C,G]}}function Er(S,B){for(var y={},C=S.l+B;S.l<C;){var G=S.read_shift(2);if(G==14e3){for(y[G]=[0,""],y[G][0]=S.read_shift(2);S[S.l];)y[G][1]+=String.fromCharCode(S[S.l]),S.l++;S.l++}}return y}function ze(S,B){var y=b(5+S.length);y.write_shift(2,14e3),y.write_shift(2,B);for(var C=0;C<S.length;++C){var G=S.charCodeAt(C);y[y.l++]=G>127?95:G}return y[y.l++]=0,y}var dr={0:{n:"BOF",f:ui},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:d},14:{n:"NUMBER",f:g},15:{n:"LABEL",f:u},16:{n:"FORMULA",f:_},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:u},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:me},222:{n:"SHEETNAMELP",f:We},65535:{n:""}},pr={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:ne},23:{n:"NUMBER17",f:M},24:{n:"NUMBER18",f:W},25:{n:"FORMULA19",f:z},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:Er},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:K},38:{n:"??"},39:{n:"NUMBER27",f:ie},40:{n:"FORMULA28",f:Ae},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:me},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:Pe},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:n,book_to_wk3:a,to_workbook:t}}(),kl=/^\s|\s$|[\t\n\r]/;function mi(e,t){if(!t.bookSST)return"";var r=[Je];r[r.length]=Z("sst",null,{xmlns:Rt[0],count:e.Count,uniqueCount:e.Unique});for(var n=0;n!=e.length;++n)if(e[n]!=null){var a=e[n],i="<si>";a.r?i+=a.r:(i+="<t",a.t||(a.t=""),a.t.match(kl)&&(i+=' xml:space="preserve"'),i+=">"+Re(a.t)+"</t>"),i+="</si>",r[r.length]=i}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}function Pl(e){return[e.read_shift(4),e.read_shift(4)]}function Ll(e,t){return t||(t=b(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t}var Bl=Cf;function Ml(e){var t=Tr();H(t,159,Ll(e));for(var r=0;r<e.length;++r)H(t,19,Bl(e[r]));return H(t,160),t.end()}function bl(e){for(var t=[],r=e.split(""),n=0;n<r.length;++n)t[n]=r[n].charCodeAt(0);return t}function vi(e){var t=0,r,n=bl(e),a=n.length+1,i,s,f,o,l;for(r=xt(a),r[0]=n.length,i=1;i!=a;++i)r[i]=n[i-1];for(i=a-1;i>=0;--i)s=r[i],f=(t&16384)===0?0:1,o=t<<1&32767,l=f|o,t=l^s;return t^52811}var Ul=function(){function e(a,i){switch(i.type){case"base64":return t(Kr(a),i);case"binary":return t(a,i);case"buffer":return t(ye&&Buffer.isBuffer(a)?a.toString("binary"):Zt(a),i);case"array":return t(Bn(a),i)}throw new Error("Unrecognized type "+i.type)}function t(a,i){var s=i||{},f=s.dense?[]:{},o=a.match(/\\trowd.*?\\row\b/g);if(!o.length)throw new Error("RTF missing table");var l={s:{c:0,r:0},e:{c:0,r:o.length-1}};return o.forEach(function(u,x){Array.isArray(f)&&(f[x]=[]);for(var d=/\\\w+\b/g,p=0,g,h=-1;g=d.exec(u);){switch(g[0]){case"\\cell":var _=u.slice(p,d.lastIndex-g[0].length);if(_[0]==" "&&(_=_.slice(1)),++h,_.length){var N={v:_,t:"s"};Array.isArray(f)?f[x][h]=N:f[Ie({r:x,c:h})]=N}break}p=d.lastIndex}h>l.e.c&&(l.e.c=h)}),f["!ref"]=qe(l),f}function r(a,i){return mt(e(a,i),i)}function n(a){for(var i=["{\\rtf1\\ansi"],s=Ue(a["!ref"]),f,o=Array.isArray(a),l=s.s.r;l<=s.e.r;++l){i.push("\\trowd\\trautofit1");for(var u=s.s.c;u<=s.e.c;++u)i.push("\\cellx"+(u+1));for(i.push("\\pard\\intbl"),u=s.s.c;u<=s.e.c;++u){var x=Ie({r:l,c:u});f=o?(a[l]||[])[u]:a[x],!(!f||f.v==null&&(!f.f||f.F))&&(i.push(" "+(f.w||(Yr(f),f.w))),i.push("\\cell"))}i.push("\\pard\\intbl\\row")}return i.join("")+"}"}return{to_workbook:r,to_sheet:e,from_sheet:n}}();function la(e){for(var t=0,r=1;t!=3;++t)r=r*256+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}var Wl=6,$r=Wl;function yn(e){return Math.floor((e+Math.round(128/$r)/256)*$r)}function On(e){return Math.floor((e-5)/$r*100+.5)/100}function s0(e){return Math.round((e*$r+5)/$r*256)/256}function T0(e){e.width?(e.wpx=yn(e.width),e.wch=On(e.wpx),e.MDW=$r):e.wpx?(e.wch=On(e.wpx),e.width=s0(e.wch),e.MDW=$r):typeof e.wch=="number"&&(e.width=s0(e.wch),e.wpx=yn(e.width),e.MDW=$r),e.customWidth&&delete e.customWidth}var Vl=96,gi=Vl;function Dn(e){return e*96/gi}function _i(e){return e*gi/96}function Hl(e){var t=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach(function(r){for(var n=r[0];n<=r[1];++n)e[n]!=null&&(t[t.length]=Z("numFmt",null,{numFmtId:n,formatCode:Re(e[n])}))}),t.length===1?"":(t[t.length]="</numFmts>",t[0]=Z("numFmts",null,{count:t.length-2}).replace("/>",">"),t.join(""))}function Gl(e){var t=[];return t[t.length]=Z("cellXfs",null),e.forEach(function(r){t[t.length]=Z("xf",null,r)}),t[t.length]="</cellXfs>",t.length===2?"":(t[0]=Z("cellXfs",null,{count:t.length-2}).replace("/>",">"),t.join(""))}function Ei(e,t){var r=[Je,Z("styleSheet",null,{xmlns:Rt[0],"xmlns:vt":er.vt})],n;return e.SSF&&(n=Hl(e.SSF))!=null&&(r[r.length]=n),r[r.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',r[r.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',r[r.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',r[r.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(n=Gl(t.cellXfs))&&(r[r.length]=n),r[r.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',r[r.length]='<dxfs count="0"/>',r[r.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',r.length>2&&(r[r.length]="</styleSheet>",r[1]=r[1].replace("/>",">")),r.join("")}function Xl(e,t){var r=e.read_shift(2),n=xr(e);return[r,n]}function zl(e,t,r){r||(r=b(6+4*t.length)),r.write_shift(2,e),tr(t,r);var n=r.length>r.l?r.slice(0,r.l):r;return r.l==null&&(r.l=r.length),n}function $l(e,t,r){var n={};n.sz=e.read_shift(2)/20;var a=kf(e);a.fItalic&&(n.italic=1),a.fCondense&&(n.condense=1),a.fExtend&&(n.extend=1),a.fShadow&&(n.shadow=1),a.fOutline&&(n.outline=1),a.fStrikeout&&(n.strike=1);var i=e.read_shift(2);switch(i===700&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript";break}var s=e.read_shift(1);s!=0&&(n.underline=s);var f=e.read_shift(1);f>0&&(n.family=f);var o=e.read_shift(1);switch(o>0&&(n.charset=o),e.l++,n.color=If(e),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor";break}return n.name=xr(e),n}function Kl(e,t){t||(t=b(25+4*32)),t.write_shift(2,e.sz*20),Pf(e,t),t.write_shift(2,e.bold?700:400);var r=0;e.vertAlign=="superscript"?r=1:e.vertAlign=="subscript"&&(r=2),t.write_shift(2,r),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),Fn(e.color,t);var n=0;return e.scheme=="major"&&(n=1),e.scheme=="minor"&&(n=2),t.write_shift(1,n),tr(e.name,t),t.length>t.l?t.slice(0,t.l):t}var Yl=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],Qn,jl=Vr;function oa(e,t){t||(t=b(4*3+8*7+16*1)),Qn||(Qn=o0(Yl));var r=Qn[e.patternType];r==null&&(r=40),t.write_shift(4,r);var n=0;if(r!=40)for(Fn({auto:1},t),Fn({auto:1},t);n<12;++n)t.write_shift(4,0);else{for(;n<4;++n)t.write_shift(4,0);for(;n<12;++n)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function ql(e,t){var r=e.l+t,n=e.read_shift(2),a=e.read_shift(2);return e.l=r,{ixfe:n,numFmtId:a}}function Ti(e,t,r){r||(r=b(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0);var n=0;return r.write_shift(1,n),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function bt(e,t){return t||(t=b(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var Jl=Vr;function Zl(e,t){return t||(t=b(51)),t.write_shift(1,0),bt(null,t),bt(null,t),bt(null,t),bt(null,t),bt(null,t),t.length>t.l?t.slice(0,t.l):t}function Ql(e,t){return t||(t=b(12+4*10)),t.write_shift(4,e.xfId),t.write_shift(2,1),t.write_shift(1,+e.builtinId),t.write_shift(1,0),Sn(e.name||"",t),t.length>t.l?t.slice(0,t.l):t}function eo(e,t,r){var n=b(2052);return n.write_shift(4,e),Sn(t,n),Sn(r,n),n.length>n.l?n.slice(0,n.l):n}function ro(e,t){if(!!t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var a=n[0];a<=n[1];++a)t[a]!=null&&++r}),r!=0&&(H(e,615,br(r)),[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var a=n[0];a<=n[1];++a)t[a]!=null&&H(e,44,zl(a,t[a]))}),H(e,616))}}function to(e){var t=1;H(e,611,br(t)),H(e,43,Kl({sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"})),H(e,612)}function no(e){var t=2;H(e,603,br(t)),H(e,45,oa({patternType:"none"})),H(e,45,oa({patternType:"gray125"})),H(e,604)}function ao(e){var t=1;H(e,613,br(t)),H(e,46,Zl()),H(e,614)}function io(e){var t=1;H(e,626,br(t)),H(e,47,Ti({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),H(e,627)}function so(e,t){H(e,617,br(t.length)),t.forEach(function(r){H(e,47,Ti(r,0))}),H(e,618)}function fo(e){var t=1;H(e,619,br(t)),H(e,48,Ql({xfId:0,builtinId:0,name:"Normal"})),H(e,620)}function lo(e){var t=0;H(e,505,br(t)),H(e,506)}function oo(e){var t=0;H(e,508,eo(t,"TableStyleMedium9","PivotStyleMedium4")),H(e,509)}function uo(e,t){var r=Tr();return H(r,278),ro(r,e.SSF),to(r),no(r),ao(r),io(r),so(r,t.cellXfs),fo(r),lo(r),oo(r),H(r,279),r.end()}function wi(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&typeof e.raw=="string")return e.raw;var r=[Je];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="\uFF2D\uFF33 \uFF30\u30B4\u30B7\u30C3\u30AF"/>',r[r.length]='<a:font script="Hang" typeface="\uB9D1\uC740 \uACE0\uB515"/>',r[r.length]='<a:font script="Hans" typeface="\u5B8B\u4F53"/>',r[r.length]='<a:font script="Hant" typeface="\u65B0\u7D30\u660E\u9AD4"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="\uFF2D\uFF33 \uFF30\u30B4\u30B7\u30C3\u30AF"/>',r[r.length]='<a:font script="Hang" typeface="\uB9D1\uC740 \uACE0\uB515"/>',r[r.length]='<a:font script="Hans" typeface="\u5B8B\u4F53"/>',r[r.length]='<a:font script="Hant" typeface="\u65B0\u7D30\u660E\u9AD4"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function co(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:xr(e)}}function ho(e){var t=b(12+2*e.name.length);return t.write_shift(4,e.flags),t.write_shift(4,e.version),tr(e.name,t),t.slice(0,t.l)}function xo(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}function po(e){var t=b(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}function mo(e,t){var r=b(8+2*t.length);return r.write_shift(4,e),tr(t,r),r.slice(0,r.l)}function vo(e){return e.l+=4,e.read_shift(4)!=0}function go(e,t){var r=b(8);return r.write_shift(4,e),r.write_shift(4,t?1:0),r}function _o(){var e=Tr();return H(e,332),H(e,334,br(1)),H(e,335,ho({name:"XLDAPR",version:12e4,flags:3496657072})),H(e,336),H(e,339,mo(1,"XLDAPR")),H(e,52),H(e,35,br(514)),H(e,4096,br(0)),H(e,4097,Ir(1)),H(e,36),H(e,53),H(e,340),H(e,337,go(1,!0)),H(e,51,po([[1,0]])),H(e,338),H(e,333),e.end()}function Ai(){var e=[Je];return e.push(`<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">
  <metadataTypes count="1">
    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>
  </metadataTypes>
  <futureMetadata name="XLDAPR" count="1">
    <bk>
      <extLst>
        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">
          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>
        </ext>
      </extLst>
    </bk>
  </futureMetadata>
  <cellMetadata count="1">
    <bk>
      <rc t="1" v="0"/>
    </bk>
  </cellMetadata>
</metadata>`),e.join("")}function Eo(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=Ie(r);var n=e.read_shift(1);return n&2&&(t.l="1"),n&8&&(t.a="1"),t}var Ft=1024;function Si(e,t){for(var r=[21600,21600],n=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),a=[Z("xml",null,{"xmlns:v":Or.v,"xmlns:o":Or.o,"xmlns:x":Or.x,"xmlns:mv":Or.mv}).replace(/\/>/,">"),Z("o:shapelayout",Z("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),Z("v:shapetype",[Z("v:stroke",null,{joinstyle:"miter"}),Z("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:n})];Ft<e*1e3;)Ft+=1e3;return t.forEach(function(i){var s=rr(i[0]),f={color2:"#BEFF82",type:"gradient"};f.type=="gradient"&&(f.angle="-180");var o=f.type=="gradient"?Z("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,l=Z("v:fill",o,f),u={on:"t",obscured:"t"};++Ft,a=a.concat(["<v:shape"+jt({id:"_x0000_s"+Ft,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(i[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",l,Z("v:shadow",null,u),Z("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",sr("x:Anchor",[s.c+1,0,s.r+1,0,s.c+3,20,s.r+5,20].join(",")),sr("x:AutoFill","False"),sr("x:Row",String(s.r)),sr("x:Column",String(s.c)),i[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])}),a.push("</xml>"),a.join("")}function Fi(e){var t=[Je,Z("comments",null,{xmlns:Rt[0]})],r=[];return t.push("<authors>"),e.forEach(function(n){n[1].forEach(function(a){var i=Re(a.a);r.indexOf(i)==-1&&(r.push(i),t.push("<author>"+i+"</author>")),a.T&&a.ID&&r.indexOf("tc="+a.ID)==-1&&(r.push("tc="+a.ID),t.push("<author>tc="+a.ID+"</author>"))})}),r.length==0&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach(function(n){var a=0,i=[];if(n[1][0]&&n[1][0].T&&n[1][0].ID?a=r.indexOf("tc="+n[1][0].ID):n[1].forEach(function(o){o.a&&(a=r.indexOf(Re(o.a))),i.push(o.t||"")}),t.push('<comment ref="'+n[0]+'" authorId="'+a+'"><text>'),i.length<=1)t.push(sr("t",Re(i[0]||"")));else{for(var s=`Comment:
    `+i[0]+`
`,f=1;f<i.length;++f)s+=`Reply:
    `+i[f]+`
`;t.push(sr("t",Re(s)))}t.push("</text></comment>")}),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}function To(e,t,r){var n=[Je,Z("ThreadedComments",null,{xmlns:er.TCMNT}).replace(/[\/]>/,">")];return e.forEach(function(a){var i="";(a[1]||[]).forEach(function(s,f){if(!s.T){delete s.ID;return}s.a&&t.indexOf(s.a)==-1&&t.push(s.a);var o={ref:a[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};f==0?i=o.id:o.parentId=i,s.ID=o.id,s.a&&(o.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(s.a)).slice(-12)+"}"),n.push(Z("threadedComment",sr("text",s.t||""),o))})}),n.push("</ThreadedComments>"),n.join("")}function wo(e){var t=[Je,Z("personList",null,{xmlns:er.TCMNT,"xmlns:x":Rt[0]}).replace(/[\/]>/,">")];return e.forEach(function(r,n){t.push(Z("person",null,{displayName:r,id:"{54EE7950-7262-4200-6969-"+("000000000000"+n).slice(-12)+"}",userId:r,providerId:"None"}))}),t.push("</personList>"),t.join("")}function Ao(e){var t={};t.iauthor=e.read_shift(4);var r=Et(e);return t.rfx=r.s,t.ref=Ie(r.s),e.l+=16,t}function So(e,t){return t==null&&(t=b(36)),t.write_shift(4,e[1].iauthor),kt(e[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t}var Fo=xr;function Co(e){return tr(e.slice(0,54))}function yo(e){var t=Tr(),r=[];return H(t,628),H(t,630),e.forEach(function(n){n[1].forEach(function(a){r.indexOf(a.a)>-1||(r.push(a.a.slice(0,54)),H(t,632,Co(a.a)))})}),H(t,631),H(t,633),e.forEach(function(n){n[1].forEach(function(a){a.iauthor=r.indexOf(a.a);var i={s:rr(n[0]),e:rr(n[0])};H(t,635,So([i,a])),a.t&&a.t.length>0&&H(t,637,Of(a)),H(t,636),delete a.iauthor})}),H(t,634),H(t,629),t.end()}function Oo(e,t){t.FullPaths.forEach(function(r,n){if(n!=0){var a=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");a.slice(-1)!=="/"&&ke.utils.cfb_add(e,a,t.FileIndex[n].content)}})}var Ci=["xlsb","xlsm","xlam","biff8","xla"],Do=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(n,a,i,s){var f=!1,o=!1;i.length==0?o=!0:i.charAt(0)=="["&&(o=!0,i=i.slice(1,-1)),s.length==0?f=!0:s.charAt(0)=="["&&(f=!0,s=s.slice(1,-1));var l=i.length>0?parseInt(i,10)|0:0,u=s.length>0?parseInt(s,10)|0:0;return f?u+=t.c:--u,o?l+=t.r:--l,a+(f?"":"$")+hr(u)+(o?"":"$")+fr(l)}return function(a,i){return t=i,a.replace(e,r)}}(),w0=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,A0=function(){return function(t,r){return t.replace(w0,function(n,a,i,s,f,o){var l=m0(s)-(i?0:r.c),u=p0(o)-(f?0:r.r),x=u==0?"":f?u+1:"["+u+"]",d=l==0?"":i?l+1:"["+l+"]";return a+"R"+x+"C"+d})}}();function No(e,t){return e.replace(w0,function(r,n,a,i,s,f){return n+(a=="$"?a+i:hr(m0(i)+t.c))+(s=="$"?s+f:fr(p0(f)+t.r))})}function Ro(e){return e.length!=1}function je(e){e.l+=1}function nt(e,t){var r=e.read_shift(t==1?1:2);return[r&16383,r>>14&1,r>>15&1]}function yi(e,t,r){var n=2;if(r){if(r.biff>=2&&r.biff<=5)return Oi(e);r.biff==12&&(n=4)}var a=e.read_shift(n),i=e.read_shift(n),s=nt(e,2),f=nt(e,2);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:i,c:f[0],cRel:f[1],rRel:f[2]}}}function Oi(e){var t=nt(e,2),r=nt(e,2),n=e.read_shift(1),a=e.read_shift(1);return{s:{r:t[0],c:n,cRel:t[1],rRel:t[2]},e:{r:r[0],c:a,cRel:r[1],rRel:r[2]}}}function Io(e,t,r){if(r.biff<8)return Oi(e);var n=e.read_shift(r.biff==12?4:2),a=e.read_shift(r.biff==12?4:2),i=nt(e,2),s=nt(e,2);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:a,c:s[0],cRel:s[1],rRel:s[2]}}}function Di(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return ko(e);var n=e.read_shift(r&&r.biff==12?4:2),a=nt(e,2);return{r:n,c:a[0],cRel:a[1],rRel:a[2]}}function ko(e){var t=nt(e,2),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}function Po(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:r&255,fQuoted:!!(r&16384),cRel:r>>15,rRel:r>>15}}function Lo(e,t,r){var n=r&&r.biff?r.biff:8;if(n>=2&&n<=5)return Bo(e);var a=e.read_shift(n>=12?4:2),i=e.read_shift(2),s=(i&16384)>>14,f=(i&32768)>>15;if(i&=16383,f==1)for(;a>524287;)a-=1048576;if(s==1)for(;i>8191;)i=i-16384;return{r:a,c:i,cRel:s,rRel:f}}function Bo(e){var t=e.read_shift(2),r=e.read_shift(1),n=(t&32768)>>15,a=(t&16384)>>14;return t&=16383,n==1&&t>=8192&&(t=t-16384),a==1&&r>=128&&(r=r-256),{r:t,c:r,cRel:a,rRel:n}}function Mo(e,t,r){var n=(e[e.l++]&96)>>5,a=yi(e,r.biff>=2&&r.biff<=5?6:8,r);return[n,a]}function bo(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2,"i"),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}var s=yi(e,i,r);return[n,a,s]}function Uo(e,t,r){var n=(e[e.l++]&96)>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[n]}function Wo(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}return e.l+=i,[n,a]}function Vo(e,t,r){var n=(e[e.l++]&96)>>5,a=Io(e,t-1,r);return[n,a]}function Ho(e,t,r){var n=(e[e.l++]&96)>>5;return e.l+=r.biff==2?6:r.biff==12?14:7,[n]}function ua(e){var t=e[e.l+1]&1,r=1;return e.l+=4,[t,r]}function Go(e,t,r){e.l+=2;for(var n=e.read_shift(r&&r.biff==2?1:2),a=[],i=0;i<=n;++i)a.push(e.read_shift(r&&r.biff==2?1:2));return a}function Xo(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function zo(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function $o(e){var t=e[e.l+1]&255?1:0;return e.l+=2,[t,e.read_shift(2)]}function Ko(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=r&&r.biff==2?3:4,[n]}function Ni(e){var t=e.read_shift(1),r=e.read_shift(1);return[t,r]}function Yo(e){return e.read_shift(2),Ni(e)}function jo(e){return e.read_shift(2),Ni(e)}function qo(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=Di(e,0,r);return[n,a]}function Jo(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=Lo(e,0,r);return[n,a]}function Zo(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=e.read_shift(2);r&&r.biff==5&&(e.l+=12);var i=Di(e,0,r);return[n,a,i]}function Qo(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=e.read_shift(r&&r.biff<=3?1:2);return[Qu[a],ki[a],n]}function eu(e,t,r){var n=e[e.l++],a=e.read_shift(1),i=r&&r.biff<=3?[n==88?-1:0,e.read_shift(1)]:ru(e);return[a,(i[0]===0?ki:Zu)[i[1]]]}function ru(e){return[e[e.l+1]>>7,e.read_shift(2)&32767]}function tu(e,t,r){e.l+=r&&r.biff==2?3:4}function nu(e,t,r){if(e.l++,r&&r.biff==12)return[e.read_shift(4,"i"),0];var n=e.read_shift(2),a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function au(e){return e.l++,rn[e.read_shift(1)]}function iu(e){return e.l++,e.read_shift(2)}function su(e){return e.l++,e.read_shift(1)!==0}function fu(e){return e.l++,Pt(e)}function lu(e,t,r){return e.l++,hi(e,t-1,r)}function ou(e,t){var r=[e.read_shift(1)];if(t==12)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2;break}switch(r[0]){case 4:r[1]=Jf(e,1)?"TRUE":"FALSE",t!=12&&(e.l+=7);break;case 37:case 16:r[1]=rn[e[e.l]],e.l+=t==12?4:8;break;case 0:e.l+=8;break;case 1:r[1]=Pt(e);break;case 2:r[1]=rl(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function uu(e,t,r){for(var n=e.read_shift(r.biff==12?4:2),a=[],i=0;i!=n;++i)a.push((r.biff==12?Et:al)(e));return a}function cu(e,t,r){var n=0,a=0;r.biff==12?(n=e.read_shift(4),a=e.read_shift(4)):(a=1+e.read_shift(1),n=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--n,--a==0&&(a=256));for(var i=0,s=[];i!=n&&(s[i]=[]);++i)for(var f=0;f!=a;++f)s[i][f]=ou(e,r.biff);return s}function hu(e,t,r){var n=e.read_shift(1)>>>5&3,a=!r||r.biff>=8?4:2,i=e.read_shift(a);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12;break}return[n,0,i]}function xu(e,t,r){if(r.biff==5)return du(e);var n=e.read_shift(1)>>>5&3,a=e.read_shift(2),i=e.read_shift(4);return[n,a,i]}function du(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var n=e.read_shift(2);return e.l+=12,[t,r,n]}function pu(e,t,r){var n=e.read_shift(1)>>>5&3;e.l+=r&&r.biff==2?3:4;var a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function mu(e,t,r){var n=e.read_shift(1)>>>5&3,a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function vu(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,r.biff==12&&(e.l+=2),[n]}function gu(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2),i=4;if(r)switch(r.biff){case 5:i=15;break;case 12:i=6;break}return e.l+=i,[n,a]}var _u=Vr,Eu=Vr,Tu=Vr;function tn(e,t,r){return e.l+=2,[Po(e)]}function S0(e){return e.l+=6,[]}var wu=tn,Au=S0,Su=S0,Fu=tn;function Ri(e){return e.l+=2,[ui(e),e.read_shift(2)&1]}var Cu=tn,yu=Ri,Ou=S0,Du=tn,Nu=tn,Ru=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];function Iu(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),a=e.read_shift(2),i=e.read_shift(2),s=Ru[r>>2&31];return{ixti:t,coltype:r&3,rt:s,idx:n,c:a,C:i}}function ku(e){return e.l+=2,[e.read_shift(4)]}function Pu(e,t,r){return e.l+=5,e.l+=2,e.l+=r.biff==2?1:4,["PTGSHEET"]}function Lu(e,t,r){return e.l+=r.biff==2?4:5,["PTGENDSHEET"]}function Bu(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function Mu(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function bu(e){return e.l+=4,[0,0]}var ca={1:{n:"PtgExp",f:nu},2:{n:"PtgTbl",f:Tu},3:{n:"PtgAdd",f:je},4:{n:"PtgSub",f:je},5:{n:"PtgMul",f:je},6:{n:"PtgDiv",f:je},7:{n:"PtgPower",f:je},8:{n:"PtgConcat",f:je},9:{n:"PtgLt",f:je},10:{n:"PtgLe",f:je},11:{n:"PtgEq",f:je},12:{n:"PtgGe",f:je},13:{n:"PtgGt",f:je},14:{n:"PtgNe",f:je},15:{n:"PtgIsect",f:je},16:{n:"PtgUnion",f:je},17:{n:"PtgRange",f:je},18:{n:"PtgUplus",f:je},19:{n:"PtgUminus",f:je},20:{n:"PtgPercent",f:je},21:{n:"PtgParen",f:je},22:{n:"PtgMissArg",f:je},23:{n:"PtgStr",f:lu},26:{n:"PtgSheet",f:Pu},27:{n:"PtgEndSheet",f:Lu},28:{n:"PtgErr",f:au},29:{n:"PtgBool",f:su},30:{n:"PtgInt",f:iu},31:{n:"PtgNum",f:fu},32:{n:"PtgArray",f:Ho},33:{n:"PtgFunc",f:Qo},34:{n:"PtgFuncVar",f:eu},35:{n:"PtgName",f:hu},36:{n:"PtgRef",f:qo},37:{n:"PtgArea",f:Mo},38:{n:"PtgMemArea",f:pu},39:{n:"PtgMemErr",f:_u},40:{n:"PtgMemNoMem",f:Eu},41:{n:"PtgMemFunc",f:mu},42:{n:"PtgRefErr",f:vu},43:{n:"PtgAreaErr",f:Uo},44:{n:"PtgRefN",f:Jo},45:{n:"PtgAreaN",f:Vo},46:{n:"PtgMemAreaN",f:Bu},47:{n:"PtgMemNoMemN",f:Mu},57:{n:"PtgNameX",f:xu},58:{n:"PtgRef3d",f:Zo},59:{n:"PtgArea3d",f:bo},60:{n:"PtgRefErr3d",f:gu},61:{n:"PtgAreaErr3d",f:Wo},255:{}},Uu={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},Wu={1:{n:"PtgElfLel",f:Ri},2:{n:"PtgElfRw",f:Du},3:{n:"PtgElfCol",f:wu},6:{n:"PtgElfRwV",f:Nu},7:{n:"PtgElfColV",f:Fu},10:{n:"PtgElfRadical",f:Cu},11:{n:"PtgElfRadicalS",f:Ou},13:{n:"PtgElfColS",f:Au},15:{n:"PtgElfColSV",f:Su},16:{n:"PtgElfRadicalLel",f:yu},25:{n:"PtgList",f:Iu},29:{n:"PtgSxName",f:ku},255:{}},Vu={0:{n:"PtgAttrNoop",f:bu},1:{n:"PtgAttrSemi",f:Ko},2:{n:"PtgAttrIf",f:zo},4:{n:"PtgAttrChoose",f:Go},8:{n:"PtgAttrGoto",f:Xo},16:{n:"PtgAttrSum",f:tu},32:{n:"PtgAttrBaxcel",f:ua},33:{n:"PtgAttrBaxcel",f:ua},64:{n:"PtgAttrSpace",f:Yo},65:{n:"PtgAttrSpaceSemi",f:jo},128:{n:"PtgAttrIfError",f:$o},255:{}};function Hu(e,t,r,n){if(n.biff<8)return Vr(e,t);for(var a=e.l+t,i=[],s=0;s!==r.length;++s)switch(r[s][0]){case"PtgArray":r[s][1]=cu(e,0,n),i.push(r[s][1]);break;case"PtgMemArea":r[s][2]=uu(e,r[s][1],n),i.push(r[s][2]);break;case"PtgExp":n&&n.biff==12&&(r[s][1][1]=e.read_shift(4),i.push(r[s][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[s][0]}return t=a-e.l,t!==0&&i.push(Vr(e,t)),i}function Gu(e,t,r){for(var n=e.l+t,a,i,s=[];n!=e.l;)t=n-e.l,i=e[e.l],a=ca[i]||ca[Uu[i]],(i===24||i===25)&&(a=(i===24?Wu:Vu)[e[e.l+1]]),!a||!a.f?Vr(e,t):s.push([a.n,a.f(e,t,r)]);return s}function Xu(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r],a=[],i=0;i<n.length;++i){var s=n[i];if(s)switch(s[0]){case 2:a.push('"'+s[1].replace(/"/g,'""')+'"');break;default:a.push(s[1])}else a.push("")}t.push(a.join(","))}return t.join(";")}var zu={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function $u(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}function Ii(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var n=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),t==0?"":e.XTI[t-1];if(!n)return"SH33TJSERR1";var a="";if(r.biff>8)switch(e[n[0]][0]){case 357:return a=n[1]==-1?"#REF":e.SheetNames[n[1]],n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 358:return r.SID!=null?e.SheetNames[r.SID]:"SH33TJSSAME"+e[n[0]][0];case 355:default:return"SH33TJSSRC"+e[n[0]][0]}switch(e[n[0]][0][0]){case 1025:return a=n[1]==-1?"#REF":e.SheetNames[n[1]]||"SH33TJSERR3",n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 14849:return e[n[0]].slice(1).map(function(i){return i.Name}).join(";;");default:return e[n[0]][0][3]?(a=n[1]==-1?"#REF":e[n[0]][0][3][n[1]]||"SH33TJSERR4",n[1]==n[2]?a:a+":"+e[n[0]][0][3][n[2]]):"SH33TJSERR2"}}function ha(e,t,r){var n=Ii(e,t,r);return n=="#REF"?n:$u(n,r)}function Nt(e,t,r,n,a){var i=a&&a.biff||8,s={s:{c:0,r:0},e:{c:0,r:0}},f=[],o,l,u,x=0,d=0,p,g="";if(!e[0]||!e[0][0])return"";for(var h=-1,_="",N=0,O=e[0].length;N<O;++N){var F=e[0][N];switch(F[0]){case"PtgUminus":f.push("-"+f.pop());break;case"PtgUplus":f.push("+"+f.pop());break;case"PtgPercent":f.push(f.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(o=f.pop(),l=f.pop(),h>=0){switch(e[0][h][1][0]){case 0:_=Ge(" ",e[0][h][1][1]);break;case 1:_=Ge("\r",e[0][h][1][1]);break;default:if(_="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][h][1][0])}l=l+_,h=-1}f.push(l+zu[F[0]]+o);break;case"PtgIsect":o=f.pop(),l=f.pop(),f.push(l+" "+o);break;case"PtgUnion":o=f.pop(),l=f.pop(),f.push(l+","+o);break;case"PtgRange":o=f.pop(),l=f.pop(),f.push(l+":"+o);break;case"PtgAttrChoose":break;case"PtgAttrGoto":break;case"PtgAttrIf":break;case"PtgAttrIfError":break;case"PtgRef":u=Gt(F[1][1],s,a),f.push(Xt(u,i));break;case"PtgRefN":u=r?Gt(F[1][1],r,a):F[1][1],f.push(Xt(u,i));break;case"PtgRef3d":x=F[1][1],u=Gt(F[1][2],s,a),g=ha(n,x,a),f.push(g+"!"+Xt(u,i));break;case"PtgFunc":case"PtgFuncVar":var R=F[1][0],q=F[1][1];R||(R=0),R&=127;var ne=R==0?[]:f.slice(-R);f.length-=R,q==="User"&&(q=ne.shift()),f.push(q+"("+ne.join(",")+")");break;case"PtgBool":f.push(F[1]?"TRUE":"FALSE");break;case"PtgInt":f.push(F[1]);break;case"PtgNum":f.push(String(F[1]));break;case"PtgStr":f.push('"'+F[1].replace(/"/g,'""')+'"');break;case"PtgErr":f.push(F[1]);break;case"PtgAreaN":p=q0(F[1][1],r?{s:r}:s,a),f.push(Jn(p,a));break;case"PtgArea":p=q0(F[1][1],s,a),f.push(Jn(p,a));break;case"PtgArea3d":x=F[1][1],p=F[1][2],g=ha(n,x,a),f.push(g+"!"+Jn(p,a));break;case"PtgAttrSum":f.push("SUM("+f.pop()+")");break;case"PtgAttrBaxcel":case"PtgAttrSemi":break;case"PtgName":d=F[1][2];var D=(n.names||[])[d-1]||(n[0]||[])[d],W=D?D.Name:"SH33TJSNAME"+String(d);W&&W.slice(0,6)=="_xlfn."&&!a.xlfn&&(W=W.slice(6)),f.push(W);break;case"PtgNameX":var M=F[1][1];d=F[1][2];var X;if(a.biff<=5)M<0&&(M=-M),n[M]&&(X=n[M][d]);else{var z="";if(((n[M]||[])[0]||[])[0]==14849||(((n[M]||[])[0]||[])[0]==1025?n[M][d]&&n[M][d].itab>0&&(z=n.SheetNames[n[M][d].itab-1]+"!"):z=n.SheetNames[d-1]+"!"),n[M]&&n[M][d])z+=n[M][d].Name;else if(n[0]&&n[0][d])z+=n[0][d].Name;else{var K=(Ii(n,M,a)||"").split(";;");K[d-1]?z=K[d-1]:z+="SH33TJSERRX"}f.push(z);break}X||(X={Name:"SH33TJSERRY"}),f.push(X.Name);break;case"PtgParen":var ie="(",Ae=")";if(h>=0){switch(_="",e[0][h][1][0]){case 2:ie=Ge(" ",e[0][h][1][1])+ie;break;case 3:ie=Ge("\r",e[0][h][1][1])+ie;break;case 4:Ae=Ge(" ",e[0][h][1][1])+Ae;break;case 5:Ae=Ge("\r",e[0][h][1][1])+Ae;break;default:if(a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][h][1][0])}h=-1}f.push(ie+f.pop()+Ae);break;case"PtgRefErr":f.push("#REF!");break;case"PtgRefErr3d":f.push("#REF!");break;case"PtgExp":u={c:F[1][1],r:F[1][0]};var me={c:r.c,r:r.r};if(n.sharedf[Ie(u)]){var We=n.sharedf[Ie(u)];f.push(Nt(We,s,me,n,a))}else{var Pe=!1;for(o=0;o!=n.arrayf.length;++o)if(l=n.arrayf[o],!(u.c<l[0].s.c||u.c>l[0].e.c)&&!(u.r<l[0].s.r||u.r>l[0].e.r)){f.push(Nt(l[1],s,me,n,a)),Pe=!0;break}Pe||f.push(F[1])}break;case"PtgArray":f.push("{"+Xu(F[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":h=N;break;case"PtgTbl":break;case"PtgMemErr":break;case"PtgMissArg":f.push("");break;case"PtgAreaErr":f.push("#REF!");break;case"PtgAreaErr3d":f.push("#REF!");break;case"PtgList":f.push("Table"+F[1].idx+"[#"+F[1].rt+"]");break;case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":break;case"PtgMemFunc":break;case"PtgMemNoMem":break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");case"PtgSxName":throw new Error("Unrecognized Formula Token: "+String(F));default:throw new Error("Unrecognized Formula Token: "+String(F))}var Er=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(a.biff!=3&&h>=0&&Er.indexOf(e[0][N][0])==-1){F=e[0][h];var ze=!0;switch(F[1][0]){case 4:ze=!1;case 0:_=Ge(" ",F[1][1]);break;case 5:ze=!1;case 1:_=Ge("\r",F[1][1]);break;default:if(_="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+F[1][0])}f.push((ze?_:"")+f.pop()+(ze?"":_)),h=-1}}if(f.length>1&&a.WTF)throw new Error("bad formula stack");return f[0]}function Ku(e){if(e==null){var t=b(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}else if(typeof e=="number")return dt(e);return dt(0)}function Yu(e,t,r,n,a){var i=pt(t,r,a),s=Ku(e.v),f=b(6),o=33;f.write_shift(2,o),f.write_shift(4,0);for(var l=b(e.bf.length),u=0;u<e.bf.length;++u)l[u]=e.bf[u];var x=ir([i,s,f,l]);return x}function Mn(e,t,r){var n=e.read_shift(4),a=Gu(e,n,r),i=e.read_shift(4),s=i>0?Hu(e,i,a,r):null;return[a,s]}var ju=Mn,bn=Mn,qu=Mn,Ju=Mn,Zu={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},ki={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},Qu={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function ec(e){var t="of:="+e.replace(w0,"$1[.$2$3$4$5]").replace(/\]:\[/g,":");return t.replace(/;/g,"|").replace(/,/g,";")}function rc(e){return e.replace(/\./,"!")}var zt=typeof Map!="undefined";function F0(e,t,r){var n=0,a=e.length;if(r){if(zt?r.has(t):Object.prototype.hasOwnProperty.call(r,t)){for(var i=zt?r.get(t):r[t];n<i.length;++n)if(e[i[n]].t===t)return e.Count++,i[n]}}else for(;n<a;++n)if(e[n].t===t)return e.Count++,n;return e[a]={t},e.Count++,e.Unique++,r&&(zt?(r.has(t)||r.set(t,[]),r.get(t).push(a)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(a))),a}function Un(e,t){var r={min:e+1,max:e+1},n=-1;return t.MDW&&($r=t.MDW),t.width!=null?r.customWidth=1:t.wpx!=null?n=On(t.wpx):t.wch!=null&&(n=t.wch),n>-1?(r.width=s0(n),r.customWidth=1):t.width!=null&&(r.width=t.width),t.hidden&&(r.hidden=!0),t.level!=null&&(r.outlineLevel=r.level=t.level),r}function Pi(e,t){if(!!e){var r=[.7,.7,.75,.75,.3,.3];t=="xlml"&&(r=[1,1,1,1,.5,.5]),e.left==null&&(e.left=r[0]),e.right==null&&(e.right=r[1]),e.top==null&&(e.top=r[2]),e.bottom==null&&(e.bottom=r[3]),e.header==null&&(e.header=r[4]),e.footer==null&&(e.footer=r[5])}}function it(e,t,r){var n=r.revssf[t.z!=null?t.z:"General"],a=60,i=e.length;if(n==null&&r.ssf){for(;a<392;++a)if(r.ssf[a]==null){Da(t.z,a),r.ssf[a]=t.z,r.revssf[t.z]=n=a;break}}for(a=0;a!=i;++a)if(e[a].numFmtId===n)return a;return e[i]={numFmtId:n,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},i}function tc(e,t,r){if(e&&e["!ref"]){var n=Ue(e["!ref"]);if(n.e.c<n.s.c||n.e.r<n.s.r)throw new Error("Bad range ("+r+"): "+e["!ref"])}}function nc(e){if(e.length===0)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+qe(e[r])+'"/>';return t+"</mergeCells>"}function ac(e,t,r,n,a){var i=!1,s={},f=null;if(n.bookType!=="xlsx"&&t.vbaraw){var o=t.SheetNames[r];try{t.Workbook&&(o=t.Workbook.Sheets[r].CodeName||o)}catch{}i=!0,s.codeName=Gr(Re(o))}if(e&&e["!outline"]){var l={summaryBelow:1,summaryRight:1};e["!outline"].above&&(l.summaryBelow=0),e["!outline"].left&&(l.summaryRight=0),f=(f||"")+Z("outlinePr",null,l)}!i&&!f||(a[a.length]=Z("sheetPr",f,s))}var ic=["objects","scenarios","selectLockedCells","selectUnlockedCells"],sc=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function fc(e){var t={sheet:1};return ic.forEach(function(r){e[r]!=null&&e[r]&&(t[r]="1")}),sc.forEach(function(r){e[r]!=null&&!e[r]&&(t[r]="0")}),e.password&&(t.password=vi(e.password).toString(16).toUpperCase()),Z("sheetProtection",null,t)}function lc(e){return Pi(e),Z("pageMargins",null,e)}function oc(e,t){for(var r=["<cols>"],n,a=0;a!=t.length;++a)!(n=t[a])||(r[r.length]=Z("col",null,Un(a,n)));return r[r.length]="</cols>",r.join("")}function uc(e,t,r,n){var a=typeof e.ref=="string"?e.ref:qe(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,s=Nr(a);s.s.r==s.e.r&&(s.e.r=Nr(t["!ref"]).e.r,a=qe(s));for(var f=0;f<i.length;++f){var o=i[f];if(o.Name=="_xlnm._FilterDatabase"&&o.Sheet==n){o.Ref="'"+r.SheetNames[n]+"'!"+a;break}}return f==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+a}),Z("autoFilter",null,{ref:a})}function cc(e,t,r,n){var a={workbookViewId:"0"};return(((n||{}).Workbook||{}).Views||[])[0]&&(a.rightToLeft=n.Workbook.Views[0].RTL?"1":"0"),Z("sheetViews",Z("sheetView",null,a),{})}function hc(e,t,r,n){if(e.c&&r["!comments"].push([t,e.c]),e.v===void 0&&typeof e.f!="string"||e.t==="z"&&!e.f)return"";var a="",i=e.t,s=e.v;if(e.t!=="z")switch(e.t){case"b":a=e.v?"1":"0";break;case"n":a=""+e.v;break;case"e":a=rn[e.v];break;case"d":n&&n.cellDates?a=_r(e.v,-1).toISOString():(e=Ar(e),e.t="n",a=""+(e.v=wr(_r(e.v)))),typeof e.z=="undefined"&&(e.z=Xe[14]);break;default:a=e.v;break}var f=sr("v",Re(a)),o={r:t},l=it(n.cellXfs,e,n);switch(l!==0&&(o.s=l),e.t){case"n":break;case"d":o.t="d";break;case"b":o.t="b";break;case"e":o.t="e";break;case"z":break;default:if(e.v==null){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(n&&n.bookSST){f=sr("v",""+F0(n.Strings,e.v,n.revStrings)),o.t="s";break}o.t="str";break}if(e.t!=i&&(e.t=i,e.v=s),typeof e.f=="string"&&e.f){var u=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;f=Z("f",Re(e.f),u)+(e.v!=null?f:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(o.cm=1),Z("c",f,o)}function xc(e,t,r,n){var a=[],i=[],s=Ue(e["!ref"]),f="",o,l="",u=[],x=0,d=0,p=e["!rows"],g=Array.isArray(e),h={r:l},_,N=-1;for(d=s.s.c;d<=s.e.c;++d)u[d]=hr(d);for(x=s.s.r;x<=s.e.r;++x){for(i=[],l=fr(x),d=s.s.c;d<=s.e.c;++d){o=u[d]+l;var O=g?(e[x]||[])[d]:e[o];O!==void 0&&(f=hc(O,o,e,t))!=null&&i.push(f)}(i.length>0||p&&p[x])&&(h={r:l},p&&p[x]&&(_=p[x],_.hidden&&(h.hidden=1),N=-1,_.hpx?N=Dn(_.hpx):_.hpt&&(N=_.hpt),N>-1&&(h.ht=N,h.customHeight=1),_.level&&(h.outlineLevel=_.level)),a[a.length]=Z("row",i.join(""),h))}if(p)for(;x<p.length;++x)p&&p[x]&&(h={r:x+1},_=p[x],_.hidden&&(h.hidden=1),N=-1,_.hpx?N=Dn(_.hpx):_.hpt&&(N=_.hpt),N>-1&&(h.ht=N,h.customHeight=1),_.level&&(h.outlineLevel=_.level),a[a.length]=Z("row","",h));return a.join("")}function Li(e,t,r,n){var a=[Je,Z("worksheet",null,{xmlns:Rt[0],"xmlns:r":er.r})],i=r.SheetNames[e],s=0,f="",o=r.Sheets[i];o==null&&(o={});var l=o["!ref"]||"A1",u=Ue(l);if(u.e.c>16383||u.e.r>1048575){if(t.WTF)throw new Error("Range "+l+" exceeds format limit A1:XFD1048576");u.e.c=Math.min(u.e.c,16383),u.e.r=Math.min(u.e.c,1048575),l=qe(u)}n||(n={}),o["!comments"]=[];var x=[];ac(o,r,e,t,a),a[a.length]=Z("dimension",null,{ref:l}),a[a.length]=cc(o,t,e,r),t.sheetFormat&&(a[a.length]=Z("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),o["!cols"]!=null&&o["!cols"].length>0&&(a[a.length]=oc(o,o["!cols"])),a[s=a.length]="<sheetData/>",o["!links"]=[],o["!ref"]!=null&&(f=xc(o,t),f.length>0&&(a[a.length]=f)),a.length>s+1&&(a[a.length]="</sheetData>",a[s]=a[s].replace("/>",">")),o["!protect"]&&(a[a.length]=fc(o["!protect"])),o["!autofilter"]!=null&&(a[a.length]=uc(o["!autofilter"],o,r,e)),o["!merges"]!=null&&o["!merges"].length>0&&(a[a.length]=nc(o["!merges"]));var d=-1,p,g=-1;return o["!links"].length>0&&(a[a.length]="<hyperlinks>",o["!links"].forEach(function(h){!h[1].Target||(p={ref:h[0]},h[1].Target.charAt(0)!="#"&&(g=Ne(n,-1,Re(h[1].Target).replace(/#.*$/,""),Ce.HLINK),p["r:id"]="rId"+g),(d=h[1].Target.indexOf("#"))>-1&&(p.location=Re(h[1].Target.slice(d+1))),h[1].Tooltip&&(p.tooltip=Re(h[1].Tooltip)),a[a.length]=Z("hyperlink",null,p))}),a[a.length]="</hyperlinks>"),delete o["!links"],o["!margins"]!=null&&(a[a.length]=lc(o["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&(a[a.length]=sr("ignoredErrors",Z("ignoredError",null,{numberStoredAsText:1,sqref:l}))),x.length>0&&(g=Ne(n,-1,"../drawings/drawing"+(e+1)+".xml",Ce.DRAW),a[a.length]=Z("drawing",null,{"r:id":"rId"+g}),o["!drawing"]=x),o["!comments"].length>0&&(g=Ne(n,-1,"../drawings/vmlDrawing"+(e+1)+".vml",Ce.VML),a[a.length]=Z("legacyDrawing",null,{"r:id":"rId"+g}),o["!legacy"]=g),a.length>1&&(a[a.length]="</worksheet>",a[1]=a[1].replace("/>",">")),a.join("")}function dc(e,t){var r={},n=e.l+t;r.r=e.read_shift(4),e.l+=4;var a=e.read_shift(2);e.l+=1;var i=e.read_shift(1);return e.l=n,i&7&&(r.level=i&7),i&16&&(r.hidden=!0),i&32&&(r.hpt=a/20),r}function pc(e,t,r){var n=b(145),a=(r["!rows"]||[])[e]||{};n.write_shift(4,e),n.write_shift(4,0);var i=320;a.hpx?i=Dn(a.hpx)*20:a.hpt&&(i=a.hpt*20),n.write_shift(2,i),n.write_shift(1,0);var s=0;a.level&&(s|=a.level),a.hidden&&(s|=16),(a.hpx||a.hpt)&&(s|=32),n.write_shift(1,s),n.write_shift(1,0);var f=0,o=n.l;n.l+=4;for(var l={r:e,c:0},u=0;u<16;++u)if(!(t.s.c>u+1<<10||t.e.c<u<<10)){for(var x=-1,d=-1,p=u<<10;p<u+1<<10;++p){l.c=p;var g=Array.isArray(r)?(r[l.r]||[])[l.c]:r[Ie(l)];g&&(x<0&&(x=p),d=p)}x<0||(++f,n.write_shift(4,x),n.write_shift(4,d))}var h=n.l;return n.l=o,n.write_shift(4,f),n.l=h,n.length>n.l?n.slice(0,n.l):n}function mc(e,t,r,n){var a=pc(n,r,t);(a.length>17||(t["!rows"]||[])[n])&&H(e,0,a)}var vc=Et,gc=kt;function _c(){}function Ec(e,t){var r={},n=e[e.l];return++e.l,r.above=!(n&64),r.left=!(n&128),e.l+=18,r.name=Df(e),r}function Tc(e,t,r){r==null&&(r=b(84+4*e.length));var n=192;t&&(t.above&&(n&=-65),t.left&&(n&=-129)),r.write_shift(1,n);for(var a=1;a<3;++a)r.write_shift(1,0);return Fn({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),qa(e,r),r.slice(0,r.l)}function wc(e){var t=kr(e);return[t]}function Ac(e,t,r){return r==null&&(r=b(8)),vt(t,r)}function Sc(e){var t=gt(e);return[t]}function Fc(e,t,r){return r==null&&(r=b(4)),_t(t,r)}function Cc(e){var t=kr(e),r=e.read_shift(1);return[t,r,"b"]}function yc(e,t,r){return r==null&&(r=b(9)),vt(t,r),r.write_shift(1,e.v?1:0),r}function Oc(e){var t=gt(e),r=e.read_shift(1);return[t,r,"b"]}function Dc(e,t,r){return r==null&&(r=b(5)),_t(t,r),r.write_shift(1,e.v?1:0),r}function Nc(e){var t=kr(e),r=e.read_shift(1);return[t,r,"e"]}function Rc(e,t,r){return r==null&&(r=b(9)),vt(t,r),r.write_shift(1,e.v),r}function Ic(e){var t=gt(e),r=e.read_shift(1);return[t,r,"e"]}function kc(e,t,r){return r==null&&(r=b(8)),_t(t,r),r.write_shift(1,e.v),r.write_shift(2,0),r.write_shift(1,0),r}function Pc(e){var t=kr(e),r=e.read_shift(4);return[t,r,"s"]}function Lc(e,t,r){return r==null&&(r=b(12)),vt(t,r),r.write_shift(4,t.v),r}function Bc(e){var t=gt(e),r=e.read_shift(4);return[t,r,"s"]}function Mc(e,t,r){return r==null&&(r=b(8)),_t(t,r),r.write_shift(4,t.v),r}function bc(e){var t=kr(e),r=Pt(e);return[t,r,"n"]}function Uc(e,t,r){return r==null&&(r=b(16)),vt(t,r),dt(e.v,r),r}function Wc(e){var t=gt(e),r=Pt(e);return[t,r,"n"]}function Vc(e,t,r){return r==null&&(r=b(12)),_t(t,r),dt(e.v,r),r}function Hc(e){var t=kr(e),r=Ja(e);return[t,r,"n"]}function Gc(e,t,r){return r==null&&(r=b(12)),vt(t,r),Za(e.v,r),r}function Xc(e){var t=gt(e),r=Ja(e);return[t,r,"n"]}function zc(e,t,r){return r==null&&(r=b(8)),_t(t,r),Za(e.v,r),r}function $c(e){var t=kr(e),r=v0(e);return[t,r,"is"]}function Kc(e){var t=kr(e),r=xr(e);return[t,r,"str"]}function Yc(e,t,r){return r==null&&(r=b(12+4*e.v.length)),vt(t,r),tr(e.v,r),r.length>r.l?r.slice(0,r.l):r}function jc(e){var t=gt(e),r=xr(e);return[t,r,"str"]}function qc(e,t,r){return r==null&&(r=b(8+4*e.v.length)),_t(t,r),tr(e.v,r),r.length>r.l?r.slice(0,r.l):r}function Jc(e,t,r){var n=e.l+t,a=kr(e);a.r=r["!row"];var i=e.read_shift(1),s=[a,i,"b"];if(r.cellFormula){e.l+=2;var f=bn(e,n-e.l,r);s[3]=Nt(f,null,a,r.supbooks,r)}else e.l=n;return s}function Zc(e,t,r){var n=e.l+t,a=kr(e);a.r=r["!row"];var i=e.read_shift(1),s=[a,i,"e"];if(r.cellFormula){e.l+=2;var f=bn(e,n-e.l,r);s[3]=Nt(f,null,a,r.supbooks,r)}else e.l=n;return s}function Qc(e,t,r){var n=e.l+t,a=kr(e);a.r=r["!row"];var i=Pt(e),s=[a,i,"n"];if(r.cellFormula){e.l+=2;var f=bn(e,n-e.l,r);s[3]=Nt(f,null,a,r.supbooks,r)}else e.l=n;return s}function e1(e,t,r){var n=e.l+t,a=kr(e);a.r=r["!row"];var i=xr(e),s=[a,i,"str"];if(r.cellFormula){e.l+=2;var f=bn(e,n-e.l,r);s[3]=Nt(f,null,a,r.supbooks,r)}else e.l=n;return s}var r1=Et,t1=kt;function n1(e,t){return t==null&&(t=b(4)),t.write_shift(4,e),t}function a1(e,t){var r=e.l+t,n=Et(e),a=g0(e),i=xr(e),s=xr(e),f=xr(e);e.l=r;var o={rfx:n,relId:a,loc:i,display:f};return s&&(o.Tooltip=s),o}function i1(e,t){var r=b(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));kt({s:rr(e[0]),e:rr(e[0])},r),_0("rId"+t,r);var n=e[1].Target.indexOf("#"),a=n==-1?"":e[1].Target.slice(n+1);return tr(a||"",r),tr(e[1].Tooltip||"",r),tr("",r),r.slice(0,r.l)}function s1(){}function f1(e,t,r){var n=e.l+t,a=Qa(e),i=e.read_shift(1),s=[a];if(s[2]=i,r.cellFormula){var f=ju(e,n-e.l,r);s[1]=f}else e.l=n;return s}function l1(e,t,r){var n=e.l+t,a=Et(e),i=[a];if(r.cellFormula){var s=Ju(e,n-e.l,r);i[1]=s,e.l=n}else e.l=n;return i}function o1(e,t,r){r==null&&(r=b(18));var n=Un(e,t);r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,(n.width||10)*256),r.write_shift(4,0);var a=0;return t.hidden&&(a|=1),typeof n.width=="number"&&(a|=2),t.level&&(a|=t.level<<8),r.write_shift(2,a),r}var Bi=["left","right","top","bottom","header","footer"];function u1(e){var t={};return Bi.forEach(function(r){t[r]=Pt(e)}),t}function c1(e,t){return t==null&&(t=b(6*8)),Pi(e),Bi.forEach(function(r){dt(e[r],t)}),t}function h1(e){var t=e.read_shift(2);return e.l+=28,{RTL:t&32}}function x1(e,t,r){r==null&&(r=b(30));var n=924;return(((t||{}).Views||[])[0]||{}).RTL&&(n|=32),r.write_shift(2,n),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,100),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(4,0),r}function d1(e){var t=b(24);return t.write_shift(4,4),t.write_shift(4,1),kt(e,t),t}function p1(e,t){return t==null&&(t=b(16*4+2)),t.write_shift(2,e.password?vi(e.password):0),t.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach(function(r){r[1]?t.write_shift(4,e[r[0]]!=null&&!e[r[0]]?1:0):t.write_shift(4,e[r[0]]!=null&&e[r[0]]?0:1)}),t}function m1(){}function v1(){}function g1(e,t,r,n,a,i,s){if(t.v===void 0)return!1;var f="";switch(t.t){case"b":f=t.v?"1":"0";break;case"d":t=Ar(t),t.z=t.z||Xe[14],t.v=wr(_r(t.v)),t.t="n";break;case"n":case"e":f=""+t.v;break;default:f=t.v;break}var o={r,c:n};switch(o.s=it(a.cellXfs,t,a),t.l&&i["!links"].push([Ie(o),t.l]),t.c&&i["!comments"].push([Ie(o),t.c]),t.t){case"s":case"str":return a.bookSST?(f=F0(a.Strings,t.v,a.revStrings),o.t="s",o.v=f,s?H(e,18,Mc(t,o)):H(e,7,Lc(t,o))):(o.t="str",s?H(e,17,qc(t,o)):H(e,6,Yc(t,o))),!0;case"n":return t.v==(t.v|0)&&t.v>-1e3&&t.v<1e3?s?H(e,13,zc(t,o)):H(e,2,Gc(t,o)):s?H(e,16,Vc(t,o)):H(e,5,Uc(t,o)),!0;case"b":return o.t="b",s?H(e,15,Dc(t,o)):H(e,4,yc(t,o)),!0;case"e":return o.t="e",s?H(e,14,kc(t,o)):H(e,3,Rc(t,o)),!0}return s?H(e,12,Fc(t,o)):H(e,1,Ac(t,o)),!0}function _1(e,t,r,n){var a=Ue(t["!ref"]||"A1"),i,s="",f=[];H(e,145);var o=Array.isArray(t),l=a.e.r;t["!rows"]&&(l=Math.max(a.e.r,t["!rows"].length-1));for(var u=a.s.r;u<=l;++u){s=fr(u),mc(e,t,a,u);var x=!1;if(u<=a.e.r)for(var d=a.s.c;d<=a.e.c;++d){u===a.s.r&&(f[d]=hr(d)),i=f[d]+s;var p=o?(t[u]||[])[d]:t[i];if(!p){x=!1;continue}x=g1(e,p,u,d,n,t,x)}}H(e,146)}function E1(e,t){!t||!t["!merges"]||(H(e,177,n1(t["!merges"].length)),t["!merges"].forEach(function(r){H(e,176,t1(r))}),H(e,178))}function T1(e,t){!t||!t["!cols"]||(H(e,390),t["!cols"].forEach(function(r,n){r&&H(e,60,o1(n,r))}),H(e,391))}function w1(e,t){!t||!t["!ref"]||(H(e,648),H(e,649,d1(Ue(t["!ref"]))),H(e,650))}function A1(e,t,r){t["!links"].forEach(function(n){if(!!n[1].Target){var a=Ne(r,-1,n[1].Target.replace(/#.*$/,""),Ce.HLINK);H(e,494,i1(n,a))}}),delete t["!links"]}function S1(e,t,r,n){if(t["!comments"].length>0){var a=Ne(n,-1,"../drawings/vmlDrawing"+(r+1)+".vml",Ce.VML);H(e,551,_0("rId"+a)),t["!legacy"]=a}}function F1(e,t,r,n){if(!!t["!autofilter"]){var a=t["!autofilter"],i=typeof a.ref=="string"?a.ref:qe(a.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,f=Nr(i);f.s.r==f.e.r&&(f.e.r=Nr(t["!ref"]).e.r,i=qe(f));for(var o=0;o<s.length;++o){var l=s[o];if(l.Name=="_xlnm._FilterDatabase"&&l.Sheet==n){l.Ref="'"+r.SheetNames[n]+"'!"+i;break}}o==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+i}),H(e,161,kt(Ue(i))),H(e,162)}}function C1(e,t,r){H(e,133),H(e,137,x1(t,r)),H(e,138),H(e,134)}function y1(e,t){!t["!protect"]||H(e,535,p1(t["!protect"]))}function O1(e,t,r,n){var a=Tr(),i=r.SheetNames[e],s=r.Sheets[i]||{},f=i;try{r&&r.Workbook&&(f=r.Workbook.Sheets[e].CodeName||f)}catch{}var o=Ue(s["!ref"]||"A1");if(o.e.c>16383||o.e.r>1048575){if(t.WTF)throw new Error("Range "+(s["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");o.e.c=Math.min(o.e.c,16383),o.e.r=Math.min(o.e.c,1048575)}return s["!links"]=[],s["!comments"]=[],H(a,129),(r.vbaraw||s["!outline"])&&H(a,147,Tc(f,s["!outline"])),H(a,148,gc(o)),C1(a,s,r.Workbook),T1(a,s),_1(a,s,e,t),y1(a,s),F1(a,s,r,e),E1(a,s),A1(a,s,n),s["!margins"]&&H(a,476,c1(s["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&w1(a,s),S1(a,s,e,n),H(a,130),a.end()}function D1(e,t){e.l+=10;var r=xr(e);return{name:r}}var N1=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]];function R1(e){return!e.Workbook||!e.Workbook.WBProps?"false":ff(e.Workbook.WBProps.date1904)?"true":"false"}var I1="][*?/\\".split("");function Mi(e,t){if(e.length>31){if(t)return!1;throw new Error("Sheet names cannot exceed 31 chars")}var r=!0;return I1.forEach(function(n){if(e.indexOf(n)!=-1){if(!t)throw new Error("Sheet name cannot contain : \\ / ? * [ ]");r=!1}}),r}function k1(e,t,r){e.forEach(function(n,a){Mi(n);for(var i=0;i<a;++i)if(n==e[i])throw new Error("Duplicate Sheet Name: "+n);if(r){var s=t&&t[a]&&t[a].CodeName||n;if(s.charCodeAt(0)==95&&s.length>22)throw new Error("Bad Code Name: Worksheet"+s)}})}function P1(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var t=e.Workbook&&e.Workbook.Sheets||[];k1(e.SheetNames,t,!!e.vbaraw);for(var r=0;r<e.SheetNames.length;++r)tc(e.Sheets[e.SheetNames[r]],e.SheetNames[r],r)}function bi(e){var t=[Je];t[t.length]=Z("workbook",null,{xmlns:Rt[0],"xmlns:r":er.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,n={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(N1.forEach(function(f){e.Workbook.WBProps[f[0]]!=null&&e.Workbook.WBProps[f[0]]!=f[1]&&(n[f[0]]=e.Workbook.WBProps[f[0]])}),e.Workbook.WBProps.CodeName&&(n.codeName=e.Workbook.WBProps.CodeName,delete n.CodeName)),t[t.length]=Z("workbookPr",null,n);var a=e.Workbook&&e.Workbook.Sheets||[],i=0;if(a&&a[0]&&!!a[0].Hidden){for(t[t.length]="<bookViews>",i=0;i!=e.SheetNames.length&&!(!a[i]||!a[i].Hidden);++i);i==e.SheetNames.length&&(i=0),t[t.length]='<workbookView firstSheet="'+i+'" activeTab="'+i+'"/>',t[t.length]="</bookViews>"}for(t[t.length]="<sheets>",i=0;i!=e.SheetNames.length;++i){var s={name:Re(e.SheetNames[i].slice(0,31))};if(s.sheetId=""+(i+1),s["r:id"]="rId"+(i+1),a[i])switch(a[i].Hidden){case 1:s.state="hidden";break;case 2:s.state="veryHidden";break}t[t.length]=Z("sheet",null,s)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach(function(f){var o={name:f.Name};f.Comment&&(o.comment=f.Comment),f.Sheet!=null&&(o.localSheetId=""+f.Sheet),f.Hidden&&(o.hidden="1"),f.Ref&&(t[t.length]=Z("definedName",Re(f.Ref),o))}),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function L1(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=i0(e),r.name=xr(e),r}function B1(e,t){return t||(t=b(127)),t.write_shift(4,e.Hidden),t.write_shift(4,e.iTabID),_0(e.strRelID,t),tr(e.name.slice(0,31),t),t.length>t.l?t.slice(0,t.l):t}function M1(e,t){var r={},n=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var a=t>8?xr(e):"";return a.length>0&&(r.CodeName=a),r.autoCompressPictures=!!(n&65536),r.backupFile=!!(n&64),r.checkCompatibility=!!(n&4096),r.date1904=!!(n&1),r.filterPrivacy=!!(n&8),r.hidePivotFieldList=!!(n&1024),r.promptedSolutions=!!(n&16),r.publishItems=!!(n&2048),r.refreshAllConnections=!!(n&262144),r.saveExternalLinkValues=!!(n&128),r.showBorderUnselectedTables=!!(n&4),r.showInkAnnotation=!!(n&32),r.showObjects=["all","placeholders","none"][n>>13&3],r.showPivotChartFilter=!!(n&32768),r.updateLinks=["userSet","never","always"][n>>8&3],r}function b1(e,t){t||(t=b(72));var r=0;return e&&e.filterPrivacy&&(r|=8),t.write_shift(4,r),t.write_shift(4,0),qa(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}function U1(e,t,r){var n=e.l+t;e.l+=4,e.l+=1;var a=e.read_shift(4),i=Nf(e),s=qu(e,0,r),f=g0(e);e.l=n;var o={Name:i,Ptg:s};return a<268435455&&(o.Sheet=a),f&&(o.Comment=f),o}function W1(e,t){H(e,143);for(var r=0;r!=t.SheetNames.length;++r){var n=t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,a={Hidden:n,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]};H(e,156,B1(a))}H(e,144)}function V1(e,t){t||(t=b(127));for(var r=0;r!=4;++r)t.write_shift(4,0);return tr("SheetJS",t),tr(gn.version,t),tr(gn.version,t),tr("7262",t),t.length>t.l?t.slice(0,t.l):t}function H1(e,t){t||(t=b(29)),t.write_shift(-4,0),t.write_shift(-4,460),t.write_shift(4,28800),t.write_shift(4,17600),t.write_shift(4,500),t.write_shift(4,e),t.write_shift(4,e);var r=120;return t.write_shift(1,r),t.length>t.l?t.slice(0,t.l):t}function G1(e,t){if(!(!t.Workbook||!t.Workbook.Sheets)){for(var r=t.Workbook.Sheets,n=0,a=-1,i=-1;n<r.length;++n)!r[n]||!r[n].Hidden&&a==-1?a=n:r[n].Hidden==1&&i==-1&&(i=n);i>a||(H(e,135),H(e,158,H1(a)),H(e,136))}}function X1(e,t){var r=Tr();return H(r,131),H(r,128,V1()),H(r,153,b1(e.Workbook&&e.Workbook.WBProps||null)),G1(r,e),W1(r,e),H(r,132),r.end()}function z1(e,t,r){return(t.slice(-4)===".bin"?X1:bi)(e)}function $1(e,t,r,n,a){return(t.slice(-4)===".bin"?O1:Li)(e,r,n,a)}function K1(e,t,r){return(t.slice(-4)===".bin"?uo:Ei)(e,r)}function Y1(e,t,r){return(t.slice(-4)===".bin"?Ml:mi)(e,r)}function j1(e,t,r){return(t.slice(-4)===".bin"?yo:Fi)(e)}function q1(e){return(e.slice(-4)===".bin"?_o:Ai)()}function J1(e,t){var r=[];return e.Props&&r.push($f(e.Props,t)),e.Custprops&&r.push(Kf(e.Props,e.Custprops)),r.join("")}function Z1(){return""}function Q1(e,t){var r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return t.cellXfs.forEach(function(n,a){var i=[];i.push(Z("NumberFormat",null,{"ss:Format":Re(Xe[n.numFmtId])}));var s={"ss:ID":"s"+(21+a)};r.push(Z("Style",i.join(""),s))}),Z("Styles",r.join(""))}function Ui(e){return Z("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+A0(e.Ref,{r:0,c:0})})}function eh(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],n=0;n<t.length;++n){var a=t[n];a.Sheet==null&&(a.Name.match(/^_xlfn\./)||r.push(Ui(a)))}return Z("Names",r.join(""))}function rh(e,t,r,n){if(!e||!((n||{}).Workbook||{}).Names)return"";for(var a=n.Workbook.Names,i=[],s=0;s<a.length;++s){var f=a[s];f.Sheet==r&&(f.Name.match(/^_xlfn\./)||i.push(Ui(f)))}return i.join("")}function th(e,t,r,n){if(!e)return"";var a=[];if(e["!margins"]&&(a.push("<PageSetup>"),e["!margins"].header&&a.push(Z("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&a.push(Z("Footer",null,{"x:Margin":e["!margins"].footer})),a.push(Z("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),a.push("</PageSetup>")),n&&n.Workbook&&n.Workbook.Sheets&&n.Workbook.Sheets[r])if(n.Workbook.Sheets[r].Hidden)a.push(Z("Visible",n.Workbook.Sheets[r].Hidden==1?"SheetHidden":"SheetVeryHidden",{}));else{for(var i=0;i<r&&!(n.Workbook.Sheets[i]&&!n.Workbook.Sheets[i].Hidden);++i);i==r&&a.push("<Selected/>")}return((((n||{}).Workbook||{}).Views||[])[0]||{}).RTL&&a.push("<DisplayRightToLeft/>"),e["!protect"]&&(a.push(sr("ProtectContents","True")),e["!protect"].objects&&a.push(sr("ProtectObjects","True")),e["!protect"].scenarios&&a.push(sr("ProtectScenarios","True")),e["!protect"].selectLockedCells!=null&&!e["!protect"].selectLockedCells?a.push(sr("EnableSelection","NoSelection")):e["!protect"].selectUnlockedCells!=null&&!e["!protect"].selectUnlockedCells&&a.push(sr("EnableSelection","UnlockedCells")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach(function(s){e["!protect"][s[0]]&&a.push("<"+s[1]+"/>")})),a.length==0?"":Z("WorksheetOptions",a.join(""),{xmlns:Or.x})}function nh(e){return e.map(function(t){var r=sf(t.t||""),n=Z("ss:Data",r,{xmlns:"http://www.w3.org/TR/REC-html40"});return Z("Comment",n,{"ss:Author":t.a})}).join("")}function ah(e,t,r,n,a,i,s){if(!e||e.v==null&&e.f==null)return"";var f={};if(e.f&&(f["ss:Formula"]="="+Re(A0(e.f,s))),e.F&&e.F.slice(0,t.length)==t){var o=rr(e.F.slice(t.length+1));f["ss:ArrayRange"]="RC:R"+(o.r==s.r?"":"["+(o.r-s.r)+"]")+"C"+(o.c==s.c?"":"["+(o.c-s.c)+"]")}if(e.l&&e.l.Target&&(f["ss:HRef"]=Re(e.l.Target),e.l.Tooltip&&(f["x:HRefScreenTip"]=Re(e.l.Tooltip))),r["!merges"])for(var l=r["!merges"],u=0;u!=l.length;++u)l[u].s.c!=s.c||l[u].s.r!=s.r||(l[u].e.c>l[u].s.c&&(f["ss:MergeAcross"]=l[u].e.c-l[u].s.c),l[u].e.r>l[u].s.r&&(f["ss:MergeDown"]=l[u].e.r-l[u].s.r));var x="",d="";switch(e.t){case"z":if(!n.sheetStubs)return"";break;case"n":x="Number",d=String(e.v);break;case"b":x="Boolean",d=e.v?"1":"0";break;case"e":x="Error",d=rn[e.v];break;case"d":x="DateTime",d=new Date(e.v).toISOString(),e.z==null&&(e.z=e.z||Xe[14]);break;case"s":x="String",d=af(e.v||"");break}var p=it(n.cellXfs,e,n);f["ss:StyleID"]="s"+(21+p),f["ss:Index"]=s.c+1;var g=e.v!=null?d:"",h=e.t=="z"?"":'<Data ss:Type="'+x+'">'+g+"</Data>";return(e.c||[]).length>0&&(h+=nh(e.c)),Z("Cell",h,f)}function ih(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=_i(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}function sh(e,t,r,n){if(!e["!ref"])return"";var a=Ue(e["!ref"]),i=e["!merges"]||[],s=0,f=[];e["!cols"]&&e["!cols"].forEach(function(_,N){T0(_);var O=!!_.width,F=Un(N,_),R={"ss:Index":N+1};O&&(R["ss:Width"]=yn(F.width)),_.hidden&&(R["ss:Hidden"]="1"),f.push(Z("Column",null,R))});for(var o=Array.isArray(e),l=a.s.r;l<=a.e.r;++l){for(var u=[ih(l,(e["!rows"]||[])[l])],x=a.s.c;x<=a.e.c;++x){var d=!1;for(s=0;s!=i.length;++s)if(!(i[s].s.c>x)&&!(i[s].s.r>l)&&!(i[s].e.c<x)&&!(i[s].e.r<l)){(i[s].s.c!=x||i[s].s.r!=l)&&(d=!0);break}if(!d){var p={r:l,c:x},g=Ie(p),h=o?(e[l]||[])[x]:e[g];u.push(ah(h,g,e,t,r,n,p))}}u.push("</Row>"),u.length>2&&f.push(u.join(""))}return f.join("")}function fh(e,t,r){var n=[],a=r.SheetNames[e],i=r.Sheets[a],s=i?rh(i,t,e,r):"";return s.length>0&&n.push("<Names>"+s+"</Names>"),s=i?sh(i,t,e,r):"",s.length>0&&n.push("<Table>"+s+"</Table>"),n.push(th(i,t,e,r)),n.join("")}function lh(e,t){t||(t={}),e.SSF||(e.SSF=Ar(Xe)),e.SSF&&(Pn(),kn(e.SSF),t.revssf=Ln(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],it(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(J1(e,t)),r.push(Z1()),r.push(""),r.push("");for(var n=0;n<e.SheetNames.length;++n)r.push(Z("Worksheet",fh(n,t,e),{"ss:Name":Re(e.SheetNames[n])}));return r[2]=Q1(e,t),r[3]=eh(e),Je+Z("Workbook",r.join(""),{xmlns:Or.ss,"xmlns:o":Or.o,"xmlns:x":Or.x,"xmlns:ss":Or.ss,"xmlns:dt":Or.dt,"xmlns:html":Or.html})}var e0={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function oh(e,t){var r=[],n=[],a=[],i=0,s,f=U0(Z0,"n"),o=U0(Q0,"n");if(e.Props)for(s=lr(e.Props),i=0;i<s.length;++i)(Object.prototype.hasOwnProperty.call(f,s[i])?r:Object.prototype.hasOwnProperty.call(o,s[i])?n:a).push([s[i],e.Props[s[i]]]);if(e.Custprops)for(s=lr(e.Custprops),i=0;i<s.length;++i)Object.prototype.hasOwnProperty.call(e.Props||{},s[i])||(Object.prototype.hasOwnProperty.call(f,s[i])?r:Object.prototype.hasOwnProperty.call(o,s[i])?n:a).push([s[i],e.Custprops[s[i]]]);var l=[];for(i=0;i<a.length;++i)oi.indexOf(a[i][0])>-1||si.indexOf(a[i][0])>-1||a[i][1]!=null&&l.push(a[i]);n.length&&ke.utils.cfb_add(t,"/SummaryInformation",aa(n,e0.SI,o,Q0)),(r.length||l.length)&&ke.utils.cfb_add(t,"/DocumentSummaryInformation",aa(r,e0.DSI,f,Z0,l.length?l:null,e0.UDI))}function uh(e,t){var r=t||{},n=ke.utils.cfb_new({root:"R"}),a="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":a="/Workbook",r.biff=8;break;case"biff5":a="/Book",r.biff=5;break;default:throw new Error("invalid type "+r.bookType+" for XLS CFB")}return ke.utils.cfb_add(n,a,Wi(e,r)),r.biff==8&&(e.Props||e.Custprops)&&oh(e,n),r.biff==8&&e.vbaraw&&Oo(n,ke.read(e.vbaraw,{type:typeof e.vbaraw=="string"?"binary":"buffer"})),n}var ch={0:{f:dc},1:{f:wc},2:{f:Hc},3:{f:Nc},4:{f:Cc},5:{f:bc},6:{f:Kc},7:{f:Pc},8:{f:e1},9:{f:Qc},10:{f:Jc},11:{f:Zc},12:{f:Sc},13:{f:Xc},14:{f:Ic},15:{f:Oc},16:{f:Wc},17:{f:jc},18:{f:Bc},19:{f:v0},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:U1},40:{},42:{},43:{f:$l},44:{f:Xl},45:{f:jl},46:{f:Jl},47:{f:ql},48:{},49:{f:Af},50:{},51:{f:xo},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:Sl},62:{f:$c},63:{f:Eo},64:{f:m1},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:Vr,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:h1},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:Ec},148:{f:vc,p:16},151:{f:s1},152:{},153:{f:M1},154:{},155:{},156:{f:L1},157:{},158:{},159:{T:1,f:Pl},160:{T:-1},161:{T:1,f:Et},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:r1},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:co},336:{T:-1},337:{f:vo,T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:i0},357:{},358:{},359:{},360:{T:1},361:{},362:{f:gl},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:f1},427:{f:l1},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:u1},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:_c},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:a1},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:i0},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:Fo},633:{T:1},634:{T:-1},635:{T:1,f:Ao},636:{T:-1},637:{f:yf},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:D1},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:v1},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}};function ee(e,t,r,n){var a=t;if(!isNaN(a)){var i=n||(r||[]).length||0,s=e.next(4);s.write_shift(2,a),s.write_shift(2,i),i>0&&d0(r)&&e.push(r)}}function hh(e,t,r,n){var a=n||(r||[]).length||0;if(a<=8224)return ee(e,t,r,a);var i=t;if(!isNaN(i)){for(var s=r.parts||[],f=0,o=0,l=0;l+(s[f]||8224)<=8224;)l+=s[f]||8224,f++;var u=e.next(4);for(u.write_shift(2,i),u.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l;o<a;){for(u=e.next(4),u.write_shift(2,60),l=0;l+(s[f]||8224)<=8224;)l+=s[f]||8224,f++;u.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l}}}function nn(e,t,r){return e||(e=b(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function xh(e,t,r,n){var a=b(9);return nn(a,e,t),ci(r,n||"b",a),a}function dh(e,t,r){var n=b(8+2*r.length);return nn(n,e,t),n.write_shift(1,r.length),n.write_shift(r.length,r,"sbcs"),n.l<n.length?n.slice(0,n.l):n}function ph(e,t,r,n){if(t.v!=null)switch(t.t){case"d":case"n":var a=t.t=="d"?wr(_r(t.v)):t.v;a==(a|0)&&a>=0&&a<65536?ee(e,2,Ol(r,n,a)):ee(e,3,yl(r,n,a));return;case"b":case"e":ee(e,5,xh(r,n,t.v,t.t));return;case"s":case"str":ee(e,4,dh(r,n,(t.v||"").slice(0,255)));return}ee(e,1,nn(null,r,n))}function mh(e,t,r,n){var a=Array.isArray(t),i=Ue(t["!ref"]||"A1"),s,f="",o=[];if(i.e.c>255||i.e.r>16383){if(n.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");i.e.c=Math.min(i.e.c,255),i.e.r=Math.min(i.e.c,16383),s=qe(i)}for(var l=i.s.r;l<=i.e.r;++l){f=fr(l);for(var u=i.s.c;u<=i.e.c;++u){l===i.s.r&&(o[u]=hr(u)),s=o[u]+f;var x=a?(t[l]||[])[u]:t[s];!x||ph(e,x,l,u)}}}function vh(e,t){for(var r=t||{},n=Tr(),a=0,i=0;i<e.SheetNames.length;++i)e.SheetNames[i]==r.sheet&&(a=i);if(a==0&&!!r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);return ee(n,r.biff==4?1033:r.biff==3?521:9,E0(e,16,r)),mh(n,e.Sheets[e.SheetNames[a]],a,r),ee(n,10),n.end()}function gh(e,t,r){ee(e,49,ul({sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"},r))}function _h(e,t,r){!t||[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var a=n[0];a<=n[1];++a)t[a]!=null&&ee(e,1054,xl(a,t[a],r))})}function Eh(e,t){var r=b(19);r.write_shift(4,2151),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,1),r.write_shift(4,0),ee(e,2151,r),r=b(39),r.write_shift(4,2152),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(2,1),r.write_shift(4,4),r.write_shift(2,0),di(Ue(t["!ref"]||"A1"),r),r.write_shift(4,4),ee(e,2152,r)}function Th(e,t){for(var r=0;r<16;++r)ee(e,224,sa({numFmtId:0,style:!0},0,t));t.cellXfs.forEach(function(n){ee(e,224,sa(n,0,t))})}function wh(e,t){for(var r=0;r<t["!links"].length;++r){var n=t["!links"][r];ee(e,440,Tl(n)),n[1].Tooltip&&ee(e,2048,wl(n))}delete t["!links"]}function Ah(e,t){if(!!t){var r=0;t.forEach(function(n,a){++r<=256&&n&&ee(e,125,Fl(Un(a,n),a))})}}function Sh(e,t,r,n,a){var i=16+it(a.cellXfs,t,a);if(t.v==null&&!t.bf){ee(e,513,pt(r,n,i));return}if(t.bf)ee(e,6,Yu(t,r,n,a,i));else switch(t.t){case"d":case"n":var s=t.t=="d"?wr(_r(t.v)):t.v;ee(e,515,vl(r,n,s,i));break;case"b":case"e":ee(e,517,ml(r,n,t.v,i,a,t.t));break;case"s":case"str":if(a.bookSST){var f=F0(a.Strings,t.v,a.revStrings);ee(e,253,cl(r,n,f,i))}else ee(e,516,hl(r,n,(t.v||"").slice(0,255),i,a));break;default:ee(e,513,pt(r,n,i))}}function Fh(e,t,r){var n=Tr(),a=r.SheetNames[e],i=r.Sheets[a]||{},s=(r||{}).Workbook||{},f=(s.Sheets||[])[e]||{},o=Array.isArray(i),l=t.biff==8,u,x="",d=[],p=Ue(i["!ref"]||"A1"),g=l?65536:16384;if(p.e.c>255||p.e.r>=g){if(t.WTF)throw new Error("Range "+(i["!ref"]||"A1")+" exceeds format limit A1:IV16384");p.e.c=Math.min(p.e.c,255),p.e.r=Math.min(p.e.c,g-1)}ee(n,2057,E0(r,16,t)),ee(n,13,Ir(1)),ee(n,12,Ir(100)),ee(n,15,gr(!0)),ee(n,17,gr(!1)),ee(n,16,dt(.001)),ee(n,95,gr(!0)),ee(n,42,gr(!1)),ee(n,43,gr(!1)),ee(n,130,Ir(1)),ee(n,128,pl([0,0])),ee(n,131,gr(!1)),ee(n,132,gr(!1)),l&&Ah(n,i["!cols"]),ee(n,512,dl(p,t)),l&&(i["!links"]=[]);for(var h=p.s.r;h<=p.e.r;++h){x=fr(h);for(var _=p.s.c;_<=p.e.c;++_){h===p.s.r&&(d[_]=hr(_)),u=d[_]+x;var N=o?(i[h]||[])[_]:i[u];!N||(Sh(n,N,h,_,t),l&&N.l&&i["!links"].push([u,N.l]))}}var O=f.CodeName||f.name||a;return l&&ee(n,574,ol((s.Views||[])[0])),l&&(i["!merges"]||[]).length&&ee(n,229,El(i["!merges"])),l&&wh(n,i),ee(n,442,xi(O)),l&&Eh(n,i),ee(n,10),n.end()}function Ch(e,t,r){var n=Tr(),a=(e||{}).Workbook||{},i=a.Sheets||[],s=a.WBProps||{},f=r.biff==8,o=r.biff==5;if(ee(n,2057,E0(e,5,r)),r.bookType=="xla"&&ee(n,135),ee(n,225,f?Ir(1200):null),ee(n,193,qf(2)),o&&ee(n,191),o&&ee(n,192),ee(n,226),ee(n,92,il("SheetJS",r)),ee(n,66,Ir(f?1200:1252)),f&&ee(n,353,Ir(0)),f&&ee(n,448),ee(n,317,Cl(e.SheetNames.length)),f&&e.vbaraw&&ee(n,211),f&&e.vbaraw){var l=s.CodeName||"ThisWorkbook";ee(n,442,xi(l))}ee(n,156,Ir(17)),ee(n,25,gr(!1)),ee(n,18,gr(!1)),ee(n,19,Ir(0)),f&&ee(n,431,gr(!1)),f&&ee(n,444,Ir(0)),ee(n,61,ll()),ee(n,64,gr(!1)),ee(n,141,Ir(0)),ee(n,34,gr(R1(e)=="true")),ee(n,14,gr(!0)),f&&ee(n,439,gr(!1)),ee(n,218,Ir(0)),gh(n,e,r),_h(n,e.SSF,r),Th(n,r),f&&ee(n,352,gr(!1));var u=n.end(),x=Tr();f&&ee(x,140,Al()),f&&r.Strings&&hh(x,252,fl(r.Strings)),ee(x,10);var d=x.end(),p=Tr(),g=0,h=0;for(h=0;h<e.SheetNames.length;++h)g+=(f?12:11)+(f?2:1)*e.SheetNames[h].length;var _=u.length+g+d.length;for(h=0;h<e.SheetNames.length;++h){var N=i[h]||{};ee(p,133,sl({pos:_,hs:N.Hidden||0,dt:0,name:e.SheetNames[h]},r)),_+=t[h].length}var O=p.end();if(g!=O.length)throw new Error("BS8 "+g+" != "+O.length);var F=[];return u.length&&F.push(u),O.length&&F.push(O),d.length&&F.push(d),ir(F)}function yh(e,t){var r=t||{},n=[];e&&!e.SSF&&(e.SSF=Ar(Xe)),e&&e.SSF&&(Pn(),kn(e.SSF),r.revssf=Ln(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,C0(r),r.cellXfs=[],it(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var a=0;a<e.SheetNames.length;++a)n[n.length]=Fh(a,r,e);return n.unshift(Ch(e,n,r)),ir(n)}function Wi(e,t){for(var r=0;r<=e.SheetNames.length;++r){var n=e.Sheets[e.SheetNames[r]];if(!(!n||!n["!ref"])){var a=Nr(n["!ref"]);a.e.c>255&&typeof console!="undefined"&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}}var i=t||{};switch(i.biff||2){case 8:case 5:return yh(e,t);case 4:case 3:case 2:return vh(e,t)}throw new Error("invalid type "+i.bookType+" for BIFF")}function Oh(e,t,r,n){for(var a=e["!merges"]||[],i=[],s=t.s.c;s<=t.e.c;++s){for(var f=0,o=0,l=0;l<a.length;++l)if(!(a[l].s.r>r||a[l].s.c>s)&&!(a[l].e.r<r||a[l].e.c<s)){if(a[l].s.r<r||a[l].s.c<s){f=-1;break}f=a[l].e.r-a[l].s.r+1,o=a[l].e.c-a[l].s.c+1;break}if(!(f<0)){var u=Ie({r,c:s}),x=n.dense?(e[r]||[])[s]:e[u],d=x&&x.v!=null&&(x.h||nf(x.w||(Yr(x),x.w)||""))||"",p={};f>1&&(p.rowspan=f),o>1&&(p.colspan=o),n.editable?d='<span contenteditable="true">'+d+"</span>":x&&(p["data-t"]=x&&x.t||"z",x.v!=null&&(p["data-v"]=x.v),x.z!=null&&(p["data-z"]=x.z),x.l&&(x.l.Target||"#").charAt(0)!="#"&&(d='<a href="'+x.l.Target+'">'+d+"</a>")),p.id=(n.id||"sjs")+"-"+u,i.push(Z("td",d,p))}}var g="<tr>";return g+i.join("")+"</tr>"}var Dh='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',Nh="</body></html>";function Rh(e,t,r){var n=[];return n.join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function Vi(e,t){var r=t||{},n=r.header!=null?r.header:Dh,a=r.footer!=null?r.footer:Nh,i=[n],s=Nr(e["!ref"]);r.dense=Array.isArray(e),i.push(Rh(e,s,r));for(var f=s.s.r;f<=s.e.r;++f)i.push(Oh(e,s,f,r));return i.push("</table>"+a),i.join("")}function Hi(e,t,r){var n=r||{},a=0,i=0;if(n.origin!=null)if(typeof n.origin=="number")a=n.origin;else{var s=typeof n.origin=="string"?rr(n.origin):n.origin;a=s.r,i=s.c}var f=t.getElementsByTagName("tr"),o=Math.min(n.sheetRows||1e7,f.length),l={s:{r:0,c:0},e:{r:a,c:i}};if(e["!ref"]){var u=Nr(e["!ref"]);l.s.r=Math.min(l.s.r,u.s.r),l.s.c=Math.min(l.s.c,u.s.c),l.e.r=Math.max(l.e.r,u.e.r),l.e.c=Math.max(l.e.c,u.e.c),a==-1&&(l.e.r=a=u.e.r+1)}var x=[],d=0,p=e["!rows"]||(e["!rows"]=[]),g=0,h=0,_=0,N=0,O=0,F=0;for(e["!cols"]||(e["!cols"]=[]);g<f.length&&h<o;++g){var R=f[g];if(xa(R)){if(n.display)continue;p[h]={hidden:!0}}var q=R.children;for(_=N=0;_<q.length;++_){var ne=q[_];if(!(n.display&&xa(ne))){var D=ne.hasAttribute("data-v")?ne.getAttribute("data-v"):ne.hasAttribute("v")?ne.getAttribute("v"):lf(ne.innerHTML),W=ne.getAttribute("data-z")||ne.getAttribute("z");for(d=0;d<x.length;++d){var M=x[d];M.s.c==N+i&&M.s.r<h+a&&h+a<=M.e.r&&(N=M.e.c+1-i,d=-1)}F=+ne.getAttribute("colspan")||1,((O=+ne.getAttribute("rowspan")||1)>1||F>1)&&x.push({s:{r:h+a,c:N+i},e:{r:h+a+(O||1)-1,c:N+i+(F||1)-1}});var X={t:"s",v:D},z=ne.getAttribute("data-t")||ne.getAttribute("t")||"";D!=null&&(D.length==0?X.t=z||"z":n.raw||D.trim().length==0||z=="s"||(D==="TRUE"?X={t:"b",v:!0}:D==="FALSE"?X={t:"b",v:!1}:isNaN(zr(D))?isNaN(Yt(D).getDate())||(X={t:"d",v:_r(D)},n.cellDates||(X={t:"n",v:wr(X.v)}),X.z=n.dateNF||Xe[14]):X={t:"n",v:zr(D)})),X.z===void 0&&W!=null&&(X.z=W);var K="",ie=ne.getElementsByTagName("A");if(ie&&ie.length)for(var Ae=0;Ae<ie.length&&!(ie[Ae].hasAttribute("href")&&(K=ie[Ae].getAttribute("href"),K.charAt(0)!="#"));++Ae);K&&K.charAt(0)!="#"&&(X.l={Target:K}),n.dense?(e[h+a]||(e[h+a]=[]),e[h+a][N+i]=X):e[Ie({c:N+i,r:h+a})]=X,l.e.c<N+i&&(l.e.c=N+i),N+=F}}++h}return x.length&&(e["!merges"]=(e["!merges"]||[]).concat(x)),l.e.r=Math.max(l.e.r,h-1+a),e["!ref"]=qe(l),h>=o&&(e["!fullref"]=qe((l.e.r=f.length-g+h-1+a,l))),e}function Gi(e,t){var r=t||{},n=r.dense?[]:{};return Hi(n,e,t)}function Ih(e,t){return mt(Gi(e,t),t)}function xa(e){var t="",r=kh(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),t==="none"}function kh(e){return e.ownerDocument.defaultView&&typeof e.ownerDocument.defaultView.getComputedStyle=="function"?e.ownerDocument.defaultView.getComputedStyle:typeof getComputedStyle=="function"?getComputedStyle:null}var Ph=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),t="<office:document-styles "+jt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return Je+t}}(),da=function(){var e=function(i){return Re(i).replace(/  +/g,function(s){return'<text:s text:c="'+s.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>")},t=`          <table:table-cell />
`,r=`          <table:covered-table-cell/>
`,n=function(i,s,f){var o=[];o.push('      <table:table table:name="'+Re(s.SheetNames[f])+`" table:style-name="ta1">
`);var l=0,u=0,x=Nr(i["!ref"]||"A1"),d=i["!merges"]||[],p=0,g=Array.isArray(i);if(i["!cols"])for(u=0;u<=x.e.c;++u)o.push("        <table:table-column"+(i["!cols"][u]?' table:style-name="co'+i["!cols"][u].ods+'"':"")+`></table:table-column>
`);var h="",_=i["!rows"]||[];for(l=0;l<x.s.r;++l)h=_[l]?' table:style-name="ro'+_[l].ods+'"':"",o.push("        <table:table-row"+h+`></table:table-row>
`);for(;l<=x.e.r;++l){for(h=_[l]?' table:style-name="ro'+_[l].ods+'"':"",o.push("        <table:table-row"+h+`>
`),u=0;u<x.s.c;++u)o.push(t);for(;u<=x.e.c;++u){var N=!1,O={},F="";for(p=0;p!=d.length;++p)if(!(d[p].s.c>u)&&!(d[p].s.r>l)&&!(d[p].e.c<u)&&!(d[p].e.r<l)){(d[p].s.c!=u||d[p].s.r!=l)&&(N=!0),O["table:number-columns-spanned"]=d[p].e.c-d[p].s.c+1,O["table:number-rows-spanned"]=d[p].e.r-d[p].s.r+1;break}if(N){o.push(r);continue}var R=Ie({r:l,c:u}),q=g?(i[l]||[])[u]:i[R];if(q&&q.f&&(O["table:formula"]=Re(ec(q.f)),q.F&&q.F.slice(0,R.length)==R)){var ne=Nr(q.F);O["table:number-matrix-columns-spanned"]=ne.e.c-ne.s.c+1,O["table:number-matrix-rows-spanned"]=ne.e.r-ne.s.r+1}if(!q){o.push(t);continue}switch(q.t){case"b":F=q.v?"TRUE":"FALSE",O["office:value-type"]="boolean",O["office:boolean-value"]=q.v?"true":"false";break;case"n":F=q.w||String(q.v||0),O["office:value-type"]="float",O["office:value"]=q.v||0;break;case"s":case"str":F=q.v==null?"":q.v,O["office:value-type"]="string";break;case"d":F=q.w||_r(q.v).toISOString(),O["office:value-type"]="date",O["office:date-value"]=_r(q.v).toISOString(),O["table:style-name"]="ce1";break;default:o.push(t);continue}var D=e(F);if(q.l&&q.l.Target){var W=q.l.Target;W=W.charAt(0)=="#"?"#"+rc(W.slice(1)):W,W.charAt(0)!="#"&&!W.match(/^\w+:/)&&(W="../"+W),D=Z("text:a",D,{"xlink:href":W.replace(/&/g,"&amp;")})}o.push("          "+Z("table:table-cell",Z("text:p",D,{}),O)+`
`)}o.push(`        </table:table-row>
`)}return o.push(`      </table:table>
`),o.join("")},a=function(i,s){i.push(` <office:automatic-styles>
`),i.push(`  <number:date-style style:name="N37" number:automatic-order="true">
`),i.push(`   <number:month number:style="long"/>
`),i.push(`   <number:text>/</number:text>
`),i.push(`   <number:day number:style="long"/>
`),i.push(`   <number:text>/</number:text>
`),i.push(`   <number:year/>
`),i.push(`  </number:date-style>
`);var f=0;s.SheetNames.map(function(l){return s.Sheets[l]}).forEach(function(l){if(!!l&&l["!cols"]){for(var u=0;u<l["!cols"].length;++u)if(l["!cols"][u]){var x=l["!cols"][u];if(x.width==null&&x.wpx==null&&x.wch==null)continue;T0(x),x.ods=f;var d=l["!cols"][u].wpx+"px";i.push('  <style:style style:name="co'+f+`" style:family="table-column">
`),i.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+d+`"/>
`),i.push(`  </style:style>
`),++f}}});var o=0;s.SheetNames.map(function(l){return s.Sheets[l]}).forEach(function(l){if(!!l&&l["!rows"]){for(var u=0;u<l["!rows"].length;++u)if(l["!rows"][u]){l["!rows"][u].ods=o;var x=l["!rows"][u].hpx+"px";i.push('  <style:style style:name="ro'+o+`" style:family="table-row">
`),i.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+x+`"/>
`),i.push(`  </style:style>
`),++o}}}),i.push(`  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">
`),i.push(`   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>
`),i.push(`  </style:style>
`),i.push(`  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>
`),i.push(` </office:automatic-styles>
`)};return function(s,f){var o=[Je],l=jt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),u=jt({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});f.bookType=="fods"?(o.push("<office:document"+l+u+`>
`),o.push(ai().replace(/office:document-meta/g,"office:meta"))):o.push("<office:document-content"+l+`>
`),a(o,s),o.push(`  <office:body>
`),o.push(`    <office:spreadsheet>
`);for(var x=0;x!=s.SheetNames.length;++x)o.push(n(s.Sheets[s.SheetNames[x]],s,x));return o.push(`    </office:spreadsheet>
`),o.push(`  </office:body>
`),f.bookType=="fods"?o.push("</office:document>"):o.push("</office:document-content>"),o.join("")}}();function Xi(e,t){if(t.bookType=="fods")return da(e,t);var r=u0(),n="",a=[],i=[];return n="mimetype",Ee(r,n,"application/vnd.oasis.opendocument.spreadsheet"),n="content.xml",Ee(r,n,da(e,t)),a.push([n,"text/xml"]),i.push([n,"ContentFile"]),n="styles.xml",Ee(r,n,Ph(e,t)),a.push([n,"text/xml"]),i.push([n,"StylesFile"]),n="meta.xml",Ee(r,n,Je+ai()),a.push([n,"text/xml"]),i.push([n,"MetadataFile"]),n="manifest.rdf",Ee(r,n,zf(i)),a.push([n,"application/rdf+xml"]),n="META-INF/manifest.xml",Ee(r,n,Gf(a)),r}/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function Nn(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function Lh(e){return typeof TextEncoder!="undefined"?new TextEncoder().encode(e):Br(Gr(e))}function Bh(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var n=0;n<t.length;++n)if(e[r+n]!=t[n])continue e;return!0}return!1}function at(e){var t=e.reduce(function(a,i){return a+i.length},0),r=new Uint8Array(t),n=0;return e.forEach(function(a){r.set(a,n),n+=a.length}),r}function Mh(e,t,r){var n=Math.floor(r==0?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,a=r/Math.pow(10,n-6176);e[t+15]|=n>>7,e[t+14]|=(n&127)<<1;for(var i=0;a>=1;++i,a/=256)e[t+i]=a&255;e[t+15]|=r>=0?0:128}function qt(e,t){var r=t?t[0]:0,n=e[r]&127;e:if(e[r++]>=128&&(n|=(e[r]&127)<<7,e[r++]<128||(n|=(e[r]&127)<<14,e[r++]<128)||(n|=(e[r]&127)<<21,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,28),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,35),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,42),++r,e[r++]<128)))break e;return t&&(t[0]=r),n}function De(e){var t=new Uint8Array(7);t[0]=e&127;var r=1;e:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383||(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)||(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)||(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)||(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103))break e;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t.slice(0,r)}function Dt(e){var t=0,r=e[t]&127;e:if(e[t++]>=128){if(r|=(e[t]&127)<<7,e[t++]<128||(r|=(e[t]&127)<<14,e[t++]<128)||(r|=(e[t]&127)<<21,e[t++]<128))break e;r|=(e[t]&127)<<28}return r}function Ze(e){for(var t=[],r=[0];r[0]<e.length;){var n=r[0],a=qt(e,r),i=a&7;a=Math.floor(a/8);var s=0,f;if(a==0)break;switch(i){case 0:{for(var o=r[0];e[r[0]++]>=128;);f=e.slice(o,r[0])}break;case 5:s=4,f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 1:s=8,f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 2:s=qt(e,r),f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 3:case 4:default:throw new Error("PB Type ".concat(i," for Field ").concat(a," at offset ").concat(n))}var l={data:f,type:i};t[a]==null?t[a]=[l]:t[a].push(l)}return t}function nr(e){var t=[];return e.forEach(function(r,n){r.forEach(function(a){!a.data||(t.push(De(n*8+a.type)),a.type==2&&t.push(De(a.data.length)),t.push(a.data))})}),at(t)}function Pr(e){for(var t,r=[],n=[0];n[0]<e.length;){var a=qt(e,n),i=Ze(e.slice(n[0],n[0]+a));n[0]+=a;var s={id:Dt(i[1][0].data),messages:[]};i[2].forEach(function(f){var o=Ze(f.data),l=Dt(o[3][0].data);s.messages.push({meta:o,data:e.slice(n[0],n[0]+l)}),n[0]+=l}),(t=i[3])!=null&&t[0]&&(s.merge=Dt(i[3][0].data)>>>0>0),r.push(s)}return r}function wt(e){var t=[];return e.forEach(function(r){var n=[];n[1]=[{data:De(r.id),type:0}],n[2]=[],r.merge!=null&&(n[3]=[{data:De(+!!r.merge),type:0}]);var a=[];r.messages.forEach(function(s){a.push(s.data),s.meta[3]=[{type:0,data:De(s.data.length)}],n[2].push({data:nr(s.meta),type:2})});var i=nr(n);t.push(De(i.length)),t.push(i),a.forEach(function(s){return t.push(s)})}),at(t)}function bh(e,t){if(e!=0)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],n=qt(t,r),a=[];r[0]<t.length;){var i=t[r[0]]&3;if(i==0){var s=t[r[0]++]>>2;if(s<60)++s;else{var f=s-59;s=t[r[0]],f>1&&(s|=t[r[0]+1]<<8),f>2&&(s|=t[r[0]+2]<<16),f>3&&(s|=t[r[0]+3]<<24),s>>>=0,s++,r[0]+=f}a.push(t.slice(r[0],r[0]+s)),r[0]+=s;continue}else{var o=0,l=0;if(i==1?(l=(t[r[0]]>>2&7)+4,o=(t[r[0]++]&224)<<3,o|=t[r[0]++]):(l=(t[r[0]++]>>2)+1,i==2?(o=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(o=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),a=[at(a)],o==0)throw new Error("Invalid offset 0");if(o>a[0].length)throw new Error("Invalid offset beyond length");if(l>=o)for(a.push(a[0].slice(-o)),l-=o;l>=a[a.length-1].length;)a.push(a[a.length-1]),l-=a[a.length-1].length;a.push(a[0].slice(-o,-o+l))}}var u=at(a);if(u.length!=n)throw new Error("Unexpected length: ".concat(u.length," != ").concat(n));return u}function Lr(e){for(var t=[],r=0;r<e.length;){var n=e[r++],a=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(bh(n,e.slice(r,r+a))),r+=a}if(r!==e.length)throw new Error("data is not a valid framed stream!");return at(t)}function At(e){for(var t=[],r=0;r<e.length;){var n=Math.min(e.length-r,268435455),a=new Uint8Array(4);t.push(a);var i=De(n),s=i.length;t.push(i),n<=60?(s++,t.push(new Uint8Array([n-1<<2]))):n<=256?(s+=2,t.push(new Uint8Array([240,n-1&255]))):n<=65536?(s+=3,t.push(new Uint8Array([244,n-1&255,n-1>>8&255]))):n<=16777216?(s+=4,t.push(new Uint8Array([248,n-1&255,n-1>>8&255,n-1>>16&255]))):n<=4294967296&&(s+=5,t.push(new Uint8Array([252,n-1&255,n-1>>8&255,n-1>>16&255,n-1>>>24&255]))),t.push(e.slice(r,r+n)),s+=n,a[0]=0,a[1]=s&255,a[2]=s>>8&255,a[3]=s>>16&255,r+=n}return at(t)}function r0(e,t){var r=new Uint8Array(32),n=Nn(r),a=12,i=0;switch(r[0]=5,e.t){case"n":r[1]=2,Mh(r,a,e.v),i|=1,a+=16;break;case"b":r[1]=6,n.setFloat64(a,e.v?1:0,!0),i|=2,a+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[1]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=8,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(8,i,!0),r.slice(0,a)}function t0(e,t){var r=new Uint8Array(32),n=Nn(r),a=12,i=0;switch(r[0]=3,e.t){case"n":r[2]=2,n.setFloat64(a,e.v,!0),i|=32,a+=8;break;case"b":r[2]=6,n.setFloat64(a,e.v?1:0,!0),i|=32,a+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[2]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=16,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(4,i,!0),r.slice(0,a)}function Qr(e){var t=Ze(e);return qt(t[1][0].data)}function Uh(e,t,r){var n,a,i,s;if(!((n=e[6])!=null&&n[0])||!((a=e[7])!=null&&a[0]))throw"Mutation only works on post-BNC storages!";var f=((s=(i=e[8])==null?void 0:i[0])==null?void 0:s.data)&&Dt(e[8][0].data)>0||!1;if(f)throw"Math only works with normal offsets";for(var o=0,l=Nn(e[7][0].data),u=0,x=[],d=Nn(e[4][0].data),p=0,g=[],h=0;h<t.length;++h){if(t[h]==null){l.setUint16(h*2,65535,!0),d.setUint16(h*2,65535);continue}l.setUint16(h*2,u,!0),d.setUint16(h*2,p,!0);var _,N;switch(typeof t[h]){case"string":_=r0({t:"s",v:t[h]},r),N=t0({t:"s",v:t[h]},r);break;case"number":_=r0({t:"n",v:t[h]},r),N=t0({t:"n",v:t[h]},r);break;case"boolean":_=r0({t:"b",v:t[h]},r),N=t0({t:"b",v:t[h]},r);break;default:throw new Error("Unsupported value "+t[h])}x.push(_),u+=_.length,g.push(N),p+=N.length,++o}for(e[2][0].data=De(o);h<e[7][0].data.length/2;++h)l.setUint16(h*2,65535,!0),d.setUint16(h*2,65535,!0);return e[6][0].data=at(x),e[3][0].data=at(g),o}function Wh(e,t){if(!t||!t.numbers)throw new Error("Must pass a `numbers` option -- check the README");var r=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var n=Nr(r["!ref"]);n.s.r=n.s.c=0;var a=!1;n.e.c>9&&(a=!0,n.e.c=9),n.e.r>49&&(a=!0,n.e.r=49),a&&console.error("The Numbers writer is currently limited to ".concat(qe(n)));var i=Rn(r,{range:n,header:1}),s=["~Sh33tJ5~"];i.forEach(function(B){return B.forEach(function(y){typeof y=="string"&&s.push(y)})});var f={},o=[],l=ke.read(t.numbers,{type:"base64"});l.FileIndex.map(function(B,y){return[B,l.FullPaths[y]]}).forEach(function(B){var y=B[0],C=B[1];if(y.type==2&&!!y.name.match(/\.iwa/)){var G=y.content,ce=Lr(G),he=Pr(ce);he.forEach(function(ue){o.push(ue.id),f[ue.id]={deps:[],location:C,type:Dt(ue.messages[0].meta[1][0].data)}})}}),o.sort(function(B,y){return B-y});var u=o.filter(function(B){return B>1}).map(function(B){return[B,De(B)]});l.FileIndex.map(function(B,y){return[B,l.FullPaths[y]]}).forEach(function(B){var y=B[0];if(B[1],!!y.name.match(/\.iwa/)){var C=Pr(Lr(y.content));C.forEach(function(G){G.messages.forEach(function(ce){u.forEach(function(he){G.messages.some(function(ue){return Dt(ue.meta[1][0].data)!=11006&&Bh(ue.data,he[1])})&&f[he[0]].deps.push(G.id)})})})}});for(var x=ke.find(l,f[1].location),d=Pr(Lr(x.content)),p,g=0;g<d.length;++g){var h=d[g];h.id==1&&(p=h)}var _=Qr(Ze(p.messages[0].data)[1][0].data);for(x=ke.find(l,f[_].location),d=Pr(Lr(x.content)),g=0;g<d.length;++g)h=d[g],h.id==_&&(p=h);for(_=Qr(Ze(p.messages[0].data)[2][0].data),x=ke.find(l,f[_].location),d=Pr(Lr(x.content)),g=0;g<d.length;++g)h=d[g],h.id==_&&(p=h);for(_=Qr(Ze(p.messages[0].data)[2][0].data),x=ke.find(l,f[_].location),d=Pr(Lr(x.content)),g=0;g<d.length;++g)h=d[g],h.id==_&&(p=h);var N=Ze(p.messages[0].data);{N[6][0].data=De(n.e.r+1),N[7][0].data=De(n.e.c+1);var O=Qr(N[46][0].data),F=ke.find(l,f[O].location),R=Pr(Lr(F.content));{for(var q=0;q<R.length&&R[q].id!=O;++q);if(R[q].id!=O)throw"Bad ColumnRowUIDMapArchive";var ne=Ze(R[q].messages[0].data);ne[1]=[],ne[2]=[],ne[3]=[];for(var D=0;D<=n.e.c;++D){var W=[];W[1]=W[2]=[{type:0,data:De(D+420690)}],ne[1].push({type:2,data:nr(W)}),ne[2].push({type:0,data:De(D)}),ne[3].push({type:0,data:De(D)})}ne[4]=[],ne[5]=[],ne[6]=[];for(var M=0;M<=n.e.r;++M)W=[],W[1]=W[2]=[{type:0,data:De(M+726270)}],ne[4].push({type:2,data:nr(W)}),ne[5].push({type:0,data:De(M)}),ne[6].push({type:0,data:De(M)});R[q].messages[0].data=nr(ne)}F.content=At(wt(R)),F.size=F.content.length,delete N[46];var X=Ze(N[4][0].data);{X[7][0].data=De(n.e.r+1);var z=Ze(X[1][0].data),K=Qr(z[2][0].data);F=ke.find(l,f[K].location),R=Pr(Lr(F.content));{if(R[0].id!=K)throw"Bad HeaderStorageBucket";var ie=Ze(R[0].messages[0].data);for(M=0;M<i.length;++M){var Ae=Ze(ie[2][0].data);Ae[1][0].data=De(M),Ae[4][0].data=De(i[M].length),ie[2][M]={type:ie[2][0].type,data:nr(Ae)}}R[0].messages[0].data=nr(ie)}F.content=At(wt(R)),F.size=F.content.length;var me=Qr(X[2][0].data);F=ke.find(l,f[me].location),R=Pr(Lr(F.content));{if(R[0].id!=me)throw"Bad HeaderStorageBucket";for(ie=Ze(R[0].messages[0].data),D=0;D<=n.e.c;++D)Ae=Ze(ie[2][0].data),Ae[1][0].data=De(D),Ae[4][0].data=De(n.e.r+1),ie[2][D]={type:ie[2][0].type,data:nr(Ae)};R[0].messages[0].data=nr(ie)}F.content=At(wt(R)),F.size=F.content.length;var We=Qr(X[4][0].data);(function(){for(var B=ke.find(l,f[We].location),y=Pr(Lr(B.content)),C,G=0;G<y.length;++G){var ce=y[G];ce.id==We&&(C=ce)}var he=Ze(C.messages[0].data);{he[3]=[];var ue=[];s.forEach(function(we,Qe){ue[1]=[{type:0,data:De(Qe)}],ue[2]=[{type:0,data:De(1)}],ue[3]=[{type:2,data:Lh(we)}],he[3].push({type:2,data:nr(ue)})})}C.messages[0].data=nr(he);var ae=wt(y),Oe=At(ae);B.content=Oe,B.size=B.content.length})();var Pe=Ze(X[3][0].data);{var Er=Pe[1][0];delete Pe[2];var ze=Ze(Er.data);{var dr=Qr(ze[2][0].data);(function(){for(var B=ke.find(l,f[dr].location),y=Pr(Lr(B.content)),C,G=0;G<y.length;++G){var ce=y[G];ce.id==dr&&(C=ce)}var he=Ze(C.messages[0].data);{delete he[6],delete Pe[7];var ue=new Uint8Array(he[5][0].data);he[5]=[];for(var ae=0,Oe=0;Oe<=n.e.r;++Oe){var we=Ze(ue);ae+=Uh(we,i[Oe],s),we[1][0].data=De(Oe),he[5].push({data:nr(we),type:2})}he[1]=[{type:0,data:De(n.e.c+1)}],he[2]=[{type:0,data:De(n.e.r+1)}],he[3]=[{type:0,data:De(ae)}],he[4]=[{type:0,data:De(n.e.r+1)}]}C.messages[0].data=nr(he);var Qe=wt(y),Fe=At(Qe);B.content=Fe,B.size=B.content.length})()}Er.data=nr(ze)}X[3][0].data=nr(Pe)}N[4][0].data=nr(X)}p.messages[0].data=nr(N);var pr=wt(d),S=At(pr);return x.content=S,x.size=x.content.length,l}function Vh(e){return function(r){for(var n=0;n!=e.length;++n){var a=e[n];r[a[0]]===void 0&&(r[a[0]]=a[1]),a[2]==="n"&&(r[a[0]]=Number(r[a[0]]))}}}function C0(e){Vh([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function Hh(e,t){return t.bookType=="ods"?Xi(e,t):t.bookType=="numbers"?Wh(e,t):t.bookType=="xlsb"?Gh(e,t):Xh(e,t)}function Gh(e,t){Ft=1024,e&&!e.SSF&&(e.SSF=Ar(Xe)),e&&e.SSF&&(Pn(),kn(e.SSF),t.revssf=Ln(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,zt?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r=t.bookType=="xlsb"?"bin":"xml",n=Ci.indexOf(t.bookType)>-1,a=ri();C0(t=t||{});var i=u0(),s="",f=0;if(t.cellXfs=[],it(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",Ee(i,s,ii(e.Props,t)),a.coreprops.push(s),Ne(t.rels,2,s,Ce.CORE_PROPS),s="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var o=[],l=0;l<e.SheetNames.length;++l)(e.Workbook.Sheets[l]||{}).Hidden!=2&&o.push(e.SheetNames[l]);e.Props.SheetNames=o}for(e.Props.Worksheets=e.Props.SheetNames.length,Ee(i,s,fi(e.Props)),a.extprops.push(s),Ne(t.rels,3,s,Ce.EXT_PROPS),e.Custprops!==e.Props&&lr(e.Custprops||{}).length>0&&(s="docProps/custom.xml",Ee(i,s,li(e.Custprops)),a.custprops.push(s),Ne(t.rels,4,s,Ce.CUST_PROPS)),f=1;f<=e.SheetNames.length;++f){var u={"!id":{}},x=e.Sheets[e.SheetNames[f-1]],d=(x||{})["!type"]||"sheet";switch(d){case"chart":default:s="xl/worksheets/sheet"+f+"."+r,Ee(i,s,$1(f-1,s,t,e,u)),a.sheets.push(s),Ne(t.wbrels,-1,"worksheets/sheet"+f+"."+r,Ce.WS[0])}if(x){var p=x["!comments"],g=!1,h="";p&&p.length>0&&(h="xl/comments"+f+"."+r,Ee(i,h,j1(p,h)),a.comments.push(h),Ne(u,-1,"../comments"+f+"."+r,Ce.CMNT),g=!0),x["!legacy"]&&g&&Ee(i,"xl/drawings/vmlDrawing"+f+".vml",Si(f,x["!comments"])),delete x["!comments"],delete x["!legacy"]}u["!id"].rId1&&Ee(i,ni(s),yt(u))}return t.Strings!=null&&t.Strings.length>0&&(s="xl/sharedStrings."+r,Ee(i,s,Y1(t.Strings,s,t)),a.strs.push(s),Ne(t.wbrels,-1,"sharedStrings."+r,Ce.SST)),s="xl/workbook."+r,Ee(i,s,z1(e,s)),a.workbooks.push(s),Ne(t.rels,1,s,Ce.WB),s="xl/theme/theme1.xml",Ee(i,s,wi(e.Themes,t)),a.themes.push(s),Ne(t.wbrels,-1,"theme/theme1.xml",Ce.THEME),s="xl/styles."+r,Ee(i,s,K1(e,s,t)),a.styles.push(s),Ne(t.wbrels,-1,"styles."+r,Ce.STY),e.vbaraw&&n&&(s="xl/vbaProject.bin",Ee(i,s,e.vbaraw),a.vba.push(s),Ne(t.wbrels,-1,"vbaProject.bin",Ce.VBA)),s="xl/metadata."+r,Ee(i,s,q1(s)),a.metadata.push(s),Ne(t.wbrels,-1,"metadata."+r,Ce.XLMETA),Ee(i,"[Content_Types].xml",ti(a,t)),Ee(i,"_rels/.rels",yt(t.rels)),Ee(i,"xl/_rels/workbook."+r+".rels",yt(t.wbrels)),delete t.revssf,delete t.ssf,i}function Xh(e,t){Ft=1024,e&&!e.SSF&&(e.SSF=Ar(Xe)),e&&e.SSF&&(Pn(),kn(e.SSF),t.revssf=Ln(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,zt?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xml",n=Ci.indexOf(t.bookType)>-1,a=ri();C0(t=t||{});var i=u0(),s="",f=0;if(t.cellXfs=[],it(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",Ee(i,s,ii(e.Props,t)),a.coreprops.push(s),Ne(t.rels,2,s,Ce.CORE_PROPS),s="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var o=[],l=0;l<e.SheetNames.length;++l)(e.Workbook.Sheets[l]||{}).Hidden!=2&&o.push(e.SheetNames[l]);e.Props.SheetNames=o}e.Props.Worksheets=e.Props.SheetNames.length,Ee(i,s,fi(e.Props)),a.extprops.push(s),Ne(t.rels,3,s,Ce.EXT_PROPS),e.Custprops!==e.Props&&lr(e.Custprops||{}).length>0&&(s="docProps/custom.xml",Ee(i,s,li(e.Custprops)),a.custprops.push(s),Ne(t.rels,4,s,Ce.CUST_PROPS));var u=["SheetJ5"];for(t.tcid=0,f=1;f<=e.SheetNames.length;++f){var x={"!id":{}},d=e.Sheets[e.SheetNames[f-1]],p=(d||{})["!type"]||"sheet";switch(p){case"chart":default:s="xl/worksheets/sheet"+f+"."+r,Ee(i,s,Li(f-1,t,e,x)),a.sheets.push(s),Ne(t.wbrels,-1,"worksheets/sheet"+f+"."+r,Ce.WS[0])}if(d){var g=d["!comments"],h=!1,_="";if(g&&g.length>0){var N=!1;g.forEach(function(O){O[1].forEach(function(F){F.T==!0&&(N=!0)})}),N&&(_="xl/threadedComments/threadedComment"+f+"."+r,Ee(i,_,To(g,u,t)),a.threadedcomments.push(_),Ne(x,-1,"../threadedComments/threadedComment"+f+"."+r,Ce.TCMNT)),_="xl/comments"+f+"."+r,Ee(i,_,Fi(g)),a.comments.push(_),Ne(x,-1,"../comments"+f+"."+r,Ce.CMNT),h=!0}d["!legacy"]&&h&&Ee(i,"xl/drawings/vmlDrawing"+f+".vml",Si(f,d["!comments"])),delete d["!comments"],delete d["!legacy"]}x["!id"].rId1&&Ee(i,ni(s),yt(x))}return t.Strings!=null&&t.Strings.length>0&&(s="xl/sharedStrings."+r,Ee(i,s,mi(t.Strings,t)),a.strs.push(s),Ne(t.wbrels,-1,"sharedStrings."+r,Ce.SST)),s="xl/workbook."+r,Ee(i,s,bi(e)),a.workbooks.push(s),Ne(t.rels,1,s,Ce.WB),s="xl/theme/theme1.xml",Ee(i,s,wi(e.Themes,t)),a.themes.push(s),Ne(t.wbrels,-1,"theme/theme1.xml",Ce.THEME),s="xl/styles."+r,Ee(i,s,Ei(e,t)),a.styles.push(s),Ne(t.wbrels,-1,"styles."+r,Ce.STY),e.vbaraw&&n&&(s="xl/vbaProject.bin",Ee(i,s,e.vbaraw),a.vba.push(s),Ne(t.wbrels,-1,"vbaProject.bin",Ce.VBA)),s="xl/metadata."+r,Ee(i,s,Ai()),a.metadata.push(s),Ne(t.wbrels,-1,"metadata."+r,Ce.XLMETA),u.length>1&&(s="xl/persons/person.xml",Ee(i,s,wo(u)),a.people.push(s),Ne(t.wbrels,-1,"persons/person.xml",Ce.PEOPLE)),Ee(i,"[Content_Types].xml",ti(a,t)),Ee(i,"_rels/.rels",yt(t.rels)),Ee(i,"xl/_rels/workbook."+r+".rels",yt(t.wbrels)),delete t.revssf,delete t.ssf,i}function zh(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=Kr(e.slice(0,12));break;case"binary":r=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function zi(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return Qt(t.file,ke.write(e,{type:ye?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return ke.write(e,t)}function $h(e,t){var r=Ar(t||{}),n=Hh(e,r);return Kh(n,r)}function Kh(e,t){var r={},n=ye?"nodebuffer":typeof Uint8Array!="undefined"?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=n;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=n;break;default:throw new Error("Unrecognized type "+t.type)}var a=e.FullPaths?ke.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if(typeof Deno!="undefined"&&typeof a=="string"){if(t.type=="binary"||t.type=="base64")return a;a=new Uint8Array(In(a))}return t.password&&typeof encrypt_agile!="undefined"?zi(encrypt_agile(a,t.password),t):t.type==="file"?Qt(t.file,a):t.type=="string"?Vt(a):a}function Yh(e,t){var r=t||{},n=uh(e,r);return zi(n,r)}function Wr(e,t,r){r||(r="");var n=r+e;switch(t.type){case"base64":return Kt(Gr(n));case"binary":return Gr(n);case"string":return e;case"file":return Qt(t.file,n,"utf8");case"buffer":return ye?jr(n,"utf8"):typeof TextEncoder!="undefined"?new TextEncoder().encode(n):Wr(n,{type:"binary"}).split("").map(function(a){return a.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function jh(e,t){switch(t.type){case"base64":return Kt(e);case"binary":return e;case"string":return e;case"file":return Qt(t.file,e,"binary");case"buffer":return ye?jr(e,"binary"):e.split("").map(function(r){return r.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function vn(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",n=0;n<e.length;++n)r+=String.fromCharCode(e[n]);return t.type=="base64"?Kt(r):t.type=="string"?Vt(r):r;case"file":return Qt(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function $i(e,t){Es(),P1(e);var r=Ar(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),r.type=="array"){r.type="binary";var n=$i(e,r);return r.type="array",In(n)}var a=0;if(r.sheet&&(typeof r.sheet=="number"?a=r.sheet:a=e.SheetNames.indexOf(r.sheet),!e.SheetNames[a]))throw new Error("Sheet not found: "+r.sheet+" : "+typeof r.sheet);switch(r.bookType||"xlsb"){case"xml":case"xlml":return Wr(lh(e,r),r);case"slk":case"sylk":return Wr(Nl.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"htm":case"html":return Wr(Vi(e.Sheets[e.SheetNames[a]],r),r);case"txt":return jh(Ki(e.Sheets[e.SheetNames[a]],r),r);case"csv":return Wr(y0(e.Sheets[e.SheetNames[a]],r),r,"\uFEFF");case"dif":return Wr(Rl.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"dbf":return vn(Dl.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"prn":return Wr(Il.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"rtf":return Wr(Ul.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"eth":return Wr(pi.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"fods":return Wr(Xi(e,r),r);case"wk1":return vn(fa.sheet_to_wk1(e.Sheets[e.SheetNames[a]],r),r);case"wk3":return vn(fa.book_to_wk3(e,r),r);case"biff2":r.biff||(r.biff=2);case"biff3":r.biff||(r.biff=3);case"biff4":return r.biff||(r.biff=4),vn(Wi(e,r),r);case"biff5":r.biff||(r.biff=5);case"biff8":case"xla":case"xls":return r.biff||(r.biff=8),Yh(e,r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return $h(e,r);default:throw new Error("Unrecognized bookType |"+r.bookType+"|")}}function qh(e){if(!e.bookType){var t={xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"},r=e.file.slice(e.file.lastIndexOf(".")).toLowerCase();r.match(/^\.[a-z]+$/)&&(e.bookType=r.slice(1)),e.bookType=t[e.bookType]||e.bookType}}function Jh(e,t,r){var n=r||{};return n.type="file",n.file=t,qh(n),$i(e,n)}function Zh(e,t,r,n,a,i,s,f){var o=fr(r),l=f.defval,u=f.raw||!Object.prototype.hasOwnProperty.call(f,"raw"),x=!0,d=a===1?[]:{};if(a!==1)if(Object.defineProperty)try{Object.defineProperty(d,"__rowNum__",{value:r,enumerable:!1})}catch{d.__rowNum__=r}else d.__rowNum__=r;if(!s||e[r])for(var p=t.s.c;p<=t.e.c;++p){var g=s?e[r][p]:e[n[p]+o];if(g===void 0||g.t===void 0){if(l===void 0)continue;i[p]!=null&&(d[i[p]]=l);continue}var h=g.v;switch(g.t){case"z":if(h==null)break;continue;case"e":h=h==0?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+g.t)}if(i[p]!=null){if(h==null)if(g.t=="e"&&h===null)d[i[p]]=null;else if(l!==void 0)d[i[p]]=l;else if(u&&h===null)d[i[p]]=null;else continue;else d[i[p]]=u&&(g.t!=="n"||g.t==="n"&&f.rawNumbers!==!1)?h:Yr(g,h,f);h!=null&&(x=!1)}}return{row:d,isempty:x}}function Rn(e,t){if(e==null||e["!ref"]==null)return[];var r={t:"n",v:0},n=0,a=1,i=[],s=0,f="",o={s:{r:0,c:0},e:{r:0,c:0}},l=t||{},u=l.range!=null?l.range:e["!ref"];switch(l.header===1?n=1:l.header==="A"?n=2:Array.isArray(l.header)?n=3:l.header==null&&(n=0),typeof u){case"string":o=Ue(u);break;case"number":o=Ue(e["!ref"]),o.s.r=u;break;default:o=u}n>0&&(a=0);var x=fr(o.s.r),d=[],p=[],g=0,h=0,_=Array.isArray(e),N=o.s.r,O=0,F={};_&&!e[N]&&(e[N]=[]);var R=l.skipHidden&&e["!cols"]||[],q=l.skipHidden&&e["!rows"]||[];for(O=o.s.c;O<=o.e.c;++O)if(!(R[O]||{}).hidden)switch(d[O]=hr(O),r=_?e[N][O]:e[d[O]+x],n){case 1:i[O]=O-o.s.c;break;case 2:i[O]=d[O];break;case 3:i[O]=l.header[O-o.s.c];break;default:if(r==null&&(r={w:"__EMPTY",t:"s"}),f=s=Yr(r,null,l),h=F[s]||0,!h)F[s]=1;else{do f=s+"_"+h++;while(F[f]);F[s]=h,F[f]=1}i[O]=f}for(N=o.s.r+a;N<=o.e.r;++N)if(!(q[N]||{}).hidden){var ne=Zh(e,o,N,d,n,i,_,l);(ne.isempty===!1||(n===1?l.blankrows!==!1:!!l.blankrows))&&(p[g++]=ne.row)}return p.length=g,p}var pa=/"/g;function Qh(e,t,r,n,a,i,s,f){for(var o=!0,l=[],u="",x=fr(r),d=t.s.c;d<=t.e.c;++d)if(!!n[d]){var p=f.dense?(e[r]||[])[d]:e[n[d]+x];if(p==null)u="";else if(p.v!=null){o=!1,u=""+(f.rawNumbers&&p.t=="n"?p.v:Yr(p,null,f));for(var g=0,h=0;g!==u.length;++g)if((h=u.charCodeAt(g))===a||h===i||h===34||f.forceQuotes){u='"'+u.replace(pa,'""')+'"';break}u=="ID"&&(u='"ID"')}else p.f!=null&&!p.F?(o=!1,u="="+p.f,u.indexOf(",")>=0&&(u='"'+u.replace(pa,'""')+'"')):u="";l.push(u)}return f.blankrows===!1&&o?null:l.join(s)}function y0(e,t){var r=[],n=t==null?{}:t;if(e==null||e["!ref"]==null)return"";var a=Ue(e["!ref"]),i=n.FS!==void 0?n.FS:",",s=i.charCodeAt(0),f=n.RS!==void 0?n.RS:`
`,o=f.charCodeAt(0),l=new RegExp((i=="|"?"\\|":i)+"+$"),u="",x=[];n.dense=Array.isArray(e);for(var d=n.skipHidden&&e["!cols"]||[],p=n.skipHidden&&e["!rows"]||[],g=a.s.c;g<=a.e.c;++g)(d[g]||{}).hidden||(x[g]=hr(g));for(var h=0,_=a.s.r;_<=a.e.r;++_)(p[_]||{}).hidden||(u=Qh(e,a,_,x,s,o,i,n),u!=null&&(n.strip&&(u=u.replace(l,"")),(u||n.blankrows!==!1)&&r.push((h++?f:"")+u)));return delete n.dense,r.join("")}function Ki(e,t){t||(t={}),t.FS="	",t.RS=`
`;var r=y0(e,t);return r}function ex(e){var t="",r,n="";if(e==null||e["!ref"]==null)return[];var a=Ue(e["!ref"]),i="",s=[],f,o=[],l=Array.isArray(e);for(f=a.s.c;f<=a.e.c;++f)s[f]=hr(f);for(var u=a.s.r;u<=a.e.r;++u)for(i=fr(u),f=a.s.c;f<=a.e.c;++f)if(t=s[f]+i,r=l?(e[u]||[])[f]:e[t],n="",r!==void 0){if(r.F!=null){if(t=r.F,!r.f)continue;n=r.f,t.indexOf(":")==-1&&(t=t+":"+t)}if(r.f!=null)n=r.f;else{if(r.t=="z")continue;if(r.t=="n"&&r.v!=null)n=""+r.v;else if(r.t=="b")n=r.v?"TRUE":"FALSE";else if(r.w!==void 0)n="'"+r.w;else{if(r.v===void 0)continue;r.t=="s"?n="'"+r.v:n=""+r.v}}o[o.length]=t+"="+n}return o}function Yi(e,t,r){var n=r||{},a=+!n.skipHeader,i=e||{},s=0,f=0;if(i&&n.origin!=null)if(typeof n.origin=="number")s=n.origin;else{var o=typeof n.origin=="string"?rr(n.origin):n.origin;s=o.r,f=o.c}var l,u={s:{c:0,r:0},e:{c:f,r:s+t.length-1+a}};if(i["!ref"]){var x=Ue(i["!ref"]);u.e.c=Math.max(u.e.c,x.e.c),u.e.r=Math.max(u.e.r,x.e.r),s==-1&&(s=x.e.r+1,u.e.r=s+t.length-1+a)}else s==-1&&(s=0,u.e.r=t.length-1+a);var d=n.header||[],p=0;t.forEach(function(h,_){lr(h).forEach(function(N){(p=d.indexOf(N))==-1&&(d[p=d.length]=N);var O=h[N],F="z",R="",q=Ie({c:f+p,r:s+_+a});l=Jt(i,q),O&&typeof O=="object"&&!(O instanceof Date)?i[q]=O:(typeof O=="number"?F="n":typeof O=="boolean"?F="b":typeof O=="string"?F="s":O instanceof Date?(F="d",n.cellDates||(F="n",O=wr(O)),R=n.dateNF||Xe[14]):O===null&&n.nullError&&(F="e",O=0),l?(l.t=F,l.v=O,delete l.w,delete l.R,R&&(l.z=R)):i[q]=l={t:F,v:O},R&&(l.z=R))})}),u.e.c=Math.max(u.e.c,f+d.length-1);var g=fr(s);if(a)for(p=0;p<d.length;++p)i[hr(p+f)+g]={t:"s",v:d[p]};return i["!ref"]=qe(u),i}function rx(e,t){return Yi(null,e,t)}function Jt(e,t,r){if(typeof t=="string"){if(Array.isArray(e)){var n=rr(t);return e[n.r]||(e[n.r]=[]),e[n.r][n.c]||(e[n.r][n.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return typeof t!="number"?Jt(e,Ie(t)):Jt(e,Ie({r:t,c:r||0}))}function tx(e,t){if(typeof t=="number"){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}else if(typeof t=="string"){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}else throw new Error("Cannot find sheet |"+t+"|")}function nx(){return{SheetNames:[],Sheets:{}}}function ax(e,t,r,n){var a=1;if(!r)for(;a<=65535&&e.SheetNames.indexOf(r="Sheet"+a)!=-1;++a,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(n&&e.SheetNames.indexOf(r)>=0){var i=r.match(/(^.*?)(\d+)$/);a=i&&+i[2]||0;var s=i&&i[1]||r;for(++a;a<=65535&&e.SheetNames.indexOf(r=s+a)!=-1;++a);}if(Mi(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function ix(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var n=tx(e,t);switch(e.Workbook.Sheets[n]||(e.Workbook.Sheets[n]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[n].Hidden=r}function sx(e,t){return e.z=t,e}function ji(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}function fx(e,t,r){return ji(e,"#"+t,r)}function lx(e,t,r){e.c||(e.c=[]),e.c.push({t,a:r||"SheetJS"})}function ox(e,t,r,n){for(var a=typeof t!="string"?t:Ue(t),i=typeof t=="string"?t:qe(t),s=a.s.r;s<=a.e.r;++s)for(var f=a.s.c;f<=a.e.c;++f){var o=Jt(e,s,f);o.t="n",o.F=i,delete o.v,s==a.s.r&&f==a.s.c&&(o.f=r,n&&(o.D=!0))}return e}var cr={encode_col:hr,encode_row:fr,encode_cell:Ie,encode_range:qe,decode_col:m0,decode_row:p0,split_cell:wf,decode_cell:rr,decode_range:Nr,format_cell:Yr,sheet_add_aoa:ja,sheet_add_json:Yi,sheet_add_dom:Hi,aoa_to_sheet:It,json_to_sheet:rx,table_to_sheet:Gi,table_to_book:Ih,sheet_to_csv:y0,sheet_to_txt:Ki,sheet_to_json:Rn,sheet_to_html:Vi,sheet_to_formulae:ex,sheet_to_row_object_array:Rn,sheet_get_cell:Jt,book_new:nx,book_append_sheet:ax,book_set_sheet_visibility:ix,cell_set_number_format:sx,cell_set_hyperlink:ji,cell_set_internal_link:fx,cell_add_comment:lx,sheet_set_array_formula:ox,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};const ux={class:"row q-mb-lg"},cx={class:"col-12 col-sm-6"},hx={class:"q-pa-sm"},xx={class:"row q-mb-lg"},dx={class:"col-12 col-sm-6"},px={class:"q-pa-sm"},mx={class:"col-12 col-sm-6"},vx={class:"q-pa-sm"},gx={key:0,class:"row q-mb-lg"},_x={class:"col-12"},Ex={class:"row q-mb-md"},Tx={class:"col-12 col-sm-4 q-pa-sm"},wx={class:"col-12 col-sm-4 q-pa-sm"},Ax={class:"col-12 col-sm-4 q-pa-sm"},Sx={class:"row q-mb-md"},Fx={class:"col-12 col-sm-4"},Cx={class:"q-pa-sm"},yx={class:"col-12 col-sm-4"},Ox={class:"q-pa-sm"},Dx={class:"col-12 col-sm-4"},Nx={class:"q-pa-sm"},Rx={class:"row q-col-gutter-md"},Ix={class:"col-12 col-sm-4"},kx={class:"q-pa-sm"},Px={class:"col-12 col-sm-4"},Lx={class:"q-pa-sm"},Bx={class:"row q-mb-md"},Mx={class:"col-12 col-sm-4 q-pa-sm"},bx={class:"col-12 col-sm-4 q-pa-sm"},Ux={class:"col-12 col-sm-4 q-pa-sm"},Wx={class:"row q-mb-md"},Vx={class:"col-12 col-sm-4"},Hx={class:"q-pa-sm"},Gx={class:"col-12 col-sm-4"},Xx={class:"q-pa-sm"},zx={class:"col-12 col-sm-4"},$x={class:"q-pa-sm"},Kx={class:"row q-mb-md"},Yx={class:"col-12 col-sm-4 q-pa-sm"},jx={class:"col-12 col-sm-4 q-pa-sm"},qx={class:"col-12 col-sm-4 q-pa-sm"},Jx={class:"row q-mb-md"},Zx={class:"col-12 col-sm-4 q-pa-sm"},Qx={class:"col-12 col-sm-4 q-pa-sm"},e2={class:"col-12 col-sm-4 q-pa-sm"},r2={class:"row q-mb-md"},t2={class:"col-12 col-sm-4"},n2={class:"q-pa-sm"},a2={class:"col-12 col-sm-4"},i2={class:"q-pa-sm"},s2={class:"col-12 col-sm-4"},f2={class:"q-pa-sm"},l2={class:"text-center q-mb-sm"},o2={class:"row q-mb-md"},u2={class:"col-12 col-sm-6"},c2={class:"text-subtitle1"},h2={class:"text-subtitle1"},x2={class:"col-12 col-sm-6 text-right"},d2=ls({__name:"BatchAnalysisPage",setup(e){const t=ps(),r=[{label:"\u7248\u8DEF\u5206\u6790",value:"ball-follow"}],n=[{label:"\u5A01\u529B\u5F69",value:"super_lotto638"},{label:"\u5927\u6A02\u900F",value:"lotto649"},{label:"\u4ECA\u5F69539",value:"daily539"},{label:"\u516D\u5408\u5F69",value:"lotto_hk"}],a=Ye("ball-follow"),i=Ye("super_lotto638"),s=Ye(""),f=Ye("excel"),o=Ye(!1),l=Ye(0),u=Ye(""),x=Ye([]),d=Ye(1),p=Ye([{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 1 \u6B21",value:1},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 2 \u6B21",value:2},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 3 \u6B21",value:3},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 4 \u6B21",value:4},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 5 \u6B21",value:5}]),g=Ye("above"),h=Ye([{label:"(\u542B)\u4EE5\u4E0A",value:"above"},{label:"(\u542B)\u4EE5\u4E0B",value:"below"},{label:"\u525B\u597D",value:"exact"}]),_=O0(()=>new Date().toISOString().split("T")[0]),N=O0(()=>a.value&&i.value&&s.value&&!o.value),O=Ye({num1:1,num2:1,num3:1,periodNum:50,maxRange:20,aheadNum:1}),F=Ye({num1:1,num2:1,num3:1,periodNum:50,maxRange:20,aheadNum:1}),R=Ye({comb1:1,comb2:1,comb3:1,tailComb1:1,tailComb2:1,tailComb3:1,period:50,maxRange:20,ahead:1}),q=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3}],ne=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3},{label:"\u56DB\u661F\u7D44\u5408",value:4},{label:"\u4E94\u661F\u7D44\u5408",value:5}],D=Ye(Array.from({length:491},(Y,A)=>({label:`${A+10}\u671F`,value:A+10}))),W=Ye(Array.from({length:21},(Y,A)=>({label:`${A+10}\u671F`,value:A+10}))),M=Ye(Array.from({length:15},(Y,A)=>({label:`\u4E0B${A+1}\u671F`,value:A+1}))),X=Ye(Array.from({length:991},(Y,A)=>({label:`${A+10}\u671F`,value:A+10}))),z=Y=>({"ball-follow":"\u7248\u8DEF\u5206\u6790",tail:"\u5C3E\u6578\u5206\u6790",pattern:"\u7D9C\u5408\u5206\u6790"})[Y]||Y,K=Y=>({super_lotto638:"\u5A01\u529B\u5F69",lotto649:"\u5927\u6A02\u900F",daily539:"\u4ECA\u5F69539",lotto_hk:"\u516D\u5408\u5F69"})[Y]||Y,ie=()=>{},Ae=()=>{switch(a.value){case"ball-follow":return{comb1:O.value.num1,comb2:O.value.num2,comb3:O.value.num3,periodNum:O.value.periodNum,maxRange:O.value.maxRange,aheadNum:O.value.aheadNum};case"tail":return{tailComb1:F.value.num1,tailComb2:F.value.num2,tailComb3:F.value.num3,periodNum:F.value.periodNum,maxRange:F.value.maxRange,aheadNum:F.value.aheadNum};case"pattern":return{comb1:R.value.comb1,comb2:R.value.comb2,comb3:R.value.comb3,tailComb1:R.value.tailComb1,tailComb2:R.value.tailComb2,tailComb3:R.value.tailComb3,period:R.value.period,maxRange:R.value.maxRange,ahead:R.value.ahead};default:return{comb1:1,comb2:1,comb3:1,tailComb1:1,tailComb2:1,tailComb3:1,periodNum:50,maxRange:20,aheadNum:1}}},me=Ye(!1),We=(Y,A=49,V=1,oe="above")=>{const ge=Y.filter(pe=>{switch(oe){case"above":return pe.consecutiveHits>=V;case"below":return pe.consecutiveHits<=V;case"exact":return pe.consecutiveHits===V;default:return pe.consecutiveHits>=V}}),Te=new Map,Q=new Map,de=new Map;ge.forEach(pe=>{pe.targetNumbers.forEach(xe=>{Te.set(xe,(Te.get(xe)||0)+1);const Me=xe%10;Q.set(Me,(Q.get(Me)||0)+1)})});const Le=[];for(let pe=1;pe<=A;pe++)if(!Te.has(pe)){Le.push(pe);const xe=Y.filter(Me=>Me.targetNumbers.includes(pe)).length;de.set(pe,xe)}return{predictNumbers:Array.from(Te.entries()).sort((pe,xe)=>xe[1]-pe[1]).slice(0,10).map(([pe])=>pe),targetNumAppearances:Te,nonAppearedNumbers:Le,nonAppearedByFrequency:de,tailNumAppearances:Q}},Pe=(Y,A)=>Y.filter(V=>A.includes(V)),Er=Y=>{switch(Y){case"super_lotto638":return 38;case"lotto649":return 49;case"daily539":return 39;case"lotto_hk":return 49;default:return 49}},ze=Y=>We(Y).predictNumbers,dr=Y=>{const A=[];for(let V=0;V<Y.length;V+=10){const oe=Y.slice(V,V+10);A.push(oe.map(ge=>ge.toString().padStart(2,"0")).join(" "))}return A},pr=(Y,A,V=!1)=>{const oe=[];for(let ge=0;ge<Y.length;ge+=10){const Q=Y.slice(ge,ge+10).map(de=>{const Le=V?de.toString():de.toString().padStart(2,"0");return A.includes(de)?`*${Le}*`:Le});oe.push(Q.join(" "))}return oe},S=(Y,A=!1)=>{const V=[];for(let oe=0;oe<Y.length;oe+=10){const Te=Y.slice(oe,oe+10).map(Q=>A?Q.toString():Q.toString().padStart(2,"0"));V.push(Te)}return V},B=(Y,A)=>{const V=[{wch:10},{wch:12},{wch:30},{wch:30},{wch:30},{wch:10}];Y["!cols"]=V;const oe=Array(A).fill({hpt:20});Y["!rows"]=oe},y=(Y,A,V)=>{console.log("\u958B\u59CB\u61C9\u7528\u7D05\u8272\u5B57\u9AD4\u6A19\u8A18\uFF0C\u985E\u578B:",V),console.log("\u6279\u91CF\u7D50\u679C\u6578\u91CF:",x.value.length);for(let oe=3;oe<A.length;oe++){const ge=A[oe],Te=ge[0];if(!Te||Te==="")continue;const Q=x.value.find(de=>de.date===Te);if(!Q||!Q.actualNumbers){console.log("\u627E\u4E0D\u5230\u5C0D\u61C9\u7D50\u679C\uFF0C\u65E5\u671F:",Te);continue}console.log("\u8655\u7406\u65E5\u671F:",Te,"\u5BE6\u969B\u865F\u78BC:",Q.actualNumbers);for(let de=1;de<ge.length;de++){const Le=ge[de];if(Le&&Le!==""){let ve=!1;if(V==="predict"){const pe=parseInt(Le.toString().replace(/^0+/,""),10);ve=Q.actualNumbers.includes(pe),console.log("\u6AA2\u67E5\u9810\u6E2C\u865F\u78BC:",pe,"\u662F\u5426\u5339\u914D:",ve)}else{const pe=parseInt(Le.toString(),10),xe=Q.actualNumbers.map(Me=>Me%10);ve=xe.includes(pe),console.log("\u6AA2\u67E5\u5C3E\u6578:",pe,"\u5BE6\u969B\u5C3E\u6578:",xe,"\u662F\u5426\u5339\u914D:",ve)}if(ve){const pe=cr.encode_cell({r:oe,c:de});console.log("\u6A19\u8A18\u547D\u4E2D\u865F\u78BC\uFF0C\u5132\u5B58\u683C:",pe,"\u503C:",Le);const xe=`${Le}\u2605`;Y[pe]?Y[pe].v=xe:Y[pe]={v:xe,t:"s"},A[oe][de]=xe}}}}console.log("\u7D05\u8272\u5B57\u9AD4\u6A19\u8A18\u61C9\u7528\u5B8C\u6210")},C=async()=>{if(!N.value){ot.create({type:"warning",message:"\u8ACB\u5B8C\u6574\u586B\u5BEB\u6240\u6709\u5FC5\u8981\u53C3\u6578"});return}try{o.value=!0,me.value=i.value==="super_lotto638",l.value=0,u.value="\u6E96\u5099\u958B\u59CB...",x.value=[];const Y=Ae(),A="periodNum"in Y?Y.periodNum:"period"in Y?Y.period:50,oe=(await Yn.getLottoList({draw_type:i.value,date_end:s.value,limit:A})).data.reverse();u.value="\u6B63\u5728\u9032\u884C\u5206\u6790...";let ge=0;for(const Te of oe){l.value=ge/oe.length,u.value=`\u5206\u6790\u4E2D... ${++ge}/${oe.length}`;const Q=await Yn.getLottoList({draw_type:i.value,date_end:Te.draw_date,limit:A}),de=Ae(),Le="aheadNum"in de?de.aheadNum:"ahead"in de?de.ahead:1,ve=await Yn.getLottoPredict({draw_type:i.value,draw_date:Te.draw_date,ahead_count:Le});let pe=[];ve.data&&(pe=[...ve.data.draw_number_size||[]],!me.value&&ve.data.special_number&&pe.push(ve.data.special_number));let xe={date:Te.draw_date,period:Te.period,analysisType:a.value,actualNumbers:pe,predictResponse:ve.data};switch(a.value){case"ball-follow":const Me=await ce(Q.data);xe.ballFollowResults=Me.data,xe.ballFollowOccurrences=Me.occurrences;const Fr=Er(i.value),mr=We(Me.data,Fr,d.value,g.value);xe.predictNumbers=mr.predictNumbers,xe.targetNumAppearances=mr.targetNumAppearances,xe.nonAppearedNumbers=mr.nonAppearedNumbers,xe.nonAppearedByFrequency=mr.nonAppearedByFrequency,xe.tailNumAppearances=mr.tailNumAppearances,xe.matches=Pe(xe.predictNumbers||[],xe.actualNumbers||[]);break;case"tail":const $e=await he(Q.data);xe.tailResults=$e.data,xe.tailOccurrences=$e.occurrences;const or=We($e.data,10,d.value,g.value);xe.predictNumbers=or.predictNumbers,xe.targetNumAppearances=or.targetNumAppearances,xe.nonAppearedNumbers=or.nonAppearedNumbers,xe.nonAppearedByFrequency=or.nonAppearedByFrequency,xe.tailNumAppearances=or.tailNumAppearances,xe.matches=Pe(xe.predictNumbers||[],xe.actualNumbers||[]);break;case"pattern":const Ke=await ce(Q.data),st=await he(Q.data);xe.ballFollowResults=Ke.data,xe.ballFollowOccurrences=Ke.occurrences,xe.tailResults=st.data,xe.tailOccurrences=st.occurrences;const Wn=Er(i.value),qr=We(Ke.data,Wn,d.value,g.value),an=We(st.data,10,d.value,g.value),sn=qr.predictNumbers,Vn=an.predictNumbers;xe.predictNumbers=[...new Set([...sn,...Vn])],xe.targetNumAppearances=qr.targetNumAppearances,xe.nonAppearedNumbers=qr.nonAppearedNumbers,xe.nonAppearedByFrequency=qr.nonAppearedByFrequency,xe.tailNumAppearances=qr.tailNumAppearances,xe.matches=Pe(xe.predictNumbers||[],xe.actualNumbers||[]);break}x.value.push(xe)}u.value="\u5206\u6790\u5B8C\u6210\uFF01",l.value=1,ot.create({type:"positive",position:"top",message:"\u5206\u6790\u5B8C\u6210\uFF01"})}catch(Y){ms(Y)}finally{o.value=!1}},G=()=>{o.value=!1,u.value="\u5206\u6790\u5DF2\u4E2D\u65B7",ot.create({type:"warning",message:"\u5206\u6790\u5DF2\u4E2D\u65B7"})},ce=async Y=>{const A=Ae(),V=Y.map(oe=>{const ge=[...oe.draw_number_size];return!me.value&&oe.special_number&&ge.push(oe.special_number),{numbers:[...ge],period:String(oe.period)}});return t.init({firstGroupSize:A.comb1||1,secondGroupSize:A.comb2||1,targetGroupSize:A.comb3||1,maxRange:A.maxRange||20,lookAheadCount:A.aheadNum||1},V),await t.analyzeWithProgress()},he=async Y=>{const A=Ae(),V=Y.map(oe=>{const ge=new Set;for(let Q of oe.draw_number_size)ge.add(Q%10);!me.value&&oe.special_number&&ge.add(oe.special_number%10);const Te=Array.from(ge).sort((Q,de)=>Q===0?1:de===0?-1:Q-de);return{period:String(oe.period),numbers:[...Te]}});return t.init({firstGroupSize:A.tailComb1||1,secondGroupSize:A.tailComb2||1,targetGroupSize:A.tailComb3||1,maxRange:A.maxRange||20,lookAheadCount:A.aheadNum||1},V),await t.analyzeWithProgress()},ue=()=>{try{ae()}catch(Y){console.error("\u4E0B\u8F09\u5931\u6557:",Y),ot.create({type:"negative",message:"\u6A94\u6848\u4E0B\u8F09\u5931\u6557"})}},ae=()=>{if(x.value.length===0){ot.create({type:"warning",message:"\u6C92\u6709\u53EF\u4E0B\u8F09\u7684\u7D50\u679C"});return}try{const Y=cr.book_new();switch(a.value){case"ball-follow":Oe(Y),we(Y),Qe(Y),Fe(Y),Sr(Y);break;case"tail":Be(Y),Sr(Y);break;case"pattern":le(Y);break}const A=z(a.value),V=K(i.value),oe=new Date().toISOString().split("T")[0].replace(/-/g,""),ge=`${A}_${V}_${oe}.xlsx`;Jh(Y,ge),ot.create({type:"positive",message:"\u5831\u8868\u4E0B\u8F09\u6210\u529F\uFF01"})}catch(Y){console.error("Excel\u751F\u6210\u5931\u6557:",Y),ot.create({type:"negative",message:"Excel\u6A94\u6848\u751F\u6210\u5931\u6557"})}},Oe=Y=>{const A=[];A.push(["\u9810\u6E2C\u865F\u78BC\u7D71\u8A08"]),A.push([]),A.push(["\u65E5\u671F"]),x.value.forEach(oe=>{if(oe.targetNumAppearances){const ge=Array.from(oe.targetNumAppearances.entries()).sort((Q,de)=>de[1]-Q[1]).map(([Q])=>Q);S(ge).forEach((Q,de)=>{de===0?A.push([oe.date,...Q]):A.push(["",...Q])})}});const V=cr.aoa_to_sheet(A);y(V,A,"predict"),cr.book_append_sheet(Y,V,"\u9810\u6E2C\u865F\u78BC")},we=Y=>{const A=[];A.push(["\u672A\u51FA\u73FE\u865F\u78BC - \u4F9D\u9810\u6E2C\u6B21\u6578\u6392\u5E8F"]),A.push([]),A.push(["\u65E5\u671F"]),x.value.forEach(oe=>{if(oe.nonAppearedByFrequency){const ge=Array.from(oe.nonAppearedByFrequency.entries()).sort((Q,de)=>de[1]-Q[1]).map(([Q])=>Q);S(ge).forEach((Q,de)=>{de===0?A.push([oe.date,...Q]):A.push(["",...Q])})}});const V=cr.aoa_to_sheet(A);y(V,A,"predict"),cr.book_append_sheet(Y,V,"\u672A\u51FA\u73FE\u865F\u78BC-\u9810\u6E2C\u6B21\u6578")},Qe=Y=>{const A=[];A.push(["\u672A\u51FA\u73FE\u865F\u78BC - \u4F9D\u5927\u5C0F\u6392\u5E8F"]),A.push([]),A.push(["\u65E5\u671F"]),x.value.forEach(oe=>{if(oe.nonAppearedNumbers){const ge=[...oe.nonAppearedNumbers].sort((Q,de)=>Q-de);S(ge).forEach((Q,de)=>{de===0?A.push([oe.date,...Q]):A.push(["",...Q])})}});const V=cr.aoa_to_sheet(A);y(V,A,"predict"),cr.book_append_sheet(Y,V,"\u672A\u51FA\u73FE\u865F\u78BC-\u5927\u5C0F\u6392\u5E8F")},Fe=Y=>{const A=[];A.push(["\u9810\u6E2C\u5C3E\u6578\u7D71\u8A08"]),A.push([]),A.push(["\u65E5\u671F"]),x.value.forEach(oe=>{if(oe.tailNumAppearances){const ge=Array.from(oe.tailNumAppearances.entries()).sort((Q,de)=>de[1]-Q[1]).map(([Q])=>Q);S(ge,!0).forEach((Q,de)=>{de===0?A.push([oe.date,...Q]):A.push(["",...Q])})}});const V=cr.aoa_to_sheet(A);y(V,A,"tail"),cr.book_append_sheet(Y,V,"\u5C3E\u6578\u7D71\u8A08")},Sr=Y=>{const A=[],V=Ae(),oe="aheadNum"in V?V.aheadNum:"ahead"in V?V.ahead:1;A.push([`\u5BE6\u969B\u958B\u734E\u865F\u78BC (\u9810\u6E2C${oe}\u671F\u5F8C)`]),A.push([]);const ge=x.value.some(ve=>!ve.actualNumbers||ve.actualNumbers.length===0||i.value==="super_lotto638"||i.value==="daily539"?!1:ve.actualNumbers.length>6),Te=["\u5206\u6790\u65E5\u671F","\u9810\u6E2C\u65E5\u671F"];let Q=6;i.value==="super_lotto638"||i.value==="lotto649"?Q=6:i.value==="daily539"?Q=5:i.value==="lotto_hk"&&(Q=6);for(let ve=1;ve<=Q;ve++)Te.push(`\u865F\u78BC${ve}`);ge&&Te.push("\u7279\u5225\u865F"),A.push(Te),x.value.forEach(ve=>{let pe="",xe=!1;ve.predictResponse&&ve.predictResponse.draw_date?(pe=ve.predictResponse.draw_date,xe=!!ve.predictResponse.period):pe="\u5C1A\u672A\u958B\u734E";const Me=[ve.date,pe];let Fr=[],mr;ve.actualNumbers&&ve.actualNumbers.length>0&&xe&&(me.value?Fr=[...ve.actualNumbers].sort(($e,or)=>$e-or):ve.actualNumbers.length>Q?(Fr=ve.actualNumbers.slice(0,-1).sort(($e,or)=>$e-or),mr=ve.actualNumbers[ve.actualNumbers.length-1]):Fr=[...ve.actualNumbers].sort(($e,or)=>$e-or));for(let $e=0;$e<Q;$e++)$e<Fr.length?Me.push(Fr[$e].toString().padStart(2,"0")):!xe&&pe!=="\u5C1A\u672A\u958B\u734E"?Me.push("\u5C1A\u672A\u958B\u734E"):Me.push("");ge&&(mr!==void 0?Me.push(mr.toString().padStart(2,"0")):!xe&&pe!=="\u5C1A\u672A\u958B\u734E"?Me.push("\u5C1A\u672A\u958B\u734E"):Me.push("")),A.push(Me)});const de=cr.aoa_to_sheet(A),Le=[{wch:12},{wch:12}];for(let ve=0;ve<Q+(ge?1:0);ve++)Le.push({wch:8});de["!cols"]=Le,cr.book_append_sheet(Y,de,"\u5BE6\u969B\u958B\u734E\u865F\u78BC")},Be=Y=>{const A=[],V=z(a.value),oe=K(i.value);A.push([`${V} - ${oe}`]),A.push([]);const ge=["\u65E5\u671F","\u9810\u6E2C\u5C3E\u6578","\u5BE6\u969B\u5C3E\u6578","\u547D\u4E2D\u5C3E\u6578","\u547D\u4E2D\u6578\u91CF"];A.push(ge),x.value.forEach(Q=>{const de=(Q.predictNumbers||[]).slice(0,10),Le=Q.actualNumbers||[],ve=Q.matches||[],pe=de.map(Ke=>Ke%10),xe=[...new Set(Le.map(Ke=>Ke%10))],Me=ve.map(Ke=>Ke%10),Fr=pr(pe,Me,!0),mr=dr(xe),$e=dr(Me),or=Math.max(Fr.length,mr.length,$e.length,1);for(let Ke=0;Ke<or;Ke++){const st=[Ke===0?Q.date:"",Fr[Ke]||"",mr[Ke]||"",$e[Ke]||"",Ke===0?Me.length:""];A.push(st)}});const Te=cr.aoa_to_sheet(A);B(Te,A.length),cr.book_append_sheet(Y,Te,V)},le=Y=>{Oe(Y),we(Y),Qe(Y),Fe(Y),Be(Y),Sr(Y);const A=[],V=z(a.value),oe=K(i.value);A.push([`${V} - ${oe} \u7D9C\u5408\u7D71\u8A08`]),A.push([]);const ge=["\u65E5\u671F","\u7248\u8DEF\u547D\u4E2D","\u5C3E\u6578\u547D\u4E2D","\u7E3D\u547D\u4E2D","\u547D\u4E2D\u7387"];A.push(ge),x.value.forEach(Q=>{const de=Q.ballFollowResults?Pe(ze(Q.ballFollowResults),Q.actualNumbers||[]).length:0,Le=Q.tailResults?Pe(ze(Q.tailResults),Q.actualNumbers||[]).length:0,ve=de+Le,pe=ve>0?(ve/((Q.actualNumbers||[]).length*2)*100).toFixed(2)+"%":"0%";A.push([Q.date,de,Le,ve,pe])});const Te=cr.aoa_to_sheet(A);B(Te,A.length),cr.book_append_sheet(Y,Te,"\u7D9C\u5408\u7D71\u8A08")};return os(()=>{s.value=new Date().toISOString().split("T")[0]}),(Y,A)=>(Jr(),ln(ds,{class:"justify-center"},{default:Zr(()=>[Se(D0,null,{default:Zr(()=>[Se(Xn,null,{default:Zr(()=>[A[45]||(A[45]=te("div",{class:"text-h4 text-center q-mb-lg"},"\u5206\u6790\u5831\u8868",-1)),te("div",ux,[te("div",cx,[A[26]||(A[26]=te("div",{class:"text-h6"},"\u5206\u6790\u65B9\u6CD5",-1)),te("div",hx,[Se(be,{outlined:"",dense:"",modelValue:a.value,"onUpdate:modelValue":A[0]||(A[0]=V=>a.value=V),options:r,"map-options":"","emit-value":"",class:"text-h6"},null,8,["modelValue"])])])]),te("div",xx,[te("div",dx,[A[27]||(A[27]=te("div",{class:"text-h6 text-weight-bold"},"\u5F69\u7A2E\u9078\u64C7",-1)),te("div",px,[Se(be,{outlined:"",dense:"",modelValue:i.value,"onUpdate:modelValue":[A[1]||(A[1]=V=>i.value=V),ie],options:n,"emit-value":"","map-options":""},null,8,["modelValue"])])]),te("div",mx,[A[28]||(A[28]=te("div",{class:"text-h6 text-weight-bold"},"\u53C3\u8003\u65E5\u671F",-1)),te("div",vx,[Se(us,{outlined:"",dense:"",modelValue:s.value,"onUpdate:modelValue":A[2]||(A[2]=V=>s.value=V),type:"date",mask:"YYYY/MM/DD",max:_.value,class:"text-h6"},null,8,["modelValue","max"])])])]),a.value?(Jr(),on("div",gx,[te("div",_x,[A[44]||(A[44]=te("div",{class:"text-h6 text-weight-bold q-mb-md"},"\u53C3\u6578\u8A2D\u5B9A",-1)),a.value==="ball-follow"?(Jr(),on(zn,{key:0},[te("div",Ex,[A[29]||(A[29]=te("div",{class:"col-12 text-h6"},"\u62D6\u724C\u7D44\u5408",-1)),te("div",Tx,[Se(be,{outlined:"",dense:"",modelValue:O.value.num1,"onUpdate:modelValue":A[3]||(A[3]=V=>O.value.num1=V),options:q,"emit-value":"","map-options":""},null,8,["modelValue"])]),te("div",wx,[Se(be,{outlined:"",dense:"",modelValue:O.value.num2,"onUpdate:modelValue":A[4]||(A[4]=V=>O.value.num2=V),options:q,"emit-value":"","map-options":""},null,8,["modelValue"])]),te("div",Ax,[Se(be,{outlined:"",dense:"",modelValue:O.value.num3,"onUpdate:modelValue":A[5]||(A[5]=V=>O.value.num3=V),options:q,"emit-value":"","map-options":""},null,8,["modelValue"])])]),te("div",Sx,[te("div",Fx,[A[30]||(A[30]=te("div",{class:"text-h6"},"\u63A8\u7B97\u671F\u6578",-1)),te("div",Cx,[Se(be,{outlined:"",dense:"",modelValue:O.value.periodNum,"onUpdate:modelValue":A[6]||(A[6]=V=>O.value.periodNum=V),options:D.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),te("div",yx,[A[31]||(A[31]=te("div",{class:"text-h6"},"\u6700\u5927\u5340\u9593",-1)),te("div",Ox,[Se(be,{outlined:"",dense:"",modelValue:O.value.maxRange,"onUpdate:modelValue":A[7]||(A[7]=V=>O.value.maxRange=V),options:W.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),te("div",Dx,[A[32]||(A[32]=te("div",{class:"text-h6"},"\u9810\u6E2C\u671F\u6578",-1)),te("div",Nx,[Se(be,{outlined:"",dense:"",modelValue:O.value.aheadNum,"onUpdate:modelValue":A[8]||(A[8]=V=>O.value.aheadNum=V),options:M.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])])]),te("div",Rx,[te("div",Ix,[A[33]||(A[33]=te("div",{class:"text-h6"},"\u9023\u7E8C\u62D6\u51FA\u6B21\u6578",-1)),te("div",kx,[Se(be,{outlined:"",dense:"",modelValue:d.value,"onUpdate:modelValue":A[9]||(A[9]=V=>d.value=V),options:p.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])])]),te("div",Px,[A[34]||(A[34]=te("div",{class:"text-h6"},"\u7BE9\u9078\u689D\u4EF6",-1)),te("div",Lx,[Se(be,{outlined:"",dense:"",modelValue:g.value,"onUpdate:modelValue":A[10]||(A[10]=V=>g.value=V),options:h.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])])])])],64)):lt("",!0),a.value==="tail"?(Jr(),on(zn,{key:1},[te("div",Bx,[A[35]||(A[35]=te("div",{class:"col-12 text-h6"},"\u62D6\u724C\u7D44\u5408",-1)),te("div",Mx,[Se(be,{outlined:"",dense:"",modelValue:F.value.num1,"onUpdate:modelValue":A[11]||(A[11]=V=>F.value.num1=V),options:ne,"emit-value":"","map-options":""},null,8,["modelValue"])]),te("div",bx,[Se(be,{outlined:"",dense:"",modelValue:F.value.num2,"onUpdate:modelValue":A[12]||(A[12]=V=>F.value.num2=V),options:ne,"emit-value":"","map-options":""},null,8,["modelValue"])]),te("div",Ux,[Se(be,{outlined:"",dense:"",modelValue:F.value.num3,"onUpdate:modelValue":A[13]||(A[13]=V=>F.value.num3=V),options:ne,"emit-value":"","map-options":""},null,8,["modelValue"])])]),te("div",Wx,[te("div",Vx,[A[36]||(A[36]=te("div",{class:"text-h6"},"\u63A8\u7B97\u671F\u6578",-1)),te("div",Hx,[Se(be,{outlined:"",dense:"",modelValue:F.value.periodNum,"onUpdate:modelValue":A[14]||(A[14]=V=>F.value.periodNum=V),options:D.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),te("div",Gx,[A[37]||(A[37]=te("div",{class:"text-h6"},"\u6700\u5927\u5340\u9593",-1)),te("div",Xx,[Se(be,{outlined:"",dense:"",modelValue:F.value.maxRange,"onUpdate:modelValue":A[15]||(A[15]=V=>F.value.maxRange=V),options:W.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),te("div",zx,[A[38]||(A[38]=te("div",{class:"text-h6"},"\u9810\u6E2C\u671F\u6578",-1)),te("div",$x,[Se(be,{outlined:"",dense:"",modelValue:F.value.aheadNum,"onUpdate:modelValue":A[16]||(A[16]=V=>F.value.aheadNum=V),options:M.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])])])],64)):lt("",!0),a.value==="pattern"?(Jr(),on(zn,{key:2},[te("div",Kx,[A[39]||(A[39]=te("div",{class:"col-12 text-h6"},"\u7248\u8DEF\u62D6\u724C\u7D44\u5408",-1)),te("div",Yx,[Se(be,{outlined:"",dense:"",modelValue:R.value.comb1,"onUpdate:modelValue":A[17]||(A[17]=V=>R.value.comb1=V),options:q,"emit-value":"","map-options":""},null,8,["modelValue"])]),te("div",jx,[Se(be,{outlined:"",dense:"",modelValue:R.value.comb2,"onUpdate:modelValue":A[18]||(A[18]=V=>R.value.comb2=V),options:q,"emit-value":"","map-options":""},null,8,["modelValue"])]),te("div",qx,[Se(be,{outlined:"",dense:"",modelValue:R.value.comb3,"onUpdate:modelValue":A[19]||(A[19]=V=>R.value.comb3=V),options:q,"emit-value":"","map-options":""},null,8,["modelValue"])])]),te("div",Jx,[A[40]||(A[40]=te("div",{class:"col-12 text-h6"},"\u5C3E\u6578\u62D6\u724C\u7D44\u5408",-1)),te("div",Zx,[Se(be,{outlined:"",dense:"",modelValue:R.value.tailComb1,"onUpdate:modelValue":A[20]||(A[20]=V=>R.value.tailComb1=V),options:ne,"emit-value":"","map-options":""},null,8,["modelValue"])]),te("div",Qx,[Se(be,{outlined:"",dense:"",modelValue:R.value.tailComb2,"onUpdate:modelValue":A[21]||(A[21]=V=>R.value.tailComb2=V),options:ne,"emit-value":"","map-options":""},null,8,["modelValue"])]),te("div",e2,[Se(be,{outlined:"",dense:"",modelValue:R.value.tailComb3,"onUpdate:modelValue":A[22]||(A[22]=V=>R.value.tailComb3=V),options:ne,"emit-value":"","map-options":""},null,8,["modelValue"])])]),te("div",r2,[te("div",t2,[A[41]||(A[41]=te("div",{class:"text-h6"},"\u63A8\u7B97\u671F\u6578",-1)),te("div",n2,[Se(be,{outlined:"",dense:"",modelValue:R.value.period,"onUpdate:modelValue":A[23]||(A[23]=V=>R.value.period=V),options:X.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),te("div",a2,[A[42]||(A[42]=te("div",{class:"text-h6"},"\u6700\u5927\u5340\u9593",-1)),te("div",i2,[Se(be,{outlined:"",dense:"",modelValue:R.value.maxRange,"onUpdate:modelValue":A[24]||(A[24]=V=>R.value.maxRange=V),options:W.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),te("div",s2,[A[43]||(A[43]=te("div",{class:"text-h6"},"\u9810\u6E2C\u671F\u6578",-1)),te("div",f2,[Se(be,{outlined:"",dense:"",modelValue:R.value.ahead,"onUpdate:modelValue":A[25]||(A[25]=V=>R.value.ahead=V),options:M.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])])])],64)):lt("",!0)])])):lt("",!0),Se(cs,{align:"right",class:"q-my-lg q-py-none q-px-md"},{default:Zr(()=>[o.value?(Jr(),ln($n,{key:0,type:"button",label:"\u4E2D\u65B7\u8A08\u7B97",color:"negative",onClick:G,class:"text-h6 q-mr-md"})):lt("",!0),Se($n,{type:"button",label:"\u7522\u751F\u5831\u8868",color:"positive",class:"text-h6 q-px-lg q-py-sm",onClick:C,loading:o.value,disable:!N.value},{loading:Zr(()=>[Se(hs)]),_:1},8,["loading","disable"])]),_:1})]),_:1}),o.value?(Jr(),ln(Xn,{key:0},{default:Zr(()=>[te("div",l2,Kn(u.value),1),Se(xs,{rounded:"",size:"md",value:l.value,"animation-speed":50,color:"primary",class:"q-mb-xs"},null,8,["value"])]),_:1})):lt("",!0)]),_:1}),x.value.length>0?(Jr(),ln(D0,{key:0,class:"q-mt-lg"},{default:Zr(()=>[Se(Xn,null,{default:Zr(()=>[A[46]||(A[46]=te("div",{class:"text-h6 text-weight-bold q-mb-md"},"\u5206\u6790\u7D50\u679C",-1)),te("div",o2,[te("div",u2,[te("div",c2," \u5206\u6790\u65B9\u6CD5\uFF1A"+Kn(z(a.value)),1),te("div",h2," \u5F69\u7A2E\uFF1A"+Kn(K(i.value)),1)]),te("div",x2,[Se($n,{color:"primary",icon:"download",label:`\u4E0B\u8F09 ${f.value.toUpperCase()} \u6A94\u6848`,onClick:ue,disable:x.value.length===0},null,8,["label","disable"])])])]),_:1})]),_:1})):lt("",!0)]),_:1}))}});var y2=vs(d2,[["__scopeId","data-v-68f2a75d"]]);export{y2 as default};
