import{C as v,c as i,h as c,Q as q,ar as L,aF as I,at as Q,aG as E,r as b,t as S,aH as R,ab as K,R as j}from"./index.5bec8c58.js";function $(e,t,n){return n<=t?t:Math.min(n,Math.max(t,e))}function D(e,t,n){if(n<=t)return t;const l=n-t+1;let u=t+(e-t)%l;return u<t&&(u=l+u),u===0?0:u}function F(e,t=2,n="0"){if(e==null)return e;const l=""+e;return l.length>=t?l:new Array(t-l.length+1).join(n)+l}var M=v({name:"QItemSection",props:{avatar:<PERSON><PERSON><PERSON>,thumbnail:<PERSON><PERSON><PERSON>,side:<PERSON><PERSON><PERSON>,top:<PERSON><PERSON>an,noWrap:Boolean},setup(e,{slots:t}){const n=i(()=>`q-item__section column q-item__section--${e.avatar===!0||e.side===!0||e.thumbnail===!0?"side":"main"}`+(e.top===!0?" q-item__section--top justify-start":" justify-center")+(e.avatar===!0?" q-item__section--avatar":"")+(e.thumbnail===!0?" q-item__section--thumbnail":"")+(e.noWrap===!0?" q-item__section--nowrap":""));return()=>c("div",{class:n.value},q(t.default))}}),N=v({name:"QItemLabel",props:{overline:Boolean,caption:Boolean,header:Boolean,lines:[Number,String]},setup(e,{slots:t}){const n=i(()=>parseInt(e.lines,10)),l=i(()=>"q-item__label"+(e.overline===!0?" q-item__label--overline text-overline":"")+(e.caption===!0?" q-item__label--caption text-caption":"")+(e.header===!0?" q-item__label--header":"")+(n.value===1?" ellipsis":"")),u=i(()=>e.lines!==void 0&&n.value>1?{overflow:"hidden",display:"-webkit-box","-webkit-box-orient":"vertical","-webkit-line-clamp":n.value}:null);return()=>c("div",{style:u.value,class:l.value},q(t.default))}}),P=v({name:"QItem",props:{...L,...I,tag:{type:String,default:"div"},active:{type:Boolean,default:null},clickable:Boolean,dense:Boolean,insetLevel:Number,tabindex:[String,Number],focused:Boolean,manualFocus:Boolean},emits:["click","keyup"],setup(e,{slots:t,emit:n}){const{proxy:{$q:l}}=S(),u=Q(e,l),{hasLink:d,linkAttrs:k,linkClass:h,linkTag:_,navigateOnClick:y}=E(),r=b(null),o=b(null),m=i(()=>e.clickable===!0||d.value===!0||e.tag==="label"),s=i(()=>e.disable!==!0&&m.value===!0),g=i(()=>"q-item q-item-type row no-wrap"+(e.dense===!0?" q-item--dense":"")+(u.value===!0?" q-item--dark":"")+(d.value===!0&&e.active===null?h.value:e.active===!0?` q-item--active${e.activeClass!==void 0?` ${e.activeClass}`:""}`:"")+(e.disable===!0?" disabled":"")+(s.value===!0?" q-item--clickable q-link cursor-pointer "+(e.manualFocus===!0?"q-manual-focusable":"q-focusable q-hoverable")+(e.focused===!0?" q-manual-focusable--focused":""):"")),B=i(()=>{if(e.insetLevel===void 0)return null;const a=l.lang.rtl===!0?"Right":"Left";return{["padding"+a]:16+e.insetLevel*56+"px"}});function x(a){s.value===!0&&(o.value!==null&&(a.qKeyEvent!==!0&&document.activeElement===r.value?o.value.focus():document.activeElement===o.value&&r.value.focus()),y(a))}function C(a){if(s.value===!0&&R(a,[13,32])===!0){K(a),a.qKeyEvent=!0;const f=new MouseEvent("click",a);f.qKeyEvent=!0,r.value.dispatchEvent(f)}n("keyup",a)}function w(){const a=j(t.default,[]);return s.value===!0&&a.unshift(c("div",{class:"q-focus-helper",tabindex:-1,ref:o})),a}return()=>{const a={ref:r,class:g.value,style:B.value,role:"listitem",onClick:x,onKeyup:C};return s.value===!0?(a.tabindex=e.tabindex||"0",Object.assign(a,k.value)):m.value===!0&&(a["aria-disabled"]="true"),c(_.value,a,w())}}});export{P as Q,M as a,$ as b,N as c,D as n,F as p};
