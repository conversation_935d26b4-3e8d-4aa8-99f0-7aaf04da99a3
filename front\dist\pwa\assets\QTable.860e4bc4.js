import{C as H,h as o,Q,R as ft,a5 as Fe,t as z,c as b,ar as Le,at as Me,U as bt,r as A,w as M,x as Ve,b as De,f as gt,o as mt,a as je,W as St,X as Te,F as yt,bn as ht,bo as xe,bp as ge,bq as Be,br as wt,n as Ee,bs as _t,bt as be,b7 as N,a8 as ee,aD as qt}from"./index.5bec8c58.js";import{Q as Pt}from"./QList.b95bf97b.js";import{u as Ct,c as kt,d as Qe,Q as Rt}from"./QSelect.88ca58b5.js";import{Q as Tt}from"./QLinearProgress.ab26c7b0.js";var xt=H({name:"QTh",props:{props:Object,autoWidth:Boolean},emits:["click"],setup(e,{slots:l,emit:n}){const c=z(),{proxy:{$q:i}}=c,f=u=>{n("click",u)};return()=>{if(e.props===void 0)return o("th",{class:e.autoWidth===!0?"q-table--col-auto-width":"",onClick:f},Q(l.default));let u,s;const d=c.vnode.key;if(d){if(u=e.props.colsMap[d],u===void 0)return}else u=e.props.col;if(u.sortable===!0){const a=u.align==="right"?"unshift":"push";s=ft(l.default,[]),s[a](o(Fe,{class:u.__iconClass,name:i.iconSet.table.arrowUp}))}else s=Q(l.default);const S={class:u.__thClass+(e.autoWidth===!0?" q-table--col-auto-width":""),style:u.headerStyle,onClick:a=>{u.sortable===!0&&e.props.sort(u),f(a)}};return o("th",S,s)}}}),nl=H({name:"QTr",props:{props:Object,noHover:Boolean},setup(e,{slots:l}){const n=b(()=>"q-tr"+(e.props===void 0||e.props.header===!0?"":" "+e.props.__trClass)+(e.noHover===!0?" q-tr--no-hover":""));return()=>o("tr",{class:n.value},Q(l.default))}}),ol=H({name:"QTd",props:{props:Object,autoWidth:Boolean,noHover:Boolean},setup(e,{slots:l}){const n=z(),c=b(()=>"q-td"+(e.autoWidth===!0?" q-table--col-auto-width":"")+(e.noHover===!0?" q-td--no-hover":"")+" ");return()=>{if(e.props===void 0)return o("td",{class:c.value},Q(l.default));const i=n.vnode.key,f=(e.props.colsMap!==void 0?e.props.colsMap[i]:null)||e.props.col;if(f===void 0)return;const{row:u}=e.props;return o("td",{class:c.value+f.__tdClass(u),style:f.__tdStyle(u)},Q(l.default))}}});const Bt=["horizontal","vertical","cell","none"];var Ot=H({name:"QMarkupTable",props:{...Le,dense:Boolean,flat:Boolean,bordered:Boolean,square:Boolean,wrapCells:Boolean,separator:{type:String,default:"horizontal",validator:e=>Bt.includes(e)}},setup(e,{slots:l}){const n=z(),c=Me(e,n.proxy.$q),i=b(()=>`q-markup-table q-table__container q-table__card q-table--${e.separator}-separator`+(c.value===!0?" q-table--dark q-table__card--dark q-dark":"")+(e.dense===!0?" q-table--dense":"")+(e.flat===!0?" q-table--flat":"")+(e.bordered===!0?" q-table--bordered":"")+(e.square===!0?" q-table--square":"")+(e.wrapCells===!1?" q-table--no-wrap":""));return()=>o("div",{class:i.value},[o("table",{class:"q-table"},Q(l.default))])}});function Ae(e,l){return o("div",e,[o("table",{class:"q-table"},l)])}const pt={list:Pt,table:Ot},Ft=["list","table","__qtable"];var Lt=H({name:"QVirtualScroll",props:{...Ct,type:{type:String,default:"list",validator:e=>Ft.includes(e)},items:{type:Array,default:()=>[]},itemsFn:Function,itemsSize:Number,scrollTarget:bt},setup(e,{slots:l,attrs:n}){let c;const i=A(null),f=b(()=>e.itemsSize>=0&&e.itemsFn!==void 0?parseInt(e.itemsSize,10):Array.isArray(e.items)?e.items.length:0),{virtualScrollSliceRange:u,localResetVirtualScroll:s,padVirtualScroll:d,onVirtualScrollEvt:S}=kt({virtualScrollLength:f,getVirtualScrollTarget:C,getVirtualScrollEl:q}),a=b(()=>{if(f.value===0)return[];const O=(p,R)=>({index:u.value.from+R,item:p});return e.itemsFn===void 0?e.items.slice(u.value.from,u.value.to).map(O):e.itemsFn(u.value.from,u.value.to-u.value.from).map(O)}),m=b(()=>"q-virtual-scroll q-virtual-scroll"+(e.virtualScrollHorizontal===!0?"--horizontal":"--vertical")+(e.scrollTarget!==void 0?"":" scroll")),_=b(()=>e.scrollTarget!==void 0?{}:{tabindex:0});M(f,()=>{s()}),M(()=>e.scrollTarget,()=>{h(),w()});function q(){return i.value.$el||i.value}function C(){return c}function w(){c=St(q(),e.scrollTarget),c.addEventListener("scroll",S,Te.passive)}function h(){c!==void 0&&(c.removeEventListener("scroll",S,Te.passive),c=void 0)}function B(){let O=d(e.type==="list"?"div":"tbody",a.value.map(l.default));return l.before!==void 0&&(O=l.before().concat(O)),yt(l.after,O)}return Ve(()=>{s()}),De(()=>{w()}),gt(()=>{w()}),mt(()=>{h()}),je(()=>{h()}),()=>{if(l.default===void 0){console.error("QVirtualScroll: default scoped slot is required for rendering");return}return e.type==="__qtable"?Ae({ref:i,class:"q-table__middle "+m.value},B()):o(pt[e.type],{...n,ref:i,class:[n.class,m.value],..._.value},B)}}});let $=0;const Mt={fullscreen:Boolean,noRouteFullscreenExit:Boolean},Vt=["update:fullscreen","fullscreen"];function Dt(){const e=z(),{props:l,emit:n,proxy:c}=e;let i,f,u;const s=A(!1);ht(e)===!0&&M(()=>c.$route.fullPath,()=>{l.noRouteFullscreenExit!==!0&&a()}),M(()=>l.fullscreen,m=>{s.value!==m&&d()}),M(s,m=>{n("update:fullscreen",m),n("fullscreen",m)});function d(){s.value===!0?a():S()}function S(){s.value!==!0&&(s.value=!0,u=c.$el.parentNode,u.replaceChild(f,c.$el),document.body.appendChild(c.$el),$++,$===1&&document.body.classList.add("q-body--fullscreen-mixin"),i={handler:a},xe.add(i))}function a(){s.value===!0&&(i!==void 0&&(xe.remove(i),i=void 0),u.replaceChild(c.$el,f),s.value=!1,$=Math.max(0,$-1),$===0&&(document.body.classList.remove("q-body--fullscreen-mixin"),c.$el.scrollIntoView!==void 0&&setTimeout(()=>{c.$el.scrollIntoView()})))}return Ve(()=>{f=document.createElement("span")}),De(()=>{l.fullscreen===!0&&S()}),je(a),Object.assign(c,{toggleFullscreen:d,setFullscreen:S,exitFullscreen:a}),{inFullscreen:s,toggleFullscreen:d}}function jt(e,l){return new Date(e)-new Date(l)}const Et={sortMethod:Function,binaryStateSort:Boolean,columnSortOrder:{type:String,validator:e=>e==="ad"||e==="da",default:"ad"}};function Qt(e,l,n,c){const i=b(()=>{const{sortBy:s}=l.value;return s&&n.value.find(d=>d.name===s)||null}),f=b(()=>e.sortMethod!==void 0?e.sortMethod:(s,d,S)=>{const a=n.value.find(q=>q.name===d);if(a===void 0||a.field===void 0)return s;const m=S===!0?-1:1,_=typeof a.field=="function"?q=>a.field(q):q=>q[a.field];return s.sort((q,C)=>{let w=_(q),h=_(C);return a.rawSort!==void 0?a.rawSort(w,h,q,C)*m:w==null?-1*m:h==null?1*m:a.sort!==void 0?a.sort(w,h,q,C)*m:ge(w)===!0&&ge(h)===!0?(w-h)*m:Be(w)===!0&&Be(h)===!0?jt(w,h)*m:typeof w=="boolean"&&typeof h=="boolean"?(w-h)*m:([w,h]=[w,h].map(B=>(B+"").toLocaleString().toLowerCase()),w<h?-1*m:w===h?0:m)})});function u(s){let d=e.columnSortOrder;if(wt(s)===!0)s.sortOrder&&(d=s.sortOrder),s=s.name;else{const m=n.value.find(_=>_.name===s);m!==void 0&&m.sortOrder&&(d=m.sortOrder)}let{sortBy:S,descending:a}=l.value;S!==s?(S=s,a=d==="da"):e.binaryStateSort===!0?a=!a:a===!0?d==="ad"?S=null:a=!1:d==="ad"?a=!0:S=null,c({sortBy:S,descending:a,page:1})}return{columnToSort:i,computedSortMethod:f,sort:u}}const At={filter:[String,Object],filterMethod:Function};function Ht(e,l){const n=b(()=>e.filterMethod!==void 0?e.filterMethod:(c,i,f,u)=>{const s=i?i.toLowerCase():"";return c.filter(d=>f.some(S=>{const a=u(S,d)+"";return(a==="undefined"||a==="null"?"":a.toLowerCase()).indexOf(s)!==-1}))});return M(()=>e.filter,()=>{Ee(()=>{l({page:1},!0)})},{deep:!0}),{computedFilterMethod:n}}function Nt(e,l){for(const n in l)if(l[n]!==e[n])return!1;return!0}function Oe(e){return e.page<1&&(e.page=1),e.rowsPerPage!==void 0&&e.rowsPerPage<1&&(e.rowsPerPage=0),e}const $t={pagination:Object,rowsPerPageOptions:{type:Array,default:()=>[5,7,10,15,20,25,50,0]},"onUpdate:pagination":[Function,Array]};function zt(e,l){const{props:n,emit:c}=e,i=A(Object.assign({sortBy:null,descending:!1,page:1,rowsPerPage:n.rowsPerPageOptions.length!==0?n.rowsPerPageOptions[0]:5},n.pagination)),f=b(()=>{const a=n["onUpdate:pagination"]!==void 0?{...i.value,...n.pagination}:i.value;return Oe(a)}),u=b(()=>f.value.rowsNumber!==void 0);function s(a){d({pagination:a,filter:n.filter})}function d(a={}){Ee(()=>{c("request",{pagination:a.pagination||f.value,filter:a.filter||n.filter,getCellValue:l})})}function S(a,m){const _=Oe({...f.value,...a});if(Nt(f.value,_)===!0){u.value===!0&&m===!0&&s(_);return}if(u.value===!0){s(_);return}n.pagination!==void 0&&n["onUpdate:pagination"]!==void 0?c("update:pagination",_):i.value=_}return{innerPagination:i,computedPagination:f,isServerSide:u,requestServerInteraction:d,setPagination:S}}function Ut(e,l,n,c,i,f){const{props:u,emit:s,proxy:{$q:d}}=e,S=b(()=>c.value===!0?n.value.rowsNumber||0:f.value),a=b(()=>{const{page:R,rowsPerPage:T}=n.value;return(R-1)*T}),m=b(()=>{const{page:R,rowsPerPage:T}=n.value;return R*T}),_=b(()=>n.value.page===1),q=b(()=>n.value.rowsPerPage===0?1:Math.max(1,Math.ceil(S.value/n.value.rowsPerPage))),C=b(()=>m.value===0?!0:n.value.page>=q.value),w=b(()=>(u.rowsPerPageOptions.includes(l.value.rowsPerPage)?u.rowsPerPageOptions:[l.value.rowsPerPage].concat(u.rowsPerPageOptions)).map(T=>({label:T===0?d.lang.table.allRows:""+T,value:T})));M(q,(R,T)=>{if(R===T)return;const U=n.value.page;R&&!U?i({page:1}):R<U&&i({page:R})});function h(){i({page:1})}function B(){const{page:R}=n.value;R>1&&i({page:R-1})}function O(){const{page:R,rowsPerPage:T}=n.value;m.value>0&&R*T<S.value&&i({page:R+1})}function p(){i({page:q.value})}return u["onUpdate:pagination"]!==void 0&&s("update:pagination",{...n.value}),{firstRowIndex:a,lastRowIndex:m,isFirstPage:_,isLastPage:C,pagesNumber:q,computedRowsPerPageOptions:w,computedRowsNumber:S,firstPage:h,prevPage:B,nextPage:O,lastPage:p}}const It={selection:{type:String,default:"none",validator:e=>["single","multiple","none"].includes(e)},selected:{type:Array,default:()=>[]}},Wt=["update:selected","selection"];function Kt(e,l,n,c){const i=b(()=>{const C={};return e.selected.map(c.value).forEach(w=>{C[w]=!0}),C}),f=b(()=>e.selection!=="none"),u=b(()=>e.selection==="single"),s=b(()=>e.selection==="multiple"),d=b(()=>n.value.length!==0&&n.value.every(C=>i.value[c.value(C)]===!0)),S=b(()=>d.value!==!0&&n.value.some(C=>i.value[c.value(C)]===!0)),a=b(()=>e.selected.length);function m(C){return i.value[C]===!0}function _(){l("update:selected",[])}function q(C,w,h,B){l("selection",{rows:w,added:h,keys:C,evt:B});const O=u.value===!0?h===!0?w:[]:h===!0?e.selected.concat(w):e.selected.filter(p=>C.includes(c.value(p))===!1);l("update:selected",O)}return{hasSelectionMode:f,singleSelection:u,multipleSelection:s,allRowsSelected:d,someRowsSelected:S,rowsSelectedNumber:a,isRowSelected:m,clearSelection:_,updateSelection:q}}function pe(e){return Array.isArray(e)?e.slice():[]}const Gt={expanded:Array},Xt=["update:expanded"];function Jt(e,l){const n=A(pe(e.expanded));M(()=>e.expanded,u=>{n.value=pe(u)});function c(u){return n.value.includes(u)}function i(u){e.expanded!==void 0?l("update:expanded",u):n.value=u}function f(u,s){const d=n.value.slice(),S=d.indexOf(u);s===!0?S===-1&&(d.push(u),i(d)):S!==-1&&(d.splice(S,1),i(d))}return{isRowExpanded:c,setExpanded:i,updateExpanded:f}}const Yt={visibleColumns:Array};function Zt(e,l,n){const c=b(()=>{if(e.columns!==void 0)return e.columns;const s=e.rows[0];return s!==void 0?Object.keys(s).map(d=>({name:d,label:d.toUpperCase(),field:d,align:ge(s[d])?"right":"left",sortable:!0})):[]}),i=b(()=>{const{sortBy:s,descending:d}=l.value;return(e.visibleColumns!==void 0?c.value.filter(a=>a.required===!0||e.visibleColumns.includes(a.name)===!0):c.value).map(a=>{const m=a.align||"right",_=`text-${m}`;return{...a,align:m,__iconClass:`q-table__sort-icon q-table__sort-icon--${m}`,__thClass:_+(a.headerClasses!==void 0?" "+a.headerClasses:"")+(a.sortable===!0?" sortable":"")+(a.name===s?` sorted ${d===!0?"sort-desc":""}`:""),__tdStyle:a.style!==void 0?typeof a.style!="function"?()=>a.style:a.style:()=>null,__tdClass:a.classes!==void 0?typeof a.classes!="function"?()=>_+" "+a.classes:q=>_+" "+a.classes(q):()=>_}})}),f=b(()=>{const s={};return i.value.forEach(d=>{s[d.name]=d}),s}),u=b(()=>e.tableColspan!==void 0?e.tableColspan:i.value.length+(n.value===!0?1:0));return{colList:c,computedCols:i,computedColsMap:f,computedColspan:u}}const te="q-table__bottom row items-center",He={};Qe.forEach(e=>{He[e]={}});var rl=H({name:"QTable",props:{rows:{type:Array,required:!0},rowKey:{type:[String,Function],default:"id"},columns:Array,loading:Boolean,iconFirstPage:String,iconPrevPage:String,iconNextPage:String,iconLastPage:String,title:String,hideHeader:Boolean,grid:Boolean,gridHeader:Boolean,dense:Boolean,flat:Boolean,bordered:Boolean,square:Boolean,separator:{type:String,default:"horizontal",validator:e=>["horizontal","vertical","cell","none"].includes(e)},wrapCells:Boolean,virtualScroll:Boolean,virtualScrollTarget:{},...He,noDataLabel:String,noResultsLabel:String,loadingLabel:String,selectedRowsLabel:Function,rowsPerPageLabel:String,paginationLabel:Function,color:{type:String,default:"grey-8"},titleClass:[String,Array,Object],tableStyle:[String,Array,Object],tableClass:[String,Array,Object],tableHeaderStyle:[String,Array,Object],tableHeaderClass:[String,Array,Object],cardContainerClass:[String,Array,Object],cardContainerStyle:[String,Array,Object],cardStyle:[String,Array,Object],cardClass:[String,Array,Object],hideBottom:Boolean,hideSelectedBanner:Boolean,hideNoData:Boolean,hidePagination:Boolean,onRowClick:Function,onRowDblclick:Function,onRowContextmenu:Function,...Le,...Mt,...Yt,...At,...$t,...Gt,...It,...Et},emits:["request","virtualScroll",...Vt,...Xt,...Wt],setup(e,{slots:l,emit:n}){const c=z(),{proxy:{$q:i}}=c,f=Me(e,i),{inFullscreen:u,toggleFullscreen:s}=Dt(),d=b(()=>typeof e.rowKey=="function"?e.rowKey:t=>t[e.rowKey]),S=A(null),a=A(null),m=b(()=>e.grid!==!0&&e.virtualScroll===!0),_=b(()=>" q-table__card"+(f.value===!0?" q-table__card--dark q-dark":"")+(e.square===!0?" q-table--square":"")+(e.flat===!0?" q-table--flat":"")+(e.bordered===!0?" q-table--bordered":"")),q=b(()=>`q-table__container q-table--${e.separator}-separator column no-wrap`+(e.grid===!0?" q-table--grid":_.value)+(f.value===!0?" q-table--dark":"")+(e.dense===!0?" q-table--dense":"")+(e.wrapCells===!1?" q-table--no-wrap":"")+(u.value===!0?" fullscreen scroll":"")),C=b(()=>q.value+(e.loading===!0?" q-table--loading":""));M(()=>e.tableStyle+e.tableClass+e.tableHeaderStyle+e.tableHeaderClass+q.value,()=>{m.value===!0&&a.value!==null&&a.value.reset()});const{innerPagination:w,computedPagination:h,isServerSide:B,requestServerInteraction:O,setPagination:p}=zt(c,j),{computedFilterMethod:R}=Ht(e,p),{isRowExpanded:T,setExpanded:U,updateExpanded:Ne}=Jt(e,n),le=b(()=>{let t=e.rows;if(B.value===!0||t.length===0)return t;const{sortBy:r,descending:v}=h.value;return e.filter&&(t=R.value(t,e.filter,F.value,j)),We.value!==null&&(t=Ke.value(e.rows===t?t.slice():t,r,v)),t}),me=b(()=>le.value.length),V=b(()=>{let t=le.value;if(B.value===!0)return t;const{rowsPerPage:r}=h.value;return r!==0&&(W.value===0&&e.rows!==t?t.length>K.value&&(t=t.slice(0,K.value)):t=t.slice(W.value,K.value)),t}),{hasSelectionMode:D,singleSelection:$e,multipleSelection:Se,allRowsSelected:ze,someRowsSelected:ye,rowsSelectedNumber:ae,isRowSelected:ne,clearSelection:Ue,updateSelection:I}=Kt(e,n,V,d),{colList:Ie,computedCols:F,computedColsMap:he,computedColspan:we}=Zt(e,h,D),{columnToSort:We,computedSortMethod:Ke,sort:oe}=Qt(e,h,Ie,p),{firstRowIndex:W,lastRowIndex:K,isFirstPage:re,isLastPage:ie,pagesNumber:G,computedRowsPerPageOptions:Ge,computedRowsNumber:X,firstPage:ue,prevPage:se,nextPage:ce,lastPage:de}=Ut(c,w,h,B,p,me),Xe=b(()=>V.value.length===0),Je=b(()=>{const t={};return Qe.forEach(r=>{t[r]=e[r]}),t.virtualScrollItemSize===void 0&&(t.virtualScrollItemSize=e.dense===!0?28:48),t});function Ye(){m.value===!0&&a.value.reset()}function Ze(){if(e.grid===!0)return dt();const t=e.hideHeader!==!0?ke:null;if(m.value===!0){const v=l["top-row"],g=l["bottom-row"],y={default:k=>qe(k.item,l.body,k.index)};if(v!==void 0){const k=o("tbody",v({cols:F.value}));y.before=t===null?()=>k:()=>[t()].concat(k)}else t!==null&&(y.before=t);return g!==void 0&&(y.after=()=>o("tbody",g({cols:F.value}))),o(Lt,{ref:a,class:e.tableClass,style:e.tableStyle,...Je.value,scrollTarget:e.virtualScrollTarget,items:V.value,type:"__qtable",tableColspan:we.value,onVirtualScroll:tt},y)}const r=[lt()];return t!==null&&r.unshift(t()),Ae({class:["q-table__middle scroll",e.tableClass],style:e.tableStyle},r)}function et(t,r){if(a.value!==null){a.value.scrollTo(t,r);return}t=parseInt(t,10);const v=S.value.querySelector(`tbody tr:nth-of-type(${t+1})`);if(v!==null){const g=S.value.querySelector(".q-table__middle.scroll"),y=v.offsetTop-e.virtualScrollStickySizeStart,k=y<g.scrollTop?"decrease":"increase";g.scrollTop=y,n("virtualScroll",{index:t,from:0,to:w.value.rowsPerPage-1,direction:k})}}function tt(t){n("virtualScroll",t)}function _e(){return[o(Tt,{class:"q-table__linear-progress",color:e.color,dark:f.value,indeterminate:!0,trackColor:"transparent"})]}function qe(t,r,v){const g=d.value(t),y=ne(g);if(r!==void 0)return r(Pe({key:g,row:t,pageIndex:v,__trClass:y?"selected":""}));const k=l["body-cell"],P=F.value.map(x=>{const Y=l[`body-cell-${x.name}`],Z=Y!==void 0?Y:k;return Z!==void 0?Z(at({key:g,row:t,pageIndex:v,col:x})):o("td",{class:x.__tdClass(t),style:x.__tdStyle(t)},j(x,t))});if(D.value===!0){const x=l["body-selection"],Y=x!==void 0?x(nt({key:g,row:t,pageIndex:v})):[o(be,{modelValue:y,color:e.color,dark:f.value,dense:e.dense,"onUpdate:modelValue":(Z,vt)=>{I([g],[t],Z,vt)}})];P.unshift(o("td",{class:"q-table--col-auto-width"},Y))}const L={key:g,class:{selected:y}};return e.onRowClick!==void 0&&(L.class["cursor-pointer"]=!0,L.onClick=x=>{n("rowClick",x,t,v)}),e.onRowDblclick!==void 0&&(L.class["cursor-pointer"]=!0,L.onDblclick=x=>{n("rowDblclick",x,t,v)}),e.onRowContextmenu!==void 0&&(L.class["cursor-pointer"]=!0,L.onContextmenu=x=>{n("rowContextmenu",x,t,v)}),o("tr",L,P)}function lt(){const t=l.body,r=l["top-row"],v=l["bottom-row"];let g=V.value.map((y,k)=>qe(y,t,k));return r!==void 0&&(g=r({cols:F.value}).concat(g)),v!==void 0&&(g=g.concat(v({cols:F.value}))),o("tbody",g)}function Pe(t){return ve(t),t.cols=t.cols.map(r=>N({...r},"value",()=>j(r,t.row))),t}function at(t){return ve(t),N(t,"value",()=>j(t.col,t.row)),t}function nt(t){return ve(t),t}function ve(t){Object.assign(t,{cols:F.value,colsMap:he.value,sort:oe,rowIndex:W.value+t.pageIndex,color:e.color,dark:f.value,dense:e.dense}),D.value===!0&&N(t,"selected",()=>ne(t.key),(r,v)=>{I([t.key],[t.row],r,v)}),N(t,"expand",()=>T(t.key),r=>{Ne(t.key,r)})}function j(t,r){const v=typeof t.field=="function"?t.field(r):r[t.field];return t.format!==void 0?t.format(v,r):v}const E=b(()=>({pagination:h.value,pagesNumber:G.value,isFirstPage:re.value,isLastPage:ie.value,firstPage:ue,prevPage:se,nextPage:ce,lastPage:de,inFullscreen:u.value,toggleFullscreen:s}));function ot(){const t=l.top,r=l["top-left"],v=l["top-right"],g=l["top-selection"],y=D.value===!0&&g!==void 0&&ae.value>0,k="q-table__top relative-position row items-center";if(t!==void 0)return o("div",{class:k},[t(E.value)]);let P;if(y===!0?P=g(E.value).slice():(P=[],r!==void 0?P.push(o("div",{class:"q-table__control"},[r(E.value)])):e.title&&P.push(o("div",{class:"q-table__control"},[o("div",{class:["q-table__title",e.titleClass]},e.title)]))),v!==void 0&&(P.push(o("div",{class:"q-table__separator col"})),P.push(o("div",{class:"q-table__control"},[v(E.value)]))),P.length!==0)return o("div",{class:k},P)}const Ce=b(()=>ye.value===!0?null:ze.value);function ke(){const t=rt();return e.loading===!0&&l.loading===void 0&&t.push(o("tr",{class:"q-table__progress"},[o("th",{class:"relative-position",colspan:we.value},_e())])),o("thead",t)}function rt(){const t=l.header,r=l["header-cell"];if(t!==void 0)return t(fe({header:!0})).slice();const v=F.value.map(g=>{const y=l[`header-cell-${g.name}`],k=y!==void 0?y:r,P=fe({col:g});return k!==void 0?k(P):o(xt,{key:g.name,props:P},()=>g.label)});if($e.value===!0&&e.grid!==!0)v.unshift(o("th",{class:"q-table--col-auto-width"}," "));else if(Se.value===!0){const g=l["header-selection"],y=g!==void 0?g(fe({})):[o(be,{color:e.color,modelValue:Ce.value,dark:f.value,dense:e.dense,"onUpdate:modelValue":Re})];v.unshift(o("th",{class:"q-table--col-auto-width"},y))}return[o("tr",{class:e.tableHeaderClass,style:e.tableHeaderStyle},v)]}function fe(t){return Object.assign(t,{cols:F.value,sort:oe,colsMap:he.value,color:e.color,dark:f.value,dense:e.dense}),Se.value===!0&&N(t,"selected",()=>Ce.value,Re),t}function Re(t){ye.value===!0&&(t=!1),I(V.value.map(d.value),V.value,t)}const J=b(()=>{const t=[e.iconFirstPage||i.iconSet.table.firstPage,e.iconPrevPage||i.iconSet.table.prevPage,e.iconNextPage||i.iconSet.table.nextPage,e.iconLastPage||i.iconSet.table.lastPage];return i.lang.rtl===!0?t.reverse():t});function it(){if(e.hideBottom===!0)return;if(Xe.value===!0){if(e.hideNoData===!0)return;const v=e.loading===!0?e.loadingLabel||i.lang.table.loading:e.filter?e.noResultsLabel||i.lang.table.noResults:e.noDataLabel||i.lang.table.noData,g=l["no-data"],y=g!==void 0?[g({message:v,icon:i.iconSet.table.warning,filter:e.filter})]:[o(Fe,{class:"q-table__bottom-nodata-icon",name:i.iconSet.table.warning}),v];return o("div",{class:te+" q-table__bottom--nodata"},y)}const t=l.bottom;if(t!==void 0)return o("div",{class:te},[t(E.value)]);const r=e.hideSelectedBanner!==!0&&D.value===!0&&ae.value>0?[o("div",{class:"q-table__control"},[o("div",[(e.selectedRowsLabel||i.lang.table.selectedRecords)(ae.value)])])]:[];if(e.hidePagination!==!0)return o("div",{class:te+" justify-end"},st(r));if(r.length!==0)return o("div",{class:te},r)}function ut(t){p({page:1,rowsPerPage:t.value})}function st(t){let r;const{rowsPerPage:v}=h.value,g=e.paginationLabel||i.lang.table.pagination,y=l.pagination,k=e.rowsPerPageOptions.length>1;if(t.push(o("div",{class:"q-table__separator col"})),k===!0&&t.push(o("div",{class:"q-table__control"},[o("span",{class:"q-table__bottom-item"},[e.rowsPerPageLabel||i.lang.table.recordsPerPage]),o(Rt,{class:"q-table__select inline q-table__bottom-item",color:e.color,modelValue:v,options:Ge.value,displayValue:v===0?i.lang.table.allRows:v,dark:f.value,borderless:!0,dense:!0,optionsDense:!0,optionsCover:!0,"onUpdate:modelValue":ut})])),y!==void 0)r=y(E.value);else if(r=[o("span",v!==0?{class:"q-table__bottom-item"}:{},[v?g(W.value+1,Math.min(K.value,X.value),X.value):g(1,me.value,X.value)])],v!==0&&G.value>1){const P={color:e.color,round:!0,dense:!0,flat:!0};e.dense===!0&&(P.size="sm"),G.value>2&&r.push(o(ee,{key:"pgFirst",...P,icon:J.value[0],disable:re.value,onClick:ue})),r.push(o(ee,{key:"pgPrev",...P,icon:J.value[1],disable:re.value,onClick:se}),o(ee,{key:"pgNext",...P,icon:J.value[2],disable:ie.value,onClick:ce})),G.value>2&&r.push(o(ee,{key:"pgLast",...P,icon:J.value[3],disable:ie.value,onClick:de}))}return t.push(o("div",{class:"q-table__control"},r)),t}function ct(){const t=e.gridHeader===!0?[o("table",{class:"q-table"},[ke()])]:e.loading===!0&&l.loading===void 0?_e():void 0;return o("div",{class:"q-table__middle"},t)}function dt(){const t=l.item!==void 0?l.item:r=>{const v=r.cols.map(y=>o("div",{class:"q-table__grid-item-row"},[o("div",{class:"q-table__grid-item-title"},[y.label]),o("div",{class:"q-table__grid-item-value"},[y.value])]));if(D.value===!0){const y=l["body-selection"],k=y!==void 0?y(r):[o(be,{modelValue:r.selected,color:e.color,dark:f.value,dense:e.dense,"onUpdate:modelValue":(P,L)=>{I([r.key],[r.row],P,L)}})];v.unshift(o("div",{class:"q-table__grid-item-row"},k),o(qt,{dark:f.value}))}const g={class:["q-table__grid-item-card"+_.value,e.cardClass],style:e.cardStyle};return(e.onRowClick!==void 0||e.onRowDblclick!==void 0)&&(g.class[0]+=" cursor-pointer",e.onRowClick!==void 0&&(g.onClick=y=>{n("RowClick",y,r.row,r.pageIndex)}),e.onRowDblclick!==void 0&&(g.onDblclick=y=>{n("RowDblclick",y,r.row,r.pageIndex)})),o("div",{class:"q-table__grid-item col-xs-12 col-sm-6 col-md-4 col-lg-3"+(r.selected===!0?" q-table__grid-item--selected":"")},[o("div",g,v)])};return o("div",{class:["q-table__grid-content row",e.cardContainerClass],style:e.cardContainerStyle},V.value.map((r,v)=>t(Pe({key:d.value(r),row:r,pageIndex:v}))))}return Object.assign(c.proxy,{requestServerInteraction:O,setPagination:p,firstPage:ue,prevPage:se,nextPage:ce,lastPage:de,isRowSelected:ne,clearSelection:Ue,isRowExpanded:T,setExpanded:U,sort:oe,resetVirtualScroll:Ye,scrollTo:et,getCellValue:j}),_t(c.proxy,{filteredSortedRows:()=>le.value,computedRows:()=>V.value,computedRowsNumber:()=>X.value}),()=>{const t=[ot()],r={ref:S,class:C.value};return e.grid===!0?t.push(ct()):Object.assign(r,{class:[r.class,e.cardClass],style:e.cardStyle}),t.push(Ze(),it()),e.loading===!0&&l.loading!==void 0&&t.push(l.loading()),o("div",r,t)}}});export{rl as Q,nl as a,ol as b,xt as c};
